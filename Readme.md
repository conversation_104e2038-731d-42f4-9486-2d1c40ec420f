# Collaborative Quarto Editor

A real-time collaborative editor for Quarto documents with VS Code integration, user presence indicators, track changes functionality, and version history.

## Features

### Core Functionality
- **Real-time collaborative editing** - Multiple users can edit the same document simultaneously
- **User presence indicators** - See who's currently editing and where their cursors are
- **Colored cursors** - Each user has a unique color for their cursor and selections
- **Synchronized rendering** - All users see the same rendered preview in real-time
- **Track changes** - Review and accept/reject changes made by different users
- **Version history** - Browse through document versions and restore previous states

### VS Code Integration
- **VS Code extension** - Edit documents directly in VS Code while maintaining collaboration
- **Bidirectional sync** - Changes made in VS Code sync to the web editor and vice versa
- **Partial document updates** - Efficient syncing that only updates changed portions
- **File explorer integration** - Sync all files in your VS Code workspace

### Technical Features
- **SQLite database** - Reliable data persistence
- **WebSocket communication** - Real-time updates using Socket.IO
- **Quarto rendering** - Live preview of rendered documents
- **User authentication** - Secure login system
- **File management** - Create, edit, and delete documents

## Architecture

The system consists of three main components:

1. **Web Server** (`server.js`) - Node.js/Express server with Socket.IO for real-time communication
2. **Web Client** (`public/`) - Browser-based editor using CodeMirror 6
3. **VS Code Extension** (`vscode-extension/`) - Extension for VS Code integration

### Database Schema
- **Users** - User accounts and authentication
- **Documents** - Document content and metadata
- **Document History** - Version tracking and change history
- **Pending Changes** - Track changes awaiting review

## Installation

### Prerequisites
- Node.js (v14 or higher)
- VS Code (for extension features)
- Quarto CLI (for document rendering)

### Server Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd collaborative-quarto-editor
```

2. Install dependencies:
```bash
npm install
```

3. Initialize the database:
```bash
node migrate-to-sqlite.js
```

4. Start the server:
```bash
npm start
```

The server will start on `http://localhost:3000`.

### VS Code Extension Setup

1. Navigate to the extension directory:
```bash
cd vscode-extension/collaborative-quarto
```

2. Install the extension:
```bash
./install.sh
```

Or manually install the `.vsix` file:
```bash
code --install-extension collaborative-quarto-0.1.0.vsix
```

## Usage

### Web Interface

1. Open your browser and navigate to `http://localhost:3000`
2. Register a new account or login with existing credentials
3. Create a new document or select an existing one
4. Start editing - changes are automatically saved and synchronized

### VS Code Integration

1. Open VS Code and ensure the Collaborative Quarto extension is installed
2. Open the Command Palette (`Ctrl+Shift+P` or `Cmd+Shift+P`)
3. Run "Connect to Collaborative Quarto Server"
4. Enter your credentials
5. Select a document to edit
6. Edit the document in VS Code - changes sync automatically

### Track Changes

1. Enable track changes mode using the toggle button
2. Make edits - they will be highlighted as pending changes
3. Use "Show Pending Changes" to review all changes
4. Accept or reject individual changes

### Version History

1. Click "Show Version History" to see all document versions
2. Click on any version to view its content
3. Restore a previous version if needed

## Configuration

### Server Configuration

The server can be configured by modifying `server.js`:

- **Port**: Change the port in the `server.listen()` call
- **Database**: SQLite database is stored in `data/quarto.db`
- **File storage**: Documents are stored in the `documents/` directory

### VS Code Extension Configuration

Configure the extension in VS Code settings:

- `collaborative-quarto.serverUrl`: URL of the server (default: `http://localhost:3000`)

## API Endpoints

### Authentication
- `POST /api/register` - Register a new user
- `POST /api/login` - Login user
- `POST /api/logout` - Logout user

### Documents
- `GET /api/documents` - List all documents
- `POST /api/documents` - Create a new document
- `GET /api/documents/:id` - Get a specific document
- `DELETE /api/documents/:id` - Delete a document
- `POST /api/documents/:id/render` - Render document to HTML

### VS Code Sync
- `POST /api/vscode-sync/:id` - Sync document to VS Code

## WebSocket Events

### Client to Server
- `authenticate` - Authenticate socket connection
- `join-document` - Join a document for editing
- `leave-document` - Leave a document
- `update-content` - Send content changes
- `cursor-update` - Send cursor position
- `vscode-sync` - Request VS Code sync

### Server to Client
- `document-list` - List of available documents
- `init-document` - Initialize document content
- `content-updated` - Document content changed
- `cursor-update` - User cursor position changed
- `user-joined` - User joined document
- `user-left` - User left document
- `preview-updated` - Rendered preview updated

## Development

### Project Structure
```
collaborative-quarto-editor/
├── server.js              # Main server file
├── models/                # Database models
│   ├── database.js        # Database connection
│   ├── documentSqlite.js  # Document model
│   ├── userSqlite.js      # User model
│   └── vscodeSync.js      # VS Code sync manager
├── public/                # Web client files
│   ├── index.html         # Main editor page
│   ├── js/                # Client-side JavaScript
│   └── css/               # Stylesheets
├── vscode-extension/      # VS Code extension
│   └── collaborative-quarto/
├── documents/             # Document storage
├── vscode_sync/           # VS Code sync files
└── data/                  # Database files
```

### Running Tests

```bash
npm test
```

### Building the VS Code Extension

```bash
cd vscode-extension/collaborative-quarto
npm run package
```

## Troubleshooting

### Common Issues

1. **VS Code sync not working**
   - Ensure the VS Code extension is properly installed
   - Check that the server URL is correctly configured
   - Verify that the `code` command is available in your PATH

2. **Connection issues**
   - Check that the server is running on the correct port
   - Verify firewall settings allow connections
   - Ensure WebSocket connections are not blocked

3. **Authentication problems**
   - Clear browser cookies and try again
   - Check that the database is properly initialized
   - Verify user credentials are correct

### Debug Mode

Enable debug logging by setting the `DEBUG` environment variable:

```bash
DEBUG=* npm start
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

[Add your license information here]

## Acknowledgments

- Built with [CodeMirror 6](https://codemirror.net/) for the web editor
- Uses [Socket.IO](https://socket.io/) for real-time communication
- [Quarto](https://quarto.org/) for document rendering
- SQLite for data persistence