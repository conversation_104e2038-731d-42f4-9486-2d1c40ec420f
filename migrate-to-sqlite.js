const fs = require('fs');
const path = require('path');
const { UserManager: OldUserManager } = require('./models/user');
const { DocumentManager: OldDocumentManager } = require('./models/document');
const db = require('./models/database');

async function migrateUsers() {
  console.log('Migrating users to SQLite...');
  
  const usersFilePath = path.join(__dirname, 'data', 'users.json');
  if (!fs.existsSync(usersFilePath)) {
    console.log('No users file found, skipping user migration');
    return;
  }
  
  const oldUserManager = new OldUserManager(usersFilePath);
  const userData = JSON.parse(fs.readFileSync(usersFilePath, 'utf8'));
  
  for (const user of userData) {
    try {
      // Check if user already exists in SQLite
      const existingUser = await db.get('SELECT * FROM users WHERE username = ?', [user.username]);
      if (existingUser) {
        console.log(`User ${user.username} already exists in SQLite, skipping`);
        continue;
      }
      
      // Insert user into SQLite
      await db.run(
        'INSERT INTO users (id, username, display_name, color, password_hash) VALUES (?, ?, ?, ?, ?)',
        [user.id, user.username, user.displayName, user.color, user.passwordHash]
      );
      
      console.log(`Migrated user: ${user.username}`);
    } catch (error) {
      console.error(`Error migrating user ${user.username}:`, error);
    }
  }
  
  console.log('User migration complete');
}

async function migrateDocuments() {
  console.log('Migrating documents to SQLite...');
  
  const documentsDir = path.join(__dirname, 'documents');
  if (!fs.existsSync(documentsDir)) {
    console.log('No documents directory found, skipping document migration');
    return;
  }
  
  const oldDocumentManager = new OldDocumentManager(documentsDir);
  const documents = oldDocumentManager.getAllDocuments();
  
  for (const doc of documents) {
    try {
      // Get full document with content
      const fullDoc = oldDocumentManager.getDocument(doc.id);
      if (!fullDoc) {
        console.log(`Could not find document ${doc.id}, skipping`);
        continue;
      }
      
      // Check if document already exists in SQLite
      const existingDoc = await db.get('SELECT * FROM documents WHERE id = ?', [doc.id]);
      if (existingDoc) {
        console.log(`Document ${doc.name} (${doc.id}) already exists in SQLite, skipping`);
        continue;
      }
      
      // Insert document into SQLite
      await db.run(
        'INSERT INTO documents (id, name, content) VALUES (?, ?, ?)',
        [doc.id, doc.name, fullDoc.content]
      );
      
      console.log(`Migrated document: ${doc.name} (${doc.id})`);
    } catch (error) {
      console.error(`Error migrating document ${doc.id}:`, error);
    }
  }
  
  console.log('Document migration complete');
}

async function migrate() {
  try {
    await migrateUsers();
    await migrateDocuments();
    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run migration
migrate();
