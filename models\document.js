const fs = require('fs');
const path = require('path');

class Document {
  constructor(id, name, content = '') {
    this.id = id;
    this.name = name;
    this.content = content;
    this.activeUsers = new Set();
  }
}

class DocumentManager {
  constructor(documentsDir) {
    this.documentsDir = documentsDir;
    this.documents = new Map();
    this.ensureDirectoryExists();
    this.loadDocuments();
  }

  ensureDirectoryExists() {
    if (!fs.existsSync(this.documentsDir)) {
      fs.mkdirSync(this.documentsDir, { recursive: true });
    }
  }

  loadDocuments() {
    const files = fs.readdirSync(this.documentsDir);

    files.forEach(file => {
      if (file.endsWith('.qmd')) {
        const id = path.basename(file, '.qmd');
        const name = id; // Use filename as name
        const content = fs.readFileSync(path.join(this.documentsDir, file), 'utf8');
        this.documents.set(id, new Document(id, name, content));
      }
    });

    // Create a default document if none exist
    if (this.documents.size === 0) {
      this.createDocument('Welcome', '# Welcome to Collaborative Quarto\n\nThis is a collaborative editor for Quarto documents.');
    }
  }

  getAllDocuments() {
    return Array.from(this.documents.values()).map(doc => ({
      id: doc.id,
      name: doc.name,
      activeUsers: Array.from(doc.activeUsers)
    }));
  }

  getDocument(id) {
    return this.documents.get(id);
  }

  createDocument(name, content = '') {
    const id = Date.now().toString();
    const document = new Document(id, name, content);

    // Save to filesystem
    fs.writeFileSync(path.join(this.documentsDir, `${id}.qmd`), content);

    // Add to memory
    this.documents.set(id, document);

    return document;
  }

  updateDocument(id, content) {
    const document = this.documents.get(id);

    if (document) {
      document.content = content;
      fs.writeFileSync(path.join(this.documentsDir, `${id}.qmd`), content);
      return true;
    }

    return false;
  }

  deleteDocument(id) {
    const document = this.documents.get(id);

    if (document) {
      const filePath = path.join(this.documentsDir, `${id}.qmd`);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      this.documents.delete(id);
      return true;
    }

    return false;
  }

  addUserToDocument(documentId, userId) {
    console.log(`Adding user ${userId} to document ${documentId}`);
    const document = this.documents.get(documentId);
    if (document) {
      document.activeUsers.add(userId);
      const activeUsers = Array.from(document.activeUsers);
      console.log(`Document ${documentId} now has ${activeUsers.length} active users:`, activeUsers);
      return activeUsers;
    }
    console.log(`Document ${documentId} not found when adding user ${userId}`);
    return null;
  }

  removeUserFromDocument(documentId, userId) {
    console.log(`Removing user ${userId} from document ${documentId}`);
    const document = this.documents.get(documentId);
    if (document) {
      document.activeUsers.delete(userId);
      const activeUsers = Array.from(document.activeUsers);
      console.log(`Document ${documentId} now has ${activeUsers.length} active users:`, activeUsers);
      return activeUsers;
    }
    console.log(`Document ${documentId} not found when removing user ${userId}`);
    return null;
  }
}

module.exports = { Document, DocumentManager };
