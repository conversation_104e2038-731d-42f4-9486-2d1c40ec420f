const db = require('./database');

class Document {
  constructor(id, name, content = '') {
    this.id = id;
    this.name = name;
    this.content = content;
    this.activeUsers = new Set(); // This will be populated from the database
  }
}

class DocumentManager {
  constructor() {
    this.initializeDefaultDocument();
  }

  async initializeDefaultDocument() {
    try {
      // Check if any documents exist
      const count = await db.get('SELECT COUNT(*) as count FROM documents');

      // Create a default document if none exist
      if (count.count === 0) {
        await this.createDocument(
          'Welcome',
          '# Welcome to Collaborative Quarto\n\nThis is a collaborative editor for Quarto documents.'
        );
      }
    } catch (error) {
      console.error('Error initializing default document:', error);
    }
  }

  async getAllDocuments(projectId = null) {
    try {
      let query = `
        SELECT d.id, d.name, d.folder_id, d.project_id, d.created_by, d.created_at, d.updated_at,
               u.display_name as creator_name
        FROM documents d
        LEFT JOIN users u ON d.created_by = u.id
      `;
      let params = [];

      if (projectId) {
        query += ' WHERE d.project_id = ?';
        params.push(projectId);
      }

      query += ' ORDER BY d.name';

      const documents = await db.all(query, params);

      // For each document, get its active users
      const result = [];

      for (const doc of documents) {
        const activeUsers = await this.getActiveUsers(doc.id);
        result.push({
          id: doc.id,
          name: doc.name,
          folderId: doc.folder_id,
          projectId: doc.project_id,
          createdBy: doc.created_by,
          createdAt: doc.created_at,
          updatedAt: doc.updated_at,
          creatorName: doc.creator_name,
          activeUsers
        });
      }

      return result;
    } catch (error) {
      console.error('Error getting all documents:', error);
      return [];
    }
  }

  async getDocument(id) {
    try {
      const doc = await db.get(`
        SELECT d.id, d.name, d.content, d.folder_id, d.project_id, d.created_by, d.created_at, d.updated_at,
               u.display_name as creator_name
        FROM documents d
        LEFT JOIN users u ON d.created_by = u.id
        WHERE d.id = ?
      `, [id]);

      if (doc) {
        const document = new Document(doc.id, doc.name, doc.content);
        document.folderId = doc.folder_id;
        document.projectId = doc.project_id;
        document.createdBy = doc.created_by;
        document.createdAt = doc.created_at;
        document.updatedAt = doc.updated_at;
        document.creatorName = doc.creator_name;

        // Get active users for this document
        const activeUsers = await this.getActiveUsers(id);
        document.activeUsers = new Set(activeUsers);

        return document;
      }

      return null;
    } catch (error) {
      console.error('Error getting document:', error);
      return null;
    }
  }

  async createDocument(name, content = '', projectId = null, folderId = null, createdBy = 'system') {
    try {
      const id = Date.now().toString();
      const now = Date.now();

      // If no project ID provided, get the default project
      if (!projectId) {
        const defaultProject = await db.get('SELECT id FROM projects ORDER BY created_at LIMIT 1');
        projectId = defaultProject ? defaultProject.id : 'default';
      }

      await db.run(
        'INSERT INTO documents (id, name, content, folder_id, project_id, created_by, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [id, name, content, folderId, projectId, createdBy, now, now]
      );

      const document = new Document(id, name, content);
      document.folderId = folderId;
      document.projectId = projectId;
      document.createdBy = createdBy;
      document.createdAt = now;
      document.updatedAt = now;

      return document;
    } catch (error) {
      console.error('Error creating document:', error);
      return null;
    }
  }

  async updateDocument(id, content, userId = null) {
    try {
      // First, save the current version to document_versions table
      const currentDoc = await this.getDocument(id);
      if (currentDoc) {
        // Only save a version if content has changed
        if (currentDoc.content !== content) {
          await db.run(
            'INSERT INTO document_versions (document_id, content, user_id, created_at) VALUES (?, ?, ?, ?)',
            [id, currentDoc.content, userId, Date.now()]
          );
        }
      }

      // Then update the document with new content and timestamp
      const result = await db.run(
        'UPDATE documents SET content = ?, updated_at = ? WHERE id = ?',
        [content, Date.now(), id]
      );

      return result.changes > 0;
    } catch (error) {
      console.error('Error updating document:', error);
      return false;
    }
  }

  async deleteDocument(id) {
    try {
      // First delete any active users for this document
      await db.run('DELETE FROM active_users WHERE document_id = ?', [id]);

      // Then delete the document
      const result = await db.run('DELETE FROM documents WHERE id = ?', [id]);

      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting document:', error);
      return false;
    }
  }

  async getActiveUsers(documentId) {
    try {
      const activeUsers = await db.all(
        'SELECT user_id FROM active_users WHERE document_id = ?',
        [documentId]
      );

      return activeUsers.map(user => user.user_id);
    } catch (error) {
      console.error('Error getting active users:', error);
      return [];
    }
  }

  async addUserToDocument(documentId, userId) {
    try {
      console.log(`Adding user ${userId} to document ${documentId}`);

      // Check if document exists
      const document = await this.getDocument(documentId);
      if (!document) {
        console.log(`Document ${documentId} not found when adding user ${userId}`);
        return null;
      }

      // Add user to active_users table (ignore if already exists)
      await db.run(
        'INSERT OR IGNORE INTO active_users (document_id, user_id) VALUES (?, ?)',
        [documentId, userId]
      );

      // Get updated list of active users
      const activeUsers = await this.getActiveUsers(documentId);
      console.log(`Document ${documentId} now has ${activeUsers.length} active users:`, activeUsers);

      return activeUsers;
    } catch (error) {
      console.error('Error adding user to document:', error);
      return null;
    }
  }

  async removeUserFromDocument(documentId, userId) {
    try {
      console.log(`Removing user ${userId} from document ${documentId}`);

      // Remove user from active_users table
      await db.run(
        'DELETE FROM active_users WHERE document_id = ? AND user_id = ?',
        [documentId, userId]
      );

      // Get updated list of active users
      const activeUsers = await this.getActiveUsers(documentId);
      console.log(`Document ${documentId} now has ${activeUsers.length} active users:`, activeUsers);

      return activeUsers;
    } catch (error) {
      console.error('Error removing user from document:', error);
      return null;
    }
  }

  async getDocumentVersions(documentId) {
    try {
      // Get all versions for this document, ordered by creation time (newest first)
      const versions = await db.all(
        `SELECT dv.*, u.display_name as user_name
         FROM document_versions dv
         LEFT JOIN users u ON dv.user_id = u.id
         WHERE dv.document_id = ?
         ORDER BY dv.created_at DESC`,
        [documentId]
      );

      // Format the versions for display
      return versions.map(version => ({
        id: version.id,
        documentId: version.document_id,
        content: version.content,
        userId: version.user_id,
        userName: version.user_name || 'Unknown User',
        createdAt: new Date(version.created_at).toLocaleString(),
        timestamp: version.created_at,
        versionName: version.version_name || `Version ${version.id}`
      }));
    } catch (error) {
      console.error('Error getting document versions:', error);
      return [];
    }
  }

  async getDocumentVersion(versionId) {
    try {
      const version = await db.get(
        'SELECT * FROM document_versions WHERE id = ?',
        [versionId]
      );

      if (!version) {
        return null;
      }

      return {
        id: version.id,
        documentId: version.document_id,
        content: version.content,
        userId: version.user_id,
        createdAt: version.created_at,
        versionName: version.version_name
      };
    } catch (error) {
      console.error('Error getting document version:', error);
      return null;
    }
  }

  async restoreDocumentVersion(versionId, userId) {
    try {
      // Get the version to restore
      const version = await this.getDocumentVersion(versionId);
      if (!version) {
        return false;
      }

      // Update the document with the version's content
      const success = await this.updateDocument(version.documentId, version.content, userId);

      return success;
    } catch (error) {
      console.error('Error restoring document version:', error);
      return false;
    }
  }

  async saveNamedVersion(documentId, versionName, userId) {
    try {
      const document = await this.getDocument(documentId);
      if (!document) {
        return false;
      }

      // Save current content as a named version
      await db.run(
        'INSERT INTO document_versions (document_id, content, user_id, created_at, version_name) VALUES (?, ?, ?, ?, ?)',
        [documentId, document.content, userId, Date.now(), versionName]
      );

      return true;
    } catch (error) {
      console.error('Error saving named version:', error);
      return false;
    }
  }

  // Track changes methods
  async addPendingChange(documentId, userId, fromPos, toPos, insertedText) {
    try {
      // Validate inputs
      if (fromPos === undefined || toPos === undefined) {
        console.error('Invalid positions for pending change:', { fromPos, toPos });
        return null;
      }

      // Ensure fromPos <= toPos
      const normalizedFromPos = Math.min(fromPos, toPos);
      const normalizedToPos = Math.max(fromPos, toPos);

      // Check if there's a similar change we can merge with
      const similarChanges = await db.all(
        `SELECT * FROM pending_changes
         WHERE document_id = ? AND user_id = ? AND status = 'pending'
         AND (ABS(to_pos - ?) <= 10 OR ABS(from_pos - ?) <= 10 OR
              (from_pos <= ? AND to_pos >= ?))
         ORDER BY created_at DESC LIMIT 1`,
        [documentId, userId, normalizedFromPos, normalizedToPos, normalizedToPos, normalizedFromPos]
      );

      if (similarChanges && similarChanges.length > 0) {
        const existingChange = similarChanges[0];
        console.log('Found similar change to merge with:', existingChange.id);

        // Merge the changes
        const newFromPos = Math.min(existingChange.from_pos, normalizedFromPos);
        const newToPos = Math.max(existingChange.to_pos, normalizedToPos);

        // Combine the inserted text intelligently
        let newInsertedText = existingChange.inserted_text;
        if (insertedText && insertedText.length > 0) {
          // If the new change is after the existing one, append
          if (normalizedFromPos >= existingChange.to_pos) {
            newInsertedText += insertedText;
          }
          // If the new change is before the existing one, prepend
          else if (normalizedToPos <= existingChange.from_pos) {
            newInsertedText = insertedText + newInsertedText;
          }
          // Otherwise, just append (simplistic approach)
          else {
            newInsertedText += insertedText;
          }
        }

        // Update the existing change
        await db.run(
          'UPDATE pending_changes SET from_pos = ?, to_pos = ?, inserted_text = ? WHERE id = ?',
          [newFromPos, newToPos, newInsertedText, existingChange.id]
        );

        return existingChange.id;
      } else {
        // No similar change found, create a new one
        const result = await db.run(
          'INSERT INTO pending_changes (document_id, user_id, from_pos, to_pos, inserted_text, created_at) VALUES (?, ?, ?, ?, ?, ?)',
          [documentId, userId, normalizedFromPos, normalizedToPos, insertedText || '', Date.now()]
        );

        return result.lastID;
      }
    } catch (error) {
      console.error('Error adding pending change:', error);
      return null;
    }
  }

  async getPendingChanges(documentId) {
    try {
      const changes = await db.all(
        `SELECT pc.*, u.display_name as user_name, u.color as user_color
         FROM pending_changes pc
         LEFT JOIN users u ON pc.user_id = u.id
         WHERE pc.document_id = ? AND pc.status = 'pending'
         ORDER BY pc.created_at ASC`,
        [documentId]
      );

      return changes.map(change => this._formatPendingChange(change));
    } catch (error) {
      console.error('Error getting pending changes:', error);
      return [];
    }
  }

  async getPendingChange(changeId) {
    try {
      const change = await db.get(
        `SELECT pc.*, u.display_name as user_name, u.color as user_color
         FROM pending_changes pc
         LEFT JOIN users u ON pc.user_id = u.id
         WHERE pc.id = ?`,
        [changeId]
      );

      if (!change) return null;

      return this._formatPendingChange(change);
    } catch (error) {
      console.error('Error getting pending change:', error);
      return null;
    }
  }

  // Helper method to format a pending change
  _formatPendingChange(change) {
    // Log the raw change for debugging
    console.log('Raw change from DB:', change);

    return {
      id: change.id,
      documentId: change.document_id,
      userId: change.user_id,
      userName: change.user_name || 'Unknown User',
      userColor: change.user_color || '#cccccc',
      fromPos: change.from_pos,
      toPos: change.to_pos,
      insertedText: change.inserted_text,
      createdAt: new Date(change.created_at).toLocaleString(),
      timestamp: change.created_at,
      status: change.status
    };
  }

  async acceptChange(changeId, userId) {
    try {
      // Get the change details
      const change = await db.get('SELECT * FROM pending_changes WHERE id = ?', [changeId]);
      if (!change) {
        return { success: false, error: 'Change not found' };
      }

      // Get the current document content
      const document = await this.getDocument(change.document_id);
      if (!document) {
        return { success: false, error: 'Document not found' };
      }

      // Apply the change to the document content
      const content = document.content;
      const newContent = content.substring(0, change.from_pos) +
                        change.inserted_text +
                        content.substring(change.to_pos);

      // Update the document with the new content
      await this.updateDocument(change.document_id, newContent, userId);

      // Mark the change as accepted
      await db.run(
        'UPDATE pending_changes SET status = ? WHERE id = ?',
        ['accepted', changeId]
      );

      return {
        success: true,
        documentId: change.document_id,
        newContent
      };
    } catch (error) {
      console.error('Error accepting change:', error);
      return { success: false, error: error.message };
    }
  }

  async rejectChange(changeId) {
    try {
      // Mark the change as rejected
      const result = await db.run(
        'UPDATE pending_changes SET status = ? WHERE id = ?',
        ['rejected', changeId]
      );

      if (result.changes > 0) {
        // Get the change to return the document ID
        const change = await db.get('SELECT document_id FROM pending_changes WHERE id = ?', [changeId]);
        return {
          success: true,
          documentId: change ? change.document_id : null
        };
      }

      return { success: false, error: 'Change not found' };
    } catch (error) {
      console.error('Error rejecting change:', error);
      return { success: false, error: error.message };
    }
  }

  async getDocumentWithPendingChanges(documentId) {
    try {
      const document = await this.getDocument(documentId);
      if (!document) {
        return null;
      }

      const pendingChanges = await this.getPendingChanges(documentId);

      return {
        ...document,
        pendingChanges
      };
    } catch (error) {
      console.error('Error getting document with pending changes:', error);
      return null;
    }
  }
}

module.exports = { Document, DocumentManager };
