const db = require('./database');

class Project {
  constructor(id, name, description, createdBy, createdAt, isActive = false) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.createdBy = createdBy;
    this.createdAt = createdAt;
    this.isActive = isActive;
  }
}

class Folder {
  constructor(id, name, parentId, projectId, createdBy, createdAt) {
    this.id = id;
    this.name = name;
    this.parentId = parentId;
    this.projectId = projectId;
    this.createdBy = createdBy;
    this.createdAt = createdAt;
  }
}

class ProjectManager {
  constructor() {
    this.initializeDefaultProject();
  }

  async initializeDefaultProject() {
    try {
      // Check if any projects exist
      const count = await db.get('SELECT COUNT(*) as count FROM projects');

      // Create a default project if none exist
      if (count.count === 0) {
        await this.createProject(
          'Default Project',
          'Default project for collaborative editing',
          'system' // System user
        );
      }
    } catch (error) {
      console.error('Error initializing default project:', error);
    }
  }

  // Project methods
  // Get all projects for a user (owned or member)
  async getAllProjects(userId = null) {
    try {
      if (!userId) {
        // If no user specified, return all projects (admin view)
        const query = `
          SELECT p.*, u.display_name as creator_name
          FROM projects p
          LEFT JOIN users u ON p.created_by = u.id
          ORDER BY p.created_at DESC
        `;
        const projects = await db.all(query);
        return projects.map(
          (p) =>
            new Project(
              p.id,
              p.name,
              p.description,
              p.created_by,
              p.created_at,
              p.is_active === 1
            )
        );
      }

      // Get projects where user is owner or member
      const query = `
        SELECT DISTINCT p.*, u.display_name as creator_name,
               CASE WHEN p.created_by = ? THEN 'owner' ELSE 'member' END as user_role
        FROM projects p
        LEFT JOIN users u ON p.created_by = u.id
        LEFT JOIN project_members pm ON p.id = pm.project_id
        WHERE p.created_by = ? OR pm.user_id = ?
        ORDER BY p.created_at DESC
      `;

      const projects = await db.all(query, [userId, userId, userId]);

      return projects.map((p) => {
        const project = new Project(
          p.id,
          p.name,
          p.description,
          p.created_by,
          p.created_at,
          p.is_active === 1
        );
        project.userRole = p.user_role;
        return project;
      });
    } catch (error) {
      console.error('Error getting all projects:', error);
      return [];
    }
  }

  async getProject(id) {
    try {
      const project = await db.get('SELECT * FROM projects WHERE id = ?', [id]);

      if (project) {
        return new Project(
          project.id,
          project.name,
          project.description,
          project.created_by,
          project.created_at,
          project.is_active === 1
        );
      }

      return null;
    } catch (error) {
      console.error('Error getting project:', error);
      return null;
    }
  }

  async createProject(name, description, createdBy) {
    try {
      const id = Date.now().toString();
      const createdAt = Date.now();

      await db.run(
        'INSERT INTO projects (id, name, description, created_by, created_at) VALUES (?, ?, ?, ?, ?)',
        [id, name, description, createdBy, createdAt]
      );

      return new Project(id, name, description, createdBy, createdAt);
    } catch (error) {
      console.error('Error creating project:', error);
      return null;
    }
  }

  async updateProject(id, name, description) {
    try {
      const result = await db.run(
        'UPDATE projects SET name = ?, description = ? WHERE id = ?',
        [name, description, id]
      );

      return result.changes > 0;
    } catch (error) {
      console.error('Error updating project:', error);
      return false;
    }
  }

  async deleteProject(id) {
    try {
      // Check if project has documents or folders
      const docCount = await db.get(
        'SELECT COUNT(*) as count FROM documents WHERE project_id = ?',
        [id]
      );
      const folderCount = await db.get(
        'SELECT COUNT(*) as count FROM folders WHERE project_id = ?',
        [id]
      );

      if (docCount.count > 0 || folderCount.count > 0) {
        return {
          success: false,
          error: 'Cannot delete project with existing documents or folders',
        };
      }

      const result = await db.run('DELETE FROM projects WHERE id = ?', [id]);
      return { success: result.changes > 0 };
    } catch (error) {
      console.error('Error deleting project:', error);
      return { success: false, error: error.message };
    }
  }

  async setActiveProject(projectId, userId) {
    try {
      // First check if user has access to this project
      const hasAccess = await this.hasProjectAccess(projectId, userId);
      if (!hasAccess) {
        console.log(
          `User ${userId} does not have access to project ${projectId}`
        );
        return false;
      }

      // Update or insert the active project for this user
      await db.run(
        'INSERT OR REPLACE INTO user_active_projects (user_id, project_id, updated_at) VALUES (?, ?, ?)',
        [userId, projectId, Date.now()]
      );

      return true;
    } catch (error) {
      console.error('Error setting active project:', error);
      return false;
    }
  }

  async getActiveProject(userId) {
    try {
      const result = await db.get(
        `SELECT p.* FROM projects p
         JOIN user_active_projects uap ON p.id = uap.project_id
         WHERE uap.user_id = ?`,
        [userId]
      );

      if (result) {
        return new Project(
          result.id,
          result.name,
          result.description,
          result.created_by,
          result.created_at,
          true
        );
      }

      return null;
    } catch (error) {
      console.error('Error getting active project:', error);
      return null;
    }
  }

  // Folder methods
  async getFoldersInProject(projectId, parentId = null) {
    try {
      const folders = await db.all(
        `SELECT f.*, u.display_name as creator_name
         FROM folders f
         LEFT JOIN users u ON f.created_by = u.id
         WHERE f.project_id = ? AND f.parent_id ${parentId ? '= ?' : 'IS NULL'}
         ORDER BY f.name`,
        parentId ? [projectId, parentId] : [projectId]
      );

      return folders.map(
        (f) =>
          new Folder(
            f.id,
            f.name,
            f.parent_id,
            f.project_id,
            f.created_by,
            f.created_at
          )
      );
    } catch (error) {
      console.error('Error getting folders in project:', error);
      return [];
    }
  }

  async getFolder(id) {
    try {
      const folder = await db.get('SELECT * FROM folders WHERE id = ?', [id]);

      if (folder) {
        return new Folder(
          folder.id,
          folder.name,
          folder.parent_id,
          folder.project_id,
          folder.created_by,
          folder.created_at
        );
      }

      return null;
    } catch (error) {
      console.error('Error getting folder:', error);
      return null;
    }
  }

  async createFolder(name, projectId, createdBy, parentId = null) {
    try {
      const id = Date.now().toString();
      const createdAt = Date.now();

      await db.run(
        'INSERT INTO folders (id, name, parent_id, project_id, created_by, created_at) VALUES (?, ?, ?, ?, ?, ?)',
        [id, name, parentId, projectId, createdBy, createdAt]
      );

      return new Folder(id, name, parentId, projectId, createdBy, createdAt);
    } catch (error) {
      console.error('Error creating folder:', error);
      return null;
    }
  }

  async updateFolder(id, name) {
    try {
      const result = await db.run('UPDATE folders SET name = ? WHERE id = ?', [
        name,
        id,
      ]);

      return result.changes > 0;
    } catch (error) {
      console.error('Error updating folder:', error);
      return false;
    }
  }

  async deleteFolder(id) {
    try {
      // Check if folder has subfolders or documents
      const subfolderCount = await db.get(
        'SELECT COUNT(*) as count FROM folders WHERE parent_id = ?',
        [id]
      );
      const docCount = await db.get(
        'SELECT COUNT(*) as count FROM documents WHERE folder_id = ?',
        [id]
      );

      if (subfolderCount.count > 0 || docCount.count > 0) {
        return {
          success: false,
          error: 'Cannot delete folder with existing subfolders or documents',
        };
      }

      const result = await db.run('DELETE FROM folders WHERE id = ?', [id]);
      return { success: result.changes > 0 };
    } catch (error) {
      console.error('Error deleting folder:', error);
      return { success: false, error: error.message };
    }
  }

  async getFolderPath(folderId) {
    try {
      const path = [];
      let currentId = folderId;

      while (currentId) {
        const folder = await this.getFolder(currentId);
        if (!folder) break;

        path.unshift(folder);
        currentId = folder.parentId;
      }

      return path;
    } catch (error) {
      console.error('Error getting folder path:', error);
      return [];
    }
  }

  // Get project structure (folders and documents)
  async getProjectStructure(projectId) {
    try {
      const folders = await this.getFoldersInProject(projectId);
      const documents = await db.all(
        `SELECT d.*, u.display_name as creator_name
         FROM documents d
         LEFT JOIN users u ON d.created_by = u.id
         WHERE d.project_id = ?
         ORDER BY d.folder_id, d.name`,
        [projectId]
      );

      return {
        folders,
        documents: documents.map((d) => ({
          id: d.id,
          name: d.name,
          folderId: d.folder_id,
          projectId: d.project_id,
          createdBy: d.created_by,
          createdAt: d.created_at,
          updatedAt: d.updated_at,
        })),
      };
    } catch (error) {
      console.error('Error getting project structure:', error);
      return { folders: [], documents: [] };
    }
  }

  // Project collaboration methods

  // Invite a user to a project
  async inviteUserToProject(projectId, invitedBy, invitedUsername) {
    try {
      // Check if the inviting user has permission (is owner or admin)
      const project = await this.getProject(projectId);
      if (!project || project.createdBy !== invitedBy) {
        return { success: false, error: 'Permission denied' };
      }

      // Find the user to invite
      const invitedUser = await db.get(
        'SELECT id, username FROM users WHERE username = ?',
        [invitedUsername]
      );
      if (!invitedUser) {
        return { success: false, error: 'User not found' };
      }

      // Check if user is already a member
      const existingMember = await db.get(
        'SELECT id FROM project_members WHERE project_id = ? AND user_id = ?',
        [projectId, invitedUser.id]
      );
      if (existingMember) {
        return {
          success: false,
          error: 'User is already a member of this project',
        };
      }

      // Check if invitation already exists
      const existingInvitation = await db.get(
        'SELECT id FROM project_invitations WHERE project_id = ? AND invited_user = ? AND status = "pending"',
        [projectId, invitedUser.id]
      );
      if (existingInvitation) {
        return {
          success: false,
          error: 'Invitation already sent to this user',
        };
      }

      // Create invitation
      const invitationId = Date.now().toString();
      await db.run(
        'INSERT INTO project_invitations (id, project_id, invited_by, invited_user, created_at) VALUES (?, ?, ?, ?, ?)',
        [invitationId, projectId, invitedBy, invitedUser.id, Date.now()]
      );

      return { success: true, invitationId };
    } catch (error) {
      console.error('Error inviting user to project:', error);
      return { success: false, error: 'Failed to send invitation' };
    }
  }

  // Get pending invitations for a user
  async getPendingInvitations(userId) {
    try {
      const invitations = await db.all(
        `
        SELECT i.id, i.project_id, i.created_at,
               p.name as project_name, p.description as project_description,
               u.display_name as invited_by_name
        FROM project_invitations i
        JOIN projects p ON i.project_id = p.id
        JOIN users u ON i.invited_by = u.id
        WHERE i.invited_user = ? AND i.status = 'pending'
        ORDER BY i.created_at DESC
      `,
        [userId]
      );

      return invitations;
    } catch (error) {
      console.error('Error getting pending invitations:', error);
      return [];
    }
  }

  // Accept a project invitation
  async acceptInvitation(invitationId, userId) {
    try {
      // Get the invitation
      const invitation = await db.get(
        'SELECT * FROM project_invitations WHERE id = ? AND invited_user = ? AND status = "pending"',
        [invitationId, userId]
      );

      if (!invitation) {
        return {
          success: false,
          error: 'Invitation not found or already processed',
        };
      }

      // Add user to project members
      const memberId = Date.now().toString();
      await db.run(
        'INSERT INTO project_members (id, project_id, user_id, joined_at) VALUES (?, ?, ?, ?)',
        [memberId, invitation.project_id, userId, Date.now()]
      );

      // Update invitation status
      await db.run(
        'UPDATE project_invitations SET status = "accepted", responded_at = ? WHERE id = ?',
        [Date.now(), invitationId]
      );

      return { success: true };
    } catch (error) {
      console.error('Error accepting invitation:', error);
      return { success: false, error: 'Failed to accept invitation' };
    }
  }

  // Decline a project invitation
  async declineInvitation(invitationId, userId) {
    try {
      const result = await db.run(
        'UPDATE project_invitations SET status = "declined", responded_at = ? WHERE id = ? AND invited_user = ? AND status = "pending"',
        [Date.now(), invitationId, userId]
      );

      if (result.changes === 0) {
        return {
          success: false,
          error: 'Invitation not found or already processed',
        };
      }

      return { success: true };
    } catch (error) {
      console.error('Error declining invitation:', error);
      return { success: false, error: 'Failed to decline invitation' };
    }
  }

  // Get project members
  async getProjectMembers(projectId) {
    try {
      const members = await db.all(
        `
        SELECT pm.role, pm.joined_at,
               u.id, u.username, u.display_name, u.color
        FROM project_members pm
        JOIN users u ON pm.user_id = u.id
        WHERE pm.project_id = ?
        ORDER BY pm.joined_at ASC
      `,
        [projectId]
      );

      return members;
    } catch (error) {
      console.error('Error getting project members:', error);
      return [];
    }
  }

  // Check if user has access to project
  async hasProjectAccess(projectId, userId) {
    try {
      // Check if user is the owner
      const project = await db.get(
        'SELECT created_by FROM projects WHERE id = ?',
        [projectId]
      );
      if (project && project.created_by === userId) {
        return true;
      }

      // Check if user is a member
      const member = await db.get(
        'SELECT id FROM project_members WHERE project_id = ? AND user_id = ?',
        [projectId, userId]
      );

      return !!member;
    } catch (error) {
      console.error('Error checking project access:', error);
      return false;
    }
  }

  // Remove user from project
  async removeUserFromProject(projectId, userId, removedBy) {
    try {
      // Check if the removing user has permission (is owner)
      const project = await this.getProject(projectId);
      if (!project || project.createdBy !== removedBy) {
        return { success: false, error: 'Permission denied' };
      }

      // Can't remove the owner
      if (userId === removedBy) {
        return { success: false, error: 'Cannot remove project owner' };
      }

      const result = await db.run(
        'DELETE FROM project_members WHERE project_id = ? AND user_id = ?',
        [projectId, userId]
      );

      if (result.changes === 0) {
        return {
          success: false,
          error: 'User is not a member of this project',
        };
      }

      return { success: true };
    } catch (error) {
      console.error('Error removing user from project:', error);
      return { success: false, error: 'Failed to remove user' };
    }
  }
}

module.exports = { Project, Folder, ProjectManager };
