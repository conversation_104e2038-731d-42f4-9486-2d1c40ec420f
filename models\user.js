const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class User {
  constructor(id, username, displayName, color) {
    this.id = id;
    this.username = username;
    this.displayName = displayName || username;
    this.color = color || this.generateRandomColor();
  }

  generateRandomColor() {
    // Generate a random pastel color
    const hue = Math.floor(Math.random() * 360);
    return `hsl(${hue}, 70%, 60%)`;
  }
}

class UserManager {
  constructor(usersFilePath) {
    this.usersFilePath = usersFilePath;
    this.users = new Map();
    this.sessions = new Map();
    this.ensureFileExists();
    this.loadUsers();
  }

  ensureFileExists() {
    const dir = path.dirname(this.usersFilePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    if (!fs.existsSync(this.usersFilePath)) {
      fs.writeFileSync(this.usersFilePath, JSON.stringify([]));
    }
  }

  loadUsers() {
    const data = fs.readFileSync(this.usersFilePath, 'utf8');
    const users = JSON.parse(data);
    
    users.forEach(user => {
      this.users.set(user.username, new User(user.id, user.username, user.displayName, user.color));
    });
  }

  saveUsers() {
    const users = Array.from(this.users.values());
    fs.writeFileSync(this.usersFilePath, JSON.stringify(users, null, 2));
  }

  createUser(username, password, displayName) {
    if (this.users.has(username)) {
      return null; // User already exists
    }
    
    const id = crypto.randomUUID();
    const passwordHash = this.hashPassword(password);
    const user = new User(id, username, displayName);
    
    // Store user with password hash
    const userWithPassword = {
      ...user,
      passwordHash
    };
    
    this.users.set(username, user);
    
    // Save to file (including password hash)
    const users = Array.from(this.users.values()).map(u => {
      if (u.username === username) {
        return userWithPassword;
      }
      return u;
    });
    
    fs.writeFileSync(this.usersFilePath, JSON.stringify(users, null, 2));
    
    return user;
  }

  authenticateUser(username, password) {
    const userData = this.getUserData(username);
    
    if (!userData || !userData.passwordHash) {
      return null;
    }
    
    const passwordHash = this.hashPassword(password);
    
    if (passwordHash === userData.passwordHash) {
      // Create session
      const sessionId = crypto.randomUUID();
      this.sessions.set(sessionId, username);
      
      return {
        sessionId,
        user: new User(userData.id, userData.username, userData.displayName, userData.color)
      };
    }
    
    return null;
  }

  validateSession(sessionId) {
    const username = this.sessions.get(sessionId);
    
    if (username) {
      return this.getUser(username);
    }
    
    return null;
  }

  getUser(username) {
    return this.users.get(username);
  }

  getUserData(username) {
    const data = fs.readFileSync(this.usersFilePath, 'utf8');
    const users = JSON.parse(data);
    return users.find(u => u.username === username);
  }

  hashPassword(password) {
    return crypto.createHash('sha256').update(password).digest('hex');
  }

  logout(sessionId) {
    return this.sessions.delete(sessionId);
  }

  getAllUsers() {
    return Array.from(this.users.values());
  }
}

module.exports = { User, UserManager };
