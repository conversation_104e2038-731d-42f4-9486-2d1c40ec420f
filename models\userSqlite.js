const crypto = require('crypto');
const db = require('./database');

class User {
  constructor(id, username, displayName, color) {
    this.id = id;
    this.username = username;
    this.displayName = displayName || username;
    this.color = color || this.generateRandomColor();
  }

  generateRandomColor() {
    // Generate a random pastel color
    const hue = Math.floor(Math.random() * 360);
    return `hsl(${hue}, 70%, 60%)`;
  }
}

class UserManager {
  constructor() {
    // No need for file path anymore
    this.loadUsers();
  }

  async loadUsers() {
    try {
      // We don't need to preload all users into memory
      // We'll fetch them from the database as needed
      console.log('User manager initialized with SQLite');
    } catch (error) {
      console.error('Error loading users:', error);
    }
  }

  async getAllUsers() {
    try {
      const users = await db.all('SELECT id, username, display_name, color FROM users');
      return users.map(user => new User(
        user.id, 
        user.username, 
        user.display_name, 
        user.color
      ));
    } catch (error) {
      console.error('Error getting all users:', error);
      return [];
    }
  }

  async createUser(username, password, displayName) {
    try {
      // Check if user already exists
      const existingUser = await db.get('SELECT * FROM users WHERE username = ?', [username]);
      if (existingUser) {
        return null; // User already exists
      }
      
      const id = crypto.randomUUID();
      const passwordHash = this.hashPassword(password);
      const color = new User(id, username).color; // Generate a random color
      
      await db.run(
        'INSERT INTO users (id, username, display_name, color, password_hash) VALUES (?, ?, ?, ?, ?)',
        [id, username, displayName || username, color, passwordHash]
      );
      
      return new User(id, username, displayName, color);
    } catch (error) {
      console.error('Error creating user:', error);
      return null;
    }
  }

  async authenticateUser(username, password) {
    try {
      const userData = await db.get(
        'SELECT id, username, display_name, color, password_hash FROM users WHERE username = ?',
        [username]
      );
      
      if (!userData || !userData.password_hash) {
        return null;
      }
      
      const passwordHash = this.hashPassword(password);
      
      if (passwordHash === userData.password_hash) {
        // Create session
        const sessionId = crypto.randomUUID();
        const now = Date.now();
        
        await db.run(
          'INSERT INTO sessions (session_id, username, created_at) VALUES (?, ?, ?)',
          [sessionId, username, now]
        );
        
        return {
          sessionId,
          user: new User(userData.id, userData.username, userData.display_name, userData.color)
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error authenticating user:', error);
      return null;
    }
  }

  async validateSession(sessionId) {
    try {
      const session = await db.get(
        'SELECT username FROM sessions WHERE session_id = ?',
        [sessionId]
      );
      
      if (session) {
        return await this.getUser(session.username);
      }
      
      return null;
    } catch (error) {
      console.error('Error validating session:', error);
      return null;
    }
  }

  async getUser(username) {
    try {
      const userData = await db.get(
        'SELECT id, username, display_name, color FROM users WHERE username = ?',
        [username]
      );
      
      if (userData) {
        return new User(
          userData.id, 
          userData.username, 
          userData.display_name, 
          userData.color
        );
      }
      
      return null;
    } catch (error) {
      console.error('Error getting user:', error);
      return null;
    }
  }

  async getUserById(id) {
    try {
      const userData = await db.get(
        'SELECT id, username, display_name, color FROM users WHERE id = ?',
        [id]
      );
      
      if (userData) {
        return new User(
          userData.id, 
          userData.username, 
          userData.display_name, 
          userData.color
        );
      }
      
      return null;
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }

  hashPassword(password) {
    return crypto.createHash('sha256').update(password).digest('hex');
  }

  async logout(sessionId) {
    try {
      await db.run('DELETE FROM sessions WHERE session_id = ?', [sessionId]);
      return true;
    } catch (error) {
      console.error('Error logging out:', error);
      return false;
    }
  }
}

module.exports = { User, UserManager };
