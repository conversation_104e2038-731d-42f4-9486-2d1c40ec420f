/**
 * Module for handling VS Code synchronization
 */
const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');

class VSCodeSyncManager {
  constructor(documentManager, io) {
    this.documentManager = documentManager;
    this.io = io;
    this.syncDir = path.join(__dirname, '..', 'vscode_sync');
    this.fileToDocMap = new Map(); // Maps filePath to documentId
    this.docToFileMap = new Map(); // Maps documentId to filePath
    this.watchers = new Map(); // Maps filePath to watcher
    this.isWatcherInitialized = false;

    // Create sync directory if it doesn't exist
    if (!fs.existsSync(this.syncDir)) {
      fs.mkdirSync(this.syncDir, { recursive: true });
    }

    // Initialize file watcher
    this.initializeWatcher();
  }

  /**
   * Initialize the file watcher for the sync directory
   */
  initializeWatcher() {
    try {
      console.log('Initializing VS Code sync watcher for directory:', this.syncDir);

      // Create a watcher for the sync directory
      this.watcher = chokidar.watch(this.syncDir, {
        ignored: /(^|[\/\\])\../, // Ignore dotfiles
        persistent: true,
        awaitWriteFinish: {
          stabilityThreshold: 1000, // Reduced from 2000ms for faster response
          pollInterval: 100
        },
        usePolling: false, // Use native file system events for better performance
        ignoreInitial: true, // Don't trigger events for existing files on startup
        alwaysStat: true  // Always stat the file for changes
      });

      // Handle file changes
      this.watcher.on('change', (filePath) => {
        console.log('File change detected by watcher:', filePath);
        this.handleFileChange(filePath);
      });

      // Handle file additions (for new files created in VS Code)
      this.watcher.on('add', (filePath) => {
        console.log('New file detected by watcher:', filePath);
        // Only handle .qmd files
        if (filePath.endsWith('.qmd')) {
          this.handleFileChange(filePath);
        }
      });

      this.watcher.on('ready', () => {
        console.log('VS Code sync watcher initialized and ready');
        this.isWatcherInitialized = true;

        // Log the watched paths
        const watchedPaths = this.watcher.getWatched();
        console.log('Watched paths:', JSON.stringify(watchedPaths));
      });

      this.watcher.on('error', (error) => {
        console.error('VS Code sync watcher error:', error);
      });
    } catch (error) {
      console.error('Error initializing VS Code sync watcher:', error);
    }
  }

  /**
   * Handle file changes in the sync directory
   * @param {string} filePath - Path to the changed file
   */
  async handleFileChange(filePath) {
    try {
      console.log('File changed:', filePath);

      // Skip if file doesn't exist (might have been deleted)
      if (!fs.existsSync(filePath)) {
        console.log('File no longer exists, skipping:', filePath);
        return;
      }

      // Check if this file is mapped to a document
      let documentId = this.fileToDocMap.get(filePath);
      console.log('Current file to document mappings:', [...this.fileToDocMap.entries()]);

      if (!documentId) {
        console.log('No document mapped to file:', filePath);

        // Try to find a match by filename
        const fileName = path.basename(filePath);
        console.log('Trying to match by filename:', fileName);

        // First try exact match with document ID
        if (fileName.match(/^\d+\.qmd$/)) {
          const potentialDocId = fileName.replace('.qmd', '');
          const document = await this.documentManager.getDocument(potentialDocId);
          if (document) {
            console.log('Found matching document by ID:', potentialDocId);
            documentId = potentialDocId;

            // Update the mapping
            this.fileToDocMap.set(filePath, documentId);
            this.docToFileMap.set(documentId, filePath);
          }
        }

        // If no exact match, try to find by document name
        if (!documentId) {
          const documents = await this.documentManager.getAllDocuments();
          for (const doc of documents) {
            const docFileName = `${doc.name.replace(/[^a-zA-Z0-9]/g, '_')}.qmd`;
            if (fileName === docFileName) {
              console.log('Found matching document by name:', doc.id);
              documentId = doc.id;

              // Update the mapping
              this.fileToDocMap.set(filePath, documentId);
              this.docToFileMap.set(documentId, filePath);
              break;
            }
          }
        }

        if (!documentId) {
          console.log('No matching document found for file:', filePath);
          return;
        }
      }

      await this.updateDocumentFromFile(filePath, documentId);
    } catch (error) {
      console.error('Error handling file change:', error);
    }
  }

  /**
   * Update a document from a file
   * @param {string} filePath - Path to the file
   * @param {string} documentId - ID of the document to update
   */
  async updateDocumentFromFile(filePath, documentId) {
    try {
      console.log(`Updating document ${documentId} from file ${filePath}`);

      // Read the file content
      const content = fs.readFileSync(filePath, 'utf8');
      console.log(`Read ${content.length} bytes from file`);

      // Get current document content to check if update is needed
      const currentDocument = await this.documentManager.getDocument(documentId);
      if (currentDocument && currentDocument.content === content) {
        console.log('Document content unchanged, skipping update');
        return;
      }

      // Temporarily disable file watching to prevent infinite loops
      const wasWatching = this.watcher && this.watcher.getWatched()[path.dirname(filePath)];
      if (wasWatching) {
        this.watcher.unwatch(filePath);
      }

      try {
        // Update the document in the database
        const success = await this.documentManager.updateDocument(documentId, content);

        if (success) {
          console.log('Document updated from VS Code:', documentId);

          // Notify all clients viewing this document
          this.io.to(`document:${documentId}`).emit('content-updated', {
            content,
            userId: null, // No specific user for VS Code updates
            fromVSCode: true,
            isIncrementalUpdate: false // Full content replacement from VS Code
          });
        } else {
          console.error('Failed to update document from VS Code:', documentId);
        }
      } finally {
        // Re-enable file watching after a short delay
        if (wasWatching) {
          setTimeout(() => {
            if (this.watcher) {
              this.watcher.add(filePath);
              console.log(`Re-enabled watching for ${filePath}`);
            }
          }, 1000);
        }
      }
    } catch (error) {
      console.error(`Error updating document ${documentId} from file:`, error);
    }
  }

  /**
   * Sync a document to VS Code
   * @param {string} documentId - ID of the document to sync
   * @param {object} document - Document object with name and content
   * @returns {object} - Result of the sync operation
   */
  async syncToVSCode(documentId, document) {
    try {
      // Create a file name from the document ID to ensure uniqueness
      const fileName = `${documentId}.qmd`;
      const filePath = path.join(this.syncDir, fileName);

      console.log(`Syncing document ${documentId} to VS Code at ${filePath}`);

      // Check if file already exists and has the same content
      let needsUpdate = true;
      if (fs.existsSync(filePath)) {
        const existingContent = fs.readFileSync(filePath, 'utf8');
        if (existingContent === document.content) {
          console.log('File already has the same content, skipping write');
          needsUpdate = false;
        }
      }

      // Temporarily disable file watching to prevent infinite loops
      const wasWatching = this.watcher && this.watcher.getWatched()[this.syncDir];
      if (wasWatching && needsUpdate) {
        this.watcher.unwatch(filePath);
      }

      try {
        if (needsUpdate) {
          // Write the document content to the file
          fs.writeFileSync(filePath, document.content);
          console.log(`Wrote ${document.content.length} bytes to file`);
        }

        // Update the mapping
        this.fileToDocMap.set(filePath, documentId);
        this.docToFileMap.set(documentId, filePath);

        console.log('Document synced to VS Code:', documentId, 'at', filePath);
        console.log('Updated file to document mappings:', [...this.fileToDocMap.entries()]);

        return {
          success: true,
          filePath
        };
      } finally {
        // Re-enable file watching after a short delay
        if (wasWatching && needsUpdate) {
          setTimeout(() => {
            if (this.watcher) {
              this.watcher.add(filePath);
              console.log(`Re-enabled watching for ${filePath}`);
            }
          }, 1000);
        } else if (this.watcher && !wasWatching) {
          // Add to watcher if not already watching
          this.watcher.add(filePath);
          console.log(`Added ${filePath} to watcher`);
        }
      }
    } catch (error) {
      console.error('Error syncing document to VS Code:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Register a file mapping for VS Code sync
   * @param {string} documentId - ID of the document
   * @param {string} filePath - Path to the VS Code file
   */
  registerFileMapping(documentId, filePath) {
    console.log(`Registering file mapping: ${documentId} -> ${filePath}`);
    this.fileToDocMap.set(filePath, documentId);
    this.docToFileMap.set(documentId, filePath);
    console.log('Updated file mappings:', [...this.docToFileMap.entries()]);
  }

  /**
   * Get the file path for a document
   * @param {string} documentId - ID of the document
   * @returns {string|null} - File path or null if not found
   */
  getFilePath(documentId) {
    return this.docToFileMap.get(documentId) || null;
  }

  /**
   * Update VS Code file when document is updated from web editor
   * @param {string} documentId - ID of the document
   * @param {string} content - New content
   */
  async updateVSCodeFile(documentId, content) {
    try {
      const filePath = this.getFilePath(documentId);
      if (!filePath) {
        console.log(`No VS Code file mapped for document ${documentId}`);
        return;
      }

      // Check if file exists and content is different
      if (!fs.existsSync(filePath)) {
        console.log(`VS Code file no longer exists: ${filePath}`);
        // Remove from mappings
        this.fileToDocMap.delete(filePath);
        this.docToFileMap.delete(documentId);
        return;
      }

      const existingContent = fs.readFileSync(filePath, 'utf8');
      if (existingContent === content) {
        console.log('VS Code file already has the same content, skipping update');
        return;
      }

      // Temporarily disable file watching to prevent infinite loops
      const wasWatching = this.watcher && this.watcher.getWatched()[path.dirname(filePath)];
      if (wasWatching) {
        this.watcher.unwatch(filePath);
      }

      try {
        // Update the file
        fs.writeFileSync(filePath, content);
        console.log(`Updated VS Code file ${filePath} with ${content.length} bytes`);
      } finally {
        // Re-enable file watching after a short delay
        if (wasWatching) {
          setTimeout(() => {
            if (this.watcher) {
              this.watcher.add(filePath);
              console.log(`Re-enabled watching for ${filePath}`);
            }
          }, 1000);
        }
      }
    } catch (error) {
      console.error(`Error updating VS Code file for document ${documentId}:`, error);
    }
  }

  /**
   * Get the document ID for a file
   * @param {string} filePath - Path to the file
   * @returns {string|null} - Document ID or null if not found
   */
  getDocumentId(filePath) {
    return this.fileToDocMap.get(filePath) || null;
  }

  /**
   * Check if a document is synced to VS Code
   * @param {string} documentId - ID of the document
   * @returns {boolean} - True if synced, false otherwise
   */
  isDocumentSynced(documentId) {
    return this.docToFileMap.has(documentId);
  }
}

module.exports = { VSCodeSyncManager };
