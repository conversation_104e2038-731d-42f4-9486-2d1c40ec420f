const Y = require('yjs');
const WebSocket = require('ws');

// Simple WebSocket connection handler for Yjs
function setupWSConnection(ws, req, options = {}) {
  const docName = options.docName || 'default';
  const gc = options.gc !== false;

  console.log(`Setting up Yjs WebSocket connection for document: ${docName}`);

  // This is a simplified version - in production you'd want to use the full y-websocket utils
  // For now, we'll just handle basic WebSocket communication

  ws.on('message', (message) => {
    console.log('Received Yjs message:', message.length, 'bytes');
    // Echo back for now - proper Yjs sync would handle this differently
    ws.send(message);
  });

  ws.on('close', () => {
    console.log(`Yjs WebSocket connection closed for document: ${docName}`);
  });

  ws.on('error', (error) => {
    console.error('Yjs WebSocket error:', error);
  });
}

/**
 * Yjs WebSocket server for collaborative editing
 */
class YjsServer {
  constructor(server, documentManager) {
    this.server = server;
    this.documentManager = documentManager;
    this.documents = new Map(); // Map of documentId -> Y.Doc
    this.wss = null;
    
    this.initialize();
  }

  /**
   * Initialize the Yjs WebSocket server
   */
  initialize() {
    console.log('Initializing Yjs WebSocket server');

    // Create WebSocket server
    this.wss = new WebSocket.Server({
      server: this.server,
      path: '/yjs'
    });

    // Handle WebSocket connections
    this.wss.on('connection', (ws, req) => {
      console.log('New Yjs WebSocket connection');
      
      // Extract document ID from URL
      const url = new URL(req.url, `http://${req.headers.host}`);
      const documentId = this.extractDocumentId(url.pathname);
      
      if (!documentId) {
        console.error('No document ID found in WebSocket connection');
        ws.close();
        return;
      }

      console.log(`Yjs WebSocket connection for document: ${documentId}`);

      // Get or create Yjs document
      this.getOrCreateYDoc(documentId).then(ydoc => {
        // Set up the WebSocket connection with Yjs utilities
        setupWSConnection(ws, req, {
          docName: `document-${documentId}`,
          gc: true // Enable garbage collection
        });

        // Handle connection close
        ws.on('close', () => {
          console.log(`Yjs WebSocket connection closed for document: ${documentId}`);
        });

      }).catch(error => {
        console.error('Error setting up Yjs document:', error);
        ws.close();
      });
    });

    console.log('Yjs WebSocket server initialized');
  }

  /**
   * Extract document ID from WebSocket path
   */
  extractDocumentId(pathname) {
    // Expected format: /yjs/document-{documentId}
    const match = pathname.match(/\/yjs\/document-(.+)/);
    return match ? match[1] : null;
  }

  /**
   * Get or create a Yjs document for the given document ID
   */
  async getOrCreateYDoc(documentId) {
    // Check if we already have this document in memory
    if (this.documents.has(documentId)) {
      return this.documents.get(documentId);
    }

    console.log(`Creating new Yjs document for: ${documentId}`);

    // Create new Yjs document
    const ydoc = new Y.Doc();
    const ytext = ydoc.getText('content');

    // Load initial content from database
    try {
      const document = await this.documentManager.getDocument(documentId);
      if (document && document.content) {
        console.log(`Loading initial content for document ${documentId}: ${document.content.length} characters`);
        ytext.insert(0, document.content);
      }
    } catch (error) {
      console.error(`Error loading initial content for document ${documentId}:`, error);
    }

    // Set up persistence - save changes back to database
    this.setupPersistence(ydoc, documentId);

    // Store the document
    this.documents.set(documentId, ydoc);

    return ydoc;
  }

  /**
   * Set up persistence for a Yjs document
   */
  setupPersistence(ydoc, documentId) {
    const ytext = ydoc.getText('content');
    let saveTimeout = null;

    // Debounced save function
    const debouncedSave = () => {
      if (saveTimeout) {
        clearTimeout(saveTimeout);
      }

      saveTimeout = setTimeout(async () => {
        try {
          const content = ytext.toString();
          console.log(`Saving document ${documentId} to database: ${content.length} characters`);
          
          await this.documentManager.updateDocument(documentId, content);
          console.log(`Document ${documentId} saved successfully`);
        } catch (error) {
          console.error(`Error saving document ${documentId}:`, error);
        }
        saveTimeout = null;
      }, 1000); // Save after 1 second of inactivity
    };

    // Listen for document changes
    ytext.observe(() => {
      debouncedSave();
    });

    console.log(`Persistence set up for document ${documentId}`);
  }

  /**
   * Get document content as string
   */
  async getDocumentContent(documentId) {
    const ydoc = await this.getOrCreateYDoc(documentId);
    const ytext = ydoc.getText('content');
    return ytext.toString();
  }

  /**
   * Update document content
   */
  async updateDocumentContent(documentId, content) {
    const ydoc = await this.getOrCreateYDoc(documentId);
    const ytext = ydoc.getText('content');
    
    // Replace all content
    ytext.delete(0, ytext.length);
    ytext.insert(0, content);
    
    console.log(`Updated Yjs document ${documentId} with ${content.length} characters`);
  }

  /**
   * Get statistics about active documents
   */
  getStats() {
    const stats = {
      activeDocuments: this.documents.size,
      connectedClients: this.wss ? this.wss.clients.size : 0,
      documents: []
    };

    this.documents.forEach((ydoc, documentId) => {
      const ytext = ydoc.getText('content');
      stats.documents.push({
        documentId,
        contentLength: ytext.length,
        clientCount: 0 // This would need more complex tracking
      });
    });

    return stats;
  }

  /**
   * Clean up resources for a document
   */
  destroyDocument(documentId) {
    const ydoc = this.documents.get(documentId);
    if (ydoc) {
      console.log(`Destroying Yjs document: ${documentId}`);
      ydoc.destroy();
      this.documents.delete(documentId);
    }
  }

  /**
   * Clean up all resources
   */
  destroy() {
    console.log('Destroying Yjs server');

    // Close WebSocket server
    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }

    // Destroy all documents
    this.documents.forEach((ydoc, documentId) => {
      this.destroyDocument(documentId);
    });

    this.documents.clear();
  }
}

module.exports = { YjsServer };
