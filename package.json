{"name": "collaborative-quarto-editor", "version": "1.0.0", "description": "A collaborative editor for Quarto documents", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "webpack --mode=production", "watch": "webpack --watch --mode=development"}, "dependencies": {"@codemirror/closebrackets": "^0.19.2", "@codemirror/commands": "^6.8.1", "@codemirror/gutter": "^0.19.9", "@codemirror/history": "^0.19.2", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/language": "^6.11.0", "@codemirror/matchbrackets": "^0.19.4", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.5", "chokidar": "^4.0.3", "cookie-parser": "^1.4.6", "express": "^4.17.1", "socket.io": "^4.4.1", "sqlite3": "^5.1.7", "ws": "^8.18.3", "y-codemirror.next": "^0.3.5", "y-websocket": "^3.0.0", "yjs": "^13.6.27"}, "devDependencies": {"nodemon": "^2.0.15", "webpack": "^5.99.5", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}}