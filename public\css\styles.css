* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: #333;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

header {
  padding: 1rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

button {
  padding: 0.5rem 1rem;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

button:hover {
  background-color: #45a049;
}

.btn-logout {
  background-color: #f44336;
}

.btn-logout:hover {
  background-color: #d32f2f;
}

.btn-new {
  padding: 0.25rem 0.5rem;
  font-size: 0.9rem;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Sidebar styles */
.sidebar {
  width: 250px;
  background-color: #f9f9f9;
  border-right: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #eee;
}

.sidebar-header h3 {
  font-size: 1rem;
  margin: 0;
}

.file-explorer {
  flex: 1;
  overflow: auto;
  border-bottom: 1px solid #ddd;
}

.document-list, .user-list {
  list-style: none;
  padding: 0;
}

.document-list li {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.document-list li:hover {
  background-color: #f0f0f0;
}

.document-list li.active {
  background-color: #e8f5e9;
  border-left: 3px solid #4CAF50;
}

.active-users {
  height: 200px;
  overflow: auto;
}

.user-list li {
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  transition: background-color 0.2s;
}

.user-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: white;
  margin-right: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.user-name {
  font-size: 13px;
  color: #e0e0e0;
  font-weight: 500;
}

.user-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

/* Editor section */
.editor-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-right: 1px solid #ddd;
}

.editor-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editor-container {
  flex: 1;
  overflow: auto;
}

.preview-container {
  flex: 1;
  padding: 1rem;
  overflow: auto;
  min-width: 400px;
}

.preview-container h2 {
  margin-bottom: 1rem;
}

footer {
  padding: 0.5rem 1rem;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
}

.status {
  font-size: 0.9rem;
  color: #666;
}

.cm-editor {
  height: 100%;
  overflow: auto;
}

#connection-status.connected {
  color: #4CAF50;
}

#connection-status.connecting {
  color: #ff9800;
}

#connection-status.disconnected {
  color: #f44336;
}

/* User info */
.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

#current-user {
  font-weight: 500;
}

/* Remote cursors */
.remote-cursor {
  position: relative;
  display: inline-block;
  width: 2px;
  height: 1.2em;
  vertical-align: text-bottom;
  /* Removed blinking animation for remote cursors */
}

.remote-cursor-mark {
  position: relative;
  pointer-events: none;
}

.remote-cursor-line {
  position: relative;
  pointer-events: none;
}

.remote-cursor-overlay {
  position: absolute;
  pointer-events: none;
  z-index: 1000;
}

.remote-cursor-label {
  position: absolute;
  font-size: 12px;
  color: white;
  padding: 2px 4px;
  border-radius: 3px;
  white-space: nowrap;
  pointer-events: none;
}

/* Keep the blink animation definition for other elements that might use it */
@keyframes blink {
  from, to { opacity: 1; }
  50% { opacity: 0; }
}

.cursor-label {
  position: absolute;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 2px;
  white-space: nowrap;
  color: white;
  top: -20px;
  left: 0;
  z-index: 10;
  pointer-events: none;
}

/* Modal styles */
.modal {
  display: none;
  position: fixed;
  z-index: 100;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: white;
  margin: 15% auto;
  padding: 2rem;
  border-radius: 8px;
  width: 80%;
  max-width: 500px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  cursor: pointer;
}

.btn-create {
  margin-top: 1rem;
}

/* Version history styles */
.btn-history {
  background-color: #2196F3;
  margin-left: 0.5rem;
}

.btn-history:hover {
  background-color: #1976D2;
}

.btn-sync {
  background-color: #009688;
  margin-left: 0.5rem;
}

.btn-sync:hover {
  background-color: #00796B;
}

.btn-sync.synced {
  background-color: #00796B;
}

.vscode-update-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #2196F3;
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  animation: slide-in 0.3s ease-out;
}

.vscode-update-notification.fade-out {
  opacity: 0;
  transition: opacity 0.5s ease-out;
}

@keyframes slide-in {
  from { transform: translateY(100px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.btn-save-version {
  background-color: #673AB7;
}

.btn-save-version:hover {
  background-color: #5E35B1;
}

.version-history-content {
  max-width: 700px;
  max-height: 80vh;
  overflow: auto;
}

.version-list-container {
  margin-top: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.version-list {
  list-style: none;
  padding: 0;
}

.version-item {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version-item:hover {
  background-color: #f9f9f9;
}

.version-name {
  font-weight: bold;
  display: block;
  margin-bottom: 0.25rem;
}

.version-meta {
  font-size: 0.85rem;
  color: #666;
}

.version-date {
  margin-right: 0.5rem;
}

.btn-restore {
  background-color: #FF9800;
  font-size: 0.85rem;
  padding: 0.35rem 0.7rem;
}

.btn-restore:hover {
  background-color: #F57C00;
}

.version-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

#version-name {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 250px;
}

/* Track changes styles */
.track-changes-toggle {
  display: flex;
  align-items: center;
  margin-left: 1rem;
}

.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  margin-left: 0.5rem;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

.slider.round {
  border-radius: 20px;
}

.slider.round:before {
  border-radius: 50%;
}

.btn-changes {
  background-color: #9C27B0;
  margin-left: 0.5rem;
}

.btn-changes:hover {
  background-color: #7B1FA2;
}

.pending-changes-content {
  max-width: 700px;
  max-height: 80vh;
  overflow: auto;
}

.pending-changes-container {
  margin-top: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.pending-changes-list {
  list-style: none;
  padding: 0;
}

.pending-change-item {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  display: flex;
  flex-direction: column;
}

.pending-change-item:hover {
  background-color: #f9f9f9;
}

.pending-change-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.pending-change-user {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pending-change-user-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.pending-change-content {
  background-color: #f5f5f5;
  padding: 0.5rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

.pending-change-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.btn-accept {
  background-color: #4CAF50;
}

.btn-accept:hover {
  background-color: #388E3C;
}

.btn-reject {
  background-color: #F44336;
}

.btn-reject:hover {
  background-color: #D32F2F;
}

/* Highlight for pending changes in the editor */
.cm-pending-change {
  background-color: rgba(255, 235, 59, 0.3);
  border-bottom: 2px dashed #FFC107;
}

/* Project Explorer Styles */
.project-explorer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.project-header {
  padding: 15px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.project-selector {
  display: flex;
  gap: 10px;
  align-items: center;
}

.project-selector select {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.project-actions {
  padding: 10px 15px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  gap: 10px;
}

.project-actions .btn {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.project-actions .btn:hover {
  background: #f0f0f0;
}

.project-actions .btn.btn-secondary {
  background: #6c757d;
  color: white;
  border-color: #6c757d;
}

.project-actions .btn.btn-success {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.project-actions .btn.btn-info {
  background: #17a2b8;
  color: white;
  border-color: #17a2b8;
}

.project-actions .btn.btn-outline-info {
  background: transparent;
  color: #17a2b8;
  border-color: #17a2b8;
}

.project-actions .btn.btn-outline-info:hover {
  background: #17a2b8;
  color: white;
}

.project-tree {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.empty-state {
  text-align: center;
  color: #666;
  padding: 40px 20px;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.5;
}

.tree-container {
  font-size: 14px;
}

.project-title {
  font-size: 16px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.folder-item, .document-item {
  padding: 6px 8px;
  margin: 2px 0;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.folder-item:hover, .document-item:hover {
  background: #f0f0f0;
}

.document-item {
  color: #333;
}

.document-item:hover {
  background: #e3f2fd;
}

.document-item.active {
  background: #2196f3;
  color: white;
}

.document-item.active small {
  color: rgba(255, 255, 255, 0.8);
}

.document-item small {
  margin-left: auto;
  font-size: 11px;
}

.folder-item i {
  color: #ffc107;
}

.document-item i {
  color: #007acc;
}

.document-item.active i {
  color: white;
}

/* Collaboration Styles */
.notification-area {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
}

.invitation-notification {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-bottom: 15px;
  position: relative;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.invitation-content {
  padding: 20px;
}

.invitation-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  color: #17a2b8;
  font-weight: bold;
}

.invitation-details {
  margin-bottom: 20px;
}

.project-name {
  font-weight: bold;
  color: #333;
  margin: 5px 0;
}

.project-description {
  color: #666;
  font-size: 14px;
  margin: 5px 0;
}

.invitation-actions {
  display: flex;
  gap: 10px;
}

.invitation-actions .btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.btn-accept {
  background: #28a745;
  color: white;
}

.btn-accept:hover {
  background: #218838;
}

.btn-decline {
  background: #dc3545;
  color: white;
}

.btn-decline:hover {
  background: #c82333;
}

.close-notification {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 16px;
  color: #999;
  cursor: pointer;
  padding: 5px;
}

.close-notification:hover {
  color: #333;
}

.message {
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.message button {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
}

/* Member Management Styles */
.member-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.member-item:last-child {
  border-bottom: none;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.member-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.member-name {
  font-weight: bold;
  color: #333;
}

.member-role {
  font-size: 12px;
  color: #17a2b8;
  text-transform: uppercase;
  font-weight: bold;
}

.member-joined {
  font-size: 12px;
  color: #666;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border: 1px solid #dc3545;
}

.btn-danger:hover {
  background: #c82333;
  border-color: #bd2130;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.modal-actions {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}
