(()=>{"use strict";let t=[],e=[];function i(i){if(i<768)return!1;for(let n=0,r=t.length;;){let s=n+r>>1;if(i<t[s])r=s;else{if(!(i>=e[s]))return!0;n=s+1}if(n==r)return!1}}function n(t){return t>=127462&&t<=127487}function r(t,e,i=!0,n=!0){return(i?s:o)(t,e,n)}function s(t,e,r){if(e==t.length)return e;e&&l(t.charCodeAt(e))&&h(t.charCodeAt(e-1))&&e--;let s=a(t,e);for(e+=c(s);e<t.length;){let o=a(t,e);if(8205==s||8205==o||r&&i(o))e+=c(o),s=o;else{if(!n(o))break;{let i=0,r=e-2;for(;r>=0&&n(a(t,r));)i++,r-=2;if(i%2==0)break;e+=2}}}return e}function o(t,e,i){for(;e>0;){let n=s(t,e-2,i);if(n<e)return n;e--}return 0}function a(t,e){let i=t.charCodeAt(e);if(!h(i)||e+1==t.length)return i;let n=t.charCodeAt(e+1);return l(n)?n-56320+(i-55296<<10)+65536:i}function l(t){return t>=56320&&t<57344}function h(t){return t>=55296&&t<56320}function c(t){return t<65536?1:2}(()=>{let i="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map(t=>t?parseInt(t,36):1);for(let n=0,r=0;n<i.length;n++)(n%2?e:t).push(r+=i[n])})();class O{lineAt(t){if(t<0||t>this.length)throw new RangeError(`Invalid position ${t} in document of length ${this.length}`);return this.lineInner(t,!1,1,0)}line(t){if(t<1||t>this.lines)throw new RangeError(`Invalid line number ${t} in ${this.lines}-line document`);return this.lineInner(t,!0,1,0)}replace(t,e,i){[t,e]=x(this,t,e);let n=[];return this.decompose(0,t,n,2),i.length&&i.decompose(0,i.length,n,3),this.decompose(e,this.length,n,1),d.from(n,this.length-(e-t)+i.length)}append(t){return this.replace(this.length,this.length,t)}slice(t,e=this.length){[t,e]=x(this,t,e);let i=[];return this.decompose(t,e,i,0),d.from(i,e-t)}eq(t){if(t==this)return!0;if(t.length!=this.length||t.lines!=this.lines)return!1;let e=this.scanIdentical(t,1),i=this.length-this.scanIdentical(t,-1),n=new g(this),r=new g(t);for(let t=e,s=e;;){if(n.next(t),r.next(t),t=0,n.lineBreak!=r.lineBreak||n.done!=r.done||n.value!=r.value)return!1;if(s+=n.value.length,n.done||s>=i)return!0}}iter(t=1){return new g(this,t)}iterRange(t,e=this.length){return new m(this,t,e)}iterLines(t,e){let i;if(null==t)i=this.iter();else{null==e&&(e=this.lines+1);let n=this.line(t).from;i=this.iterRange(n,Math.max(n,e==this.lines+1?this.length:e<=1?0:this.line(e-1).to))}return new Q(i)}toString(){return this.sliceString(0)}toJSON(){let t=[];return this.flatten(t),t}constructor(){}static of(t){if(0==t.length)throw new RangeError("A document must have at least one line");return 1!=t.length||t[0]?t.length<=32?new u(t):d.from(u.split(t,[])):O.empty}}class u extends O{constructor(t,e=function(t){let e=-1;for(let i of t)e+=i.length+1;return e}(t)){super(),this.text=t,this.length=e}get lines(){return this.text.length}get children(){return null}lineInner(t,e,i,n){for(let r=0;;r++){let s=this.text[r],o=n+s.length;if((e?i:o)>=t)return new S(n,o,i,s);n=o+1,i++}}decompose(t,e,i,n){let r=t<=0&&e>=this.length?this:new u(p(this.text,t,e),Math.min(e,this.length)-Math.max(0,t));if(1&n){let t=i.pop(),e=f(r.text,t.text.slice(),0,r.length);if(e.length<=32)i.push(new u(e,t.length+r.length));else{let t=e.length>>1;i.push(new u(e.slice(0,t)),new u(e.slice(t)))}}else i.push(r)}replace(t,e,i){if(!(i instanceof u))return super.replace(t,e,i);[t,e]=x(this,t,e);let n=f(this.text,f(i.text,p(this.text,0,t)),e),r=this.length+i.length-(e-t);return n.length<=32?new u(n,r):d.from(u.split(n,[]),r)}sliceString(t,e=this.length,i="\n"){[t,e]=x(this,t,e);let n="";for(let r=0,s=0;r<=e&&s<this.text.length;s++){let o=this.text[s],a=r+o.length;r>t&&s&&(n+=i),t<a&&e>r&&(n+=o.slice(Math.max(0,t-r),e-r)),r=a+1}return n}flatten(t){for(let e of this.text)t.push(e)}scanIdentical(){return 0}static split(t,e){let i=[],n=-1;for(let r of t)i.push(r),n+=r.length+1,32==i.length&&(e.push(new u(i,n)),i=[],n=-1);return n>-1&&e.push(new u(i,n)),e}}class d extends O{constructor(t,e){super(),this.children=t,this.length=e,this.lines=0;for(let e of t)this.lines+=e.lines}lineInner(t,e,i,n){for(let r=0;;r++){let s=this.children[r],o=n+s.length,a=i+s.lines-1;if((e?a:o)>=t)return s.lineInner(t,e,i,n);n=o+1,i=a+1}}decompose(t,e,i,n){for(let r=0,s=0;s<=e&&r<this.children.length;r++){let o=this.children[r],a=s+o.length;if(t<=a&&e>=s){let r=n&((s<=t?1:0)|(a>=e?2:0));s>=t&&a<=e&&!r?i.push(o):o.decompose(t-s,e-s,i,r)}s=a+1}}replace(t,e,i){if([t,e]=x(this,t,e),i.lines<this.lines)for(let n=0,r=0;n<this.children.length;n++){let s=this.children[n],o=r+s.length;if(t>=r&&e<=o){let a=s.replace(t-r,e-r,i),l=this.lines-s.lines+a.lines;if(a.lines<l>>4&&a.lines>l>>6){let r=this.children.slice();return r[n]=a,new d(r,this.length-(e-t)+i.length)}return super.replace(r,o,a)}r=o+1}return super.replace(t,e,i)}sliceString(t,e=this.length,i="\n"){[t,e]=x(this,t,e);let n="";for(let r=0,s=0;r<this.children.length&&s<=e;r++){let o=this.children[r],a=s+o.length;s>t&&r&&(n+=i),t<a&&e>s&&(n+=o.sliceString(t-s,e-s,i)),s=a+1}return n}flatten(t){for(let e of this.children)e.flatten(t)}scanIdentical(t,e){if(!(t instanceof d))return 0;let i=0,[n,r,s,o]=e>0?[0,0,this.children.length,t.children.length]:[this.children.length-1,t.children.length-1,-1,-1];for(;;n+=e,r+=e){if(n==s||r==o)return i;let a=this.children[n],l=t.children[r];if(a!=l)return i+a.scanIdentical(l,e);i+=a.length+1}}static from(t,e=t.reduce((t,e)=>t+e.length+1,-1)){let i=0;for(let e of t)i+=e.lines;if(i<32){let i=[];for(let e of t)e.flatten(i);return new u(i,e)}let n=Math.max(32,i>>5),r=n<<1,s=n>>1,o=[],a=0,l=-1,h=[];function c(t){let e;if(t.lines>r&&t instanceof d)for(let e of t.children)c(e);else t.lines>s&&(a>s||!a)?(O(),o.push(t)):t instanceof u&&a&&(e=h[h.length-1])instanceof u&&t.lines+e.lines<=32?(a+=t.lines,l+=t.length+1,h[h.length-1]=new u(e.text.concat(t.text),e.length+1+t.length)):(a+t.lines>n&&O(),a+=t.lines,l+=t.length+1,h.push(t))}function O(){0!=a&&(o.push(1==h.length?h[0]:d.from(h,l)),l=-1,a=h.length=0)}for(let e of t)c(e);return O(),1==o.length?o[0]:new d(o,e)}}function f(t,e,i=0,n=1e9){for(let r=0,s=0,o=!0;s<t.length&&r<=n;s++){let a=t[s],l=r+a.length;l>=i&&(l>n&&(a=a.slice(0,n-r)),r<i&&(a=a.slice(i-r)),o?(e[e.length-1]+=a,o=!1):e.push(a)),r=l+1}return e}function p(t,e,i){return f(t,[""],e,i)}O.empty=new u([""],0);class g{constructor(t,e=1){this.dir=e,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[t],this.offsets=[e>0?1:(t instanceof u?t.text.length:t.children.length)<<1]}nextInner(t,e){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,n=this.nodes[i],r=this.offsets[i],s=r>>1,o=n instanceof u?n.text.length:n.children.length;if(s==(e>0?o:0)){if(0==i)return this.done=!0,this.value="",this;e>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop()}else if((1&r)==(e>0?0:1)){if(this.offsets[i]+=e,0==t)return this.lineBreak=!0,this.value="\n",this;t--}else if(n instanceof u){let r=n.text[s+(e<0?-1:0)];if(this.offsets[i]+=e,r.length>Math.max(0,t))return this.value=0==t?r:e>0?r.slice(t):r.slice(0,r.length-t),this;t-=r.length}else{let r=n.children[s+(e<0?-1:0)];t>r.length?(t-=r.length,this.offsets[i]+=e):(e<0&&this.offsets[i]--,this.nodes.push(r),this.offsets.push(e>0?1:(r instanceof u?r.text.length:r.children.length)<<1))}}}next(t=0){return t<0&&(this.nextInner(-t,-this.dir),t=this.value.length),this.nextInner(t,this.dir)}}class m{constructor(t,e,i){this.value="",this.done=!1,this.cursor=new g(t,e>i?-1:1),this.pos=e>i?t.length:0,this.from=Math.min(e,i),this.to=Math.max(e,i)}nextInner(t,e){if(e<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;t+=Math.max(0,e<0?this.pos-this.to:this.from-this.pos);let i=e<0?this.pos-this.from:this.to-this.pos;t>i&&(t=i),i-=t;let{value:n}=this.cursor.next(t);return this.pos+=(n.length+t)*e,this.value=n.length<=i?n:e<0?n.slice(n.length-i):n.slice(0,i),this.done=!this.value,this}next(t=0){return t<0?t=Math.max(t,this.from-this.pos):t>0&&(t=Math.min(t,this.to-this.pos)),this.nextInner(t,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&""!=this.value}}class Q{constructor(t){this.inner=t,this.afterBreak=!0,this.value="",this.done=!1}next(t=0){let{done:e,lineBreak:i,value:n}=this.inner.next(t);return e&&this.afterBreak?(this.value="",this.afterBreak=!1):e?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=n,this.afterBreak=!1),this}get lineBreak(){return!1}}"undefined"!=typeof Symbol&&(O.prototype[Symbol.iterator]=function(){return this.iter()},g.prototype[Symbol.iterator]=m.prototype[Symbol.iterator]=Q.prototype[Symbol.iterator]=function(){return this});class S{constructor(t,e,i,n){this.from=t,this.to=e,this.number=i,this.text=n}get length(){return this.to-this.from}}function x(t,e,i){return[e=Math.max(0,Math.min(t.length,e)),Math.max(e,Math.min(t.length,i))]}function y(t,e,i=!0,n=!0){return r(t,e,i,n)}function w(t,e){let i=t.charCodeAt(e);if(!((n=i)>=55296&&n<56320&&e+1!=t.length))return i;var n;let r=t.charCodeAt(e+1);return function(t){return t>=56320&&t<57344}(r)?r-56320+(i-55296<<10)+65536:i}function b(t){return t<65536?1:2}const k=/\r\n?|\n/;var v=function(t){return t[t.Simple=0]="Simple",t[t.TrackDel=1]="TrackDel",t[t.TrackBefore=2]="TrackBefore",t[t.TrackAfter=3]="TrackAfter",t}(v||(v={}));class ${constructor(t){this.sections=t}get length(){let t=0;for(let e=0;e<this.sections.length;e+=2)t+=this.sections[e];return t}get newLength(){let t=0;for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e+1];t+=i<0?this.sections[e]:i}return t}get empty(){return 0==this.sections.length||2==this.sections.length&&this.sections[1]<0}iterGaps(t){for(let e=0,i=0,n=0;e<this.sections.length;){let r=this.sections[e++],s=this.sections[e++];s<0?(t(i,n,r),n+=r):n+=s,i+=r}}iterChangedRanges(t,e=!1){X(this,t,e)}get invertedDesc(){let t=[];for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];n<0?t.push(i,n):t.push(n,i)}return new $(t)}composeDesc(t){return this.empty?t:t.empty?this:C(this,t)}mapDesc(t,e=!1){return t.empty?this:A(this,t,e)}mapPos(t,e=-1,i=v.Simple){let n=0,r=0;for(let s=0;s<this.sections.length;){let o=this.sections[s++],a=this.sections[s++],l=n+o;if(a<0){if(l>t)return r+(t-n);r+=o}else{if(i!=v.Simple&&l>=t&&(i==v.TrackDel&&n<t&&l>t||i==v.TrackBefore&&n<t||i==v.TrackAfter&&l>t))return null;if(l>t||l==t&&e<0&&!o)return t==n||e<0?r:r+a;r+=a}n=l}if(t>n)throw new RangeError(`Position ${t} is out of range for changeset of length ${n}`);return r}touchesRange(t,e=t){for(let i=0,n=0;i<this.sections.length&&n<=e;){let r=n+this.sections[i++];if(this.sections[i++]>=0&&n<=e&&r>=t)return!(n<t&&r>e)||"cover";n=r}return!1}toString(){let t="";for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];t+=(t?" ":"")+i+(n>=0?":"+n:"")}return t}toJSON(){return this.sections}static fromJSON(t){if(!Array.isArray(t)||t.length%2||t.some(t=>"number"!=typeof t))throw new RangeError("Invalid JSON representation of ChangeDesc");return new $(t)}static create(t){return new $(t)}}class P extends ${constructor(t,e){super(t),this.inserted=e}apply(t){if(this.length!=t.length)throw new RangeError("Applying change set to a document with the wrong length");return X(this,(e,i,n,r,s)=>t=t.replace(n,n+(i-e),s),!1),t}mapDesc(t,e=!1){return A(this,t,e,!0)}invert(t){let e=this.sections.slice(),i=[];for(let n=0,r=0;n<e.length;n+=2){let s=e[n],o=e[n+1];if(o>=0){e[n]=o,e[n+1]=s;let a=n>>1;for(;i.length<a;)i.push(O.empty);i.push(s?t.slice(r,r+s):O.empty)}r+=s}return new P(e,i)}compose(t){return this.empty?t:t.empty?this:C(this,t,!0)}map(t,e=!1){return t.empty?this:A(this,t,e,!0)}iterChanges(t,e=!1){X(this,t,e)}get desc(){return $.create(this.sections)}filter(t){let e=[],i=[],n=[],r=new M(this);t:for(let s=0,o=0;;){let a=s==t.length?1e9:t[s++];for(;o<a||o==a&&0==r.len;){if(r.done)break t;let t=Math.min(r.len,a-o);T(n,t,-1);let s=-1==r.ins?-1:0==r.off?r.ins:0;T(e,t,s),s>0&&Z(i,e,r.text),r.forward(t),o+=t}let l=t[s++];for(;o<l;){if(r.done)break t;let t=Math.min(r.len,l-o);T(e,t,-1),T(n,t,-1==r.ins?-1:0==r.off?r.ins:0),r.forward(t),o+=t}}return{changes:new P(e,i),filtered:$.create(n)}}toJSON(){let t=[];for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e],n=this.sections[e+1];n<0?t.push(i):0==n?t.push([i]):t.push([i].concat(this.inserted[e>>1].toJSON()))}return t}static of(t,e,i){let n=[],r=[],s=0,o=null;function a(t=!1){if(!t&&!n.length)return;s<e&&T(n,e-s,-1);let i=new P(n,r);o=o?o.compose(i.map(o)):i,n=[],r=[],s=0}return function t(l){if(Array.isArray(l))for(let e of l)t(e);else if(l instanceof P){if(l.length!=e)throw new RangeError(`Mismatched change set length (got ${l.length}, expected ${e})`);a(),o=o?o.compose(l.map(o)):l}else{let{from:t,to:o=t,insert:h}=l;if(t>o||t<0||o>e)throw new RangeError(`Invalid change range ${t} to ${o} (in doc of length ${e})`);let c=h?"string"==typeof h?O.of(h.split(i||k)):h:O.empty,u=c.length;if(t==o&&0==u)return;t<s&&a(),t>s&&T(n,t-s,-1),T(n,o-t,u),Z(r,n,c),s=o}}(t),a(!o),o}static empty(t){return new P(t?[t,-1]:[],[])}static fromJSON(t){if(!Array.isArray(t))throw new RangeError("Invalid JSON representation of ChangeSet");let e=[],i=[];for(let n=0;n<t.length;n++){let r=t[n];if("number"==typeof r)e.push(r,-1);else{if(!Array.isArray(r)||"number"!=typeof r[0]||r.some((t,e)=>e&&"string"!=typeof t))throw new RangeError("Invalid JSON representation of ChangeSet");if(1==r.length)e.push(r[0],0);else{for(;i.length<n;)i.push(O.empty);i[n]=O.of(r.slice(1)),e.push(r[0],i[n].length)}}}return new P(e,i)}static createSet(t,e){return new P(t,e)}}function T(t,e,i,n=!1){if(0==e&&i<=0)return;let r=t.length-2;r>=0&&i<=0&&i==t[r+1]?t[r]+=e:r>=0&&0==e&&0==t[r]?t[r+1]+=i:n?(t[r]+=e,t[r+1]+=i):t.push(e,i)}function Z(t,e,i){if(0==i.length)return;let n=e.length-2>>1;if(n<t.length)t[t.length-1]=t[t.length-1].append(i);else{for(;t.length<n;)t.push(O.empty);t.push(i)}}function X(t,e,i){let n=t.inserted;for(let r=0,s=0,o=0;o<t.sections.length;){let a=t.sections[o++],l=t.sections[o++];if(l<0)r+=a,s+=a;else{let h=r,c=s,u=O.empty;for(;h+=a,c+=l,l&&n&&(u=u.append(n[o-2>>1])),!(i||o==t.sections.length||t.sections[o+1]<0);)a=t.sections[o++],l=t.sections[o++];e(r,h,s,c,u),r=h,s=c}}}function A(t,e,i,n=!1){let r=[],s=n?[]:null,o=new M(t),a=new M(e);for(let t=-1;;){if(o.done&&a.len||a.done&&o.len)throw new Error("Mismatched change set lengths");if(-1==o.ins&&-1==a.ins){let t=Math.min(o.len,a.len);T(r,t,-1),o.forward(t),a.forward(t)}else if(a.ins>=0&&(o.ins<0||t==o.i||0==o.off&&(a.len<o.len||a.len==o.len&&!i))){let e=a.len;for(T(r,a.ins,-1);e;){let i=Math.min(o.len,e);o.ins>=0&&t<o.i&&o.len<=i&&(T(r,0,o.ins),s&&Z(s,r,o.text),t=o.i),o.forward(i),e-=i}a.next()}else{if(!(o.ins>=0)){if(o.done&&a.done)return s?P.createSet(r,s):$.create(r);throw new Error("Mismatched change set lengths")}{let e=0,i=o.len;for(;i;)if(-1==a.ins){let t=Math.min(i,a.len);e+=t,i-=t,a.forward(t)}else{if(!(0==a.ins&&a.len<i))break;i-=a.len,a.next()}T(r,e,t<o.i?o.ins:0),s&&t<o.i&&Z(s,r,o.text),t=o.i,o.forward(o.len-i)}}}}function C(t,e,i=!1){let n=[],r=i?[]:null,s=new M(t),o=new M(e);for(let t=!1;;){if(s.done&&o.done)return r?P.createSet(n,r):$.create(n);if(0==s.ins)T(n,s.len,0,t),s.next();else if(0!=o.len||o.done){if(s.done||o.done)throw new Error("Mismatched change set lengths");{let e=Math.min(s.len2,o.len),i=n.length;if(-1==s.ins){let i=-1==o.ins?-1:o.off?0:o.ins;T(n,e,i,t),r&&i&&Z(r,n,o.text)}else-1==o.ins?(T(n,s.off?0:s.len,e,t),r&&Z(r,n,s.textBit(e))):(T(n,s.off?0:s.len,o.off?0:o.ins,t),r&&!o.off&&Z(r,n,o.text));t=(s.ins>e||o.ins>=0&&o.len>e)&&(t||n.length>i),s.forward2(e),o.forward(e)}}else T(n,0,o.ins,t),r&&Z(r,n,o.text),o.next()}}class M{constructor(t){this.set=t,this.i=0,this.next()}next(){let{sections:t}=this.set;this.i<t.length?(this.len=t[this.i++],this.ins=t[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return-2==this.ins}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:t}=this.set,e=this.i-2>>1;return e>=t.length?O.empty:t[e]}textBit(t){let{inserted:e}=this.set,i=this.i-2>>1;return i>=e.length&&!t?O.empty:e[i].slice(this.off,null==t?void 0:this.off+t)}forward(t){t==this.len?this.next():(this.len-=t,this.off+=t)}forward2(t){-1==this.ins?this.forward(t):t==this.ins?this.next():(this.ins-=t,this.off+=t)}}class R{constructor(t,e,i){this.from=t,this.to=e,this.flags=i}get anchor(){return 32&this.flags?this.to:this.from}get head(){return 32&this.flags?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return 8&this.flags?-1:16&this.flags?1:0}get bidiLevel(){let t=7&this.flags;return 7==t?null:t}get goalColumn(){let t=this.flags>>6;return 16777215==t?void 0:t}map(t,e=-1){let i,n;return this.empty?i=n=t.mapPos(this.from,e):(i=t.mapPos(this.from,1),n=t.mapPos(this.to,-1)),i==this.from&&n==this.to?this:new R(i,n,this.flags)}extend(t,e=t){if(t<=this.anchor&&e>=this.anchor)return _.range(t,e);let i=Math.abs(t-this.anchor)>Math.abs(e-this.anchor)?t:e;return _.range(this.anchor,i)}eq(t,e=!1){return!(this.anchor!=t.anchor||this.head!=t.head||e&&this.empty&&this.assoc!=t.assoc)}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(t){if(!t||"number"!=typeof t.anchor||"number"!=typeof t.head)throw new RangeError("Invalid JSON representation for SelectionRange");return _.range(t.anchor,t.head)}static create(t,e,i){return new R(t,e,i)}}class _{constructor(t,e){this.ranges=t,this.mainIndex=e}map(t,e=-1){return t.empty?this:_.create(this.ranges.map(i=>i.map(t,e)),this.mainIndex)}eq(t,e=!1){if(this.ranges.length!=t.ranges.length||this.mainIndex!=t.mainIndex)return!1;for(let i=0;i<this.ranges.length;i++)if(!this.ranges[i].eq(t.ranges[i],e))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return 1==this.ranges.length?this:new _([this.main],0)}addRange(t,e=!0){return _.create([t].concat(this.ranges),e?0:this.mainIndex+1)}replaceRange(t,e=this.mainIndex){let i=this.ranges.slice();return i[e]=t,_.create(i,this.mainIndex)}toJSON(){return{ranges:this.ranges.map(t=>t.toJSON()),main:this.mainIndex}}static fromJSON(t){if(!t||!Array.isArray(t.ranges)||"number"!=typeof t.main||t.main>=t.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new _(t.ranges.map(t=>R.fromJSON(t)),t.main)}static single(t,e=t){return new _([_.range(t,e)],0)}static create(t,e=0){if(0==t.length)throw new RangeError("A selection needs at least one range");for(let i=0,n=0;n<t.length;n++){let r=t[n];if(r.empty?r.from<=i:r.from<i)return _.normalized(t.slice(),e);i=r.to}return new _(t,e)}static cursor(t,e=0,i,n){return R.create(t,t,(0==e?0:e<0?8:16)|(null==i?7:Math.min(6,i))|(null!=n?n:16777215)<<6)}static range(t,e,i,n){let r=(null!=i?i:16777215)<<6|(null==n?7:Math.min(6,n));return e<t?R.create(e,t,48|r):R.create(t,e,(e>t?8:0)|r)}static normalized(t,e=0){let i=t[e];t.sort((t,e)=>t.from-e.from),e=t.indexOf(i);for(let i=1;i<t.length;i++){let n=t[i],r=t[i-1];if(n.empty?n.from<=r.to:n.from<r.to){let s=r.from,o=Math.max(n.to,r.to);i<=e&&e--,t.splice(--i,2,n.anchor>n.head?_.range(o,s):_.range(s,o))}}return new _(t,e)}}function z(t,e){for(let i of t.ranges)if(i.to>e)throw new RangeError("Selection points outside of document")}let Y=0;class E{constructor(t,e,i,n,r){this.combine=t,this.compareInput=e,this.compare=i,this.isStatic=n,this.id=Y++,this.default=t([]),this.extensions="function"==typeof r?r(this):r}get reader(){return this}static define(t={}){return new E(t.combine||(t=>t),t.compareInput||((t,e)=>t===e),t.compare||(t.combine?(t,e)=>t===e:V),!!t.static,t.enables)}of(t){return new q([],this,0,t)}compute(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new q(t,this,1,e)}computeN(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new q(t,this,2,e)}from(t,e){return e||(e=t=>t),this.compute([t],i=>e(i.field(t)))}}function V(t,e){return t==e||t.length==e.length&&t.every((t,i)=>t===e[i])}class q{constructor(t,e,i,n){this.dependencies=t,this.facet=e,this.type=i,this.value=n,this.id=Y++}dynamicSlot(t){var e;let i=this.value,n=this.facet.compareInput,r=this.id,s=t[r]>>1,o=2==this.type,a=!1,l=!1,h=[];for(let i of this.dependencies)"doc"==i?a=!0:"selection"==i?l=!0:1&(null!==(e=t[i.id])&&void 0!==e?e:1)||h.push(t[i.id]);return{create:t=>(t.values[s]=i(t),1),update(t,e){if(a&&e.docChanged||l&&(e.docChanged||e.selection)||D(t,h)){let e=i(t);if(o?!L(e,t.values[s],n):!n(e,t.values[s]))return t.values[s]=e,1}return 0},reconfigure:(t,e)=>{let a,l=e.config.address[r];if(null!=l){let r=J(e,l);if(this.dependencies.every(i=>i instanceof E?e.facet(i)===t.facet(i):!(i instanceof B)||e.field(i,!1)==t.field(i,!1))||(o?L(a=i(t),r,n):n(a=i(t),r)))return t.values[s]=r,0}else a=i(t);return t.values[s]=a,1}}}}function L(t,e,i){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!i(t[n],e[n]))return!1;return!0}function D(t,e){let i=!1;for(let n of e)1&K(t,n)&&(i=!0);return i}function W(t,e,i){let n=i.map(e=>t[e.id]),r=i.map(t=>t.type),s=n.filter(t=>!(1&t)),o=t[e.id]>>1;function a(t){let i=[];for(let e=0;e<n.length;e++){let s=J(t,n[e]);if(2==r[e])for(let t of s)i.push(t);else i.push(s)}return e.combine(i)}return{create(t){for(let e of n)K(t,e);return t.values[o]=a(t),1},update(t,i){if(!D(t,s))return 0;let n=a(t);return e.compare(n,t.values[o])?0:(t.values[o]=n,1)},reconfigure(t,r){let s=D(t,n),l=r.config.facets[e.id],h=r.facet(e);if(l&&!s&&V(i,l))return t.values[o]=h,0;let c=a(t);return e.compare(c,h)?(t.values[o]=h,0):(t.values[o]=c,1)}}}const j=E.define({static:!0});class B{constructor(t,e,i,n,r){this.id=t,this.createF=e,this.updateF=i,this.compareF=n,this.spec=r,this.provides=void 0}static define(t){let e=new B(Y++,t.create,t.update,t.compare||((t,e)=>t===e),t);return t.provide&&(e.provides=t.provide(e)),e}create(t){let e=t.facet(j).find(t=>t.field==this);return((null==e?void 0:e.create)||this.createF)(t)}slot(t){let e=t[this.id]>>1;return{create:t=>(t.values[e]=this.create(t),1),update:(t,i)=>{let n=t.values[e],r=this.updateF(n,i);return this.compareF(n,r)?0:(t.values[e]=r,1)},reconfigure:(t,i)=>{let n,r=t.facet(j),s=i.facet(j);return(n=r.find(t=>t.field==this))&&n!=s.find(t=>t.field==this)?(t.values[e]=n.create(t),1):null!=i.config.address[this.id]?(t.values[e]=i.field(this),0):(t.values[e]=this.create(t),1)}}}init(t){return[this,j.of({field:this,create:t})]}get extension(){return this}}function I(t){return e=>new N(e,t)}const G={highest:I(0),high:I(1),default:I(2),low:I(3),lowest:I(4)};class N{constructor(t,e){this.inner=t,this.prec=e}}class U{of(t){return new H(this,t)}reconfigure(t){return U.reconfigure.of({compartment:this,extension:t})}get(t){return t.config.compartments.get(this)}}class H{constructor(t,e){this.compartment=t,this.inner=e}}class F{constructor(t,e,i,n,r,s){for(this.base=t,this.compartments=e,this.dynamicSlots=i,this.address=n,this.staticValues=r,this.facets=s,this.statusTemplate=[];this.statusTemplate.length<i.length;)this.statusTemplate.push(0)}staticFacet(t){let e=this.address[t.id];return null==e?t.default:this.staticValues[e>>1]}static resolve(t,e,i){let n=[],r=Object.create(null),s=new Map;for(let i of function(t,e,i){let n=[[],[],[],[],[]],r=new Map;return function t(s,o){let a=r.get(s);if(null!=a){if(a<=o)return;let t=n[a].indexOf(s);t>-1&&n[a].splice(t,1),s instanceof H&&i.delete(s.compartment)}if(r.set(s,o),Array.isArray(s))for(let e of s)t(e,o);else if(s instanceof H){if(i.has(s.compartment))throw new RangeError("Duplicate use of compartment in extensions");let n=e.get(s.compartment)||s.inner;i.set(s.compartment,n),t(n,o)}else if(s instanceof N)t(s.inner,s.prec);else if(s instanceof B)n[o].push(s),s.provides&&t(s.provides,o);else if(s instanceof q)n[o].push(s),s.facet.extensions&&t(s.facet.extensions,2);else{let e=s.extension;if(!e)throw new Error(`Unrecognized extension value in extension set (${s}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);t(e,o)}}(t,2),n.reduce((t,e)=>t.concat(e))}(t,e,s))i instanceof B?n.push(i):(r[i.facet.id]||(r[i.facet.id]=[])).push(i);let o=Object.create(null),a=[],l=[];for(let t of n)o[t.id]=l.length<<1,l.push(e=>t.slot(e));let h=null==i?void 0:i.config.facets;for(let t in r){let e=r[t],n=e[0].facet,s=h&&h[t]||[];if(e.every(t=>0==t.type))if(o[n.id]=a.length<<1|1,V(s,e))a.push(i.facet(n));else{let t=n.combine(e.map(t=>t.value));a.push(i&&n.compare(t,i.facet(n))?i.facet(n):t)}else{for(let t of e)0==t.type?(o[t.id]=a.length<<1|1,a.push(t.value)):(o[t.id]=l.length<<1,l.push(e=>t.dynamicSlot(e)));o[n.id]=l.length<<1,l.push(t=>W(t,n,e))}}let c=l.map(t=>t(o));return new F(t,s,c,o,a,r)}}function K(t,e){if(1&e)return 2;let i=e>>1,n=t.status[i];if(4==n)throw new Error("Cyclic dependency between fields and/or facets");if(2&n)return n;t.status[i]=4;let r=t.computeSlot(t,t.config.dynamicSlots[i]);return t.status[i]=2|r}function J(t,e){return 1&e?t.config.staticValues[e>>1]:t.values[e>>1]}const tt=E.define(),et=E.define({combine:t=>t.some(t=>t),static:!0}),it=E.define({combine:t=>t.length?t[0]:void 0,static:!0}),nt=E.define(),rt=E.define(),st=E.define(),ot=E.define({combine:t=>!!t.length&&t[0]});class at{constructor(t,e){this.type=t,this.value=e}static define(){return new lt}}class lt{of(t){return new at(this,t)}}class ht{constructor(t){this.map=t}of(t){return new ct(this,t)}}class ct{constructor(t,e){this.type=t,this.value=e}map(t){let e=this.type.map(this.value,t);return void 0===e?void 0:e==this.value?this:new ct(this.type,e)}is(t){return this.type==t}static define(t={}){return new ht(t.map||(t=>t))}static mapEffects(t,e){if(!t.length)return t;let i=[];for(let n of t){let t=n.map(e);t&&i.push(t)}return i}}ct.reconfigure=ct.define(),ct.appendConfig=ct.define();class Ot{constructor(t,e,i,n,r,s){this.startState=t,this.changes=e,this.selection=i,this.effects=n,this.annotations=r,this.scrollIntoView=s,this._doc=null,this._state=null,i&&z(i,e.newLength),r.some(t=>t.type==Ot.time)||(this.annotations=r.concat(Ot.time.of(Date.now())))}static create(t,e,i,n,r,s){return new Ot(t,e,i,n,r,s)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(t){for(let e of this.annotations)if(e.type==t)return e.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(t){let e=this.annotation(Ot.userEvent);return!(!e||!(e==t||e.length>t.length&&e.slice(0,t.length)==t&&"."==e[t.length]))}}function ut(t,e){let i=[];for(let n=0,r=0;;){let s,o;if(n<t.length&&(r==e.length||e[r]>=t[n]))s=t[n++],o=t[n++];else{if(!(r<e.length))return i;s=e[r++],o=e[r++]}!i.length||i[i.length-1]<s?i.push(s,o):i[i.length-1]<o&&(i[i.length-1]=o)}}function dt(t,e,i){var n;let r,s,o;return i?(r=e.changes,s=P.empty(e.changes.length),o=t.changes.compose(e.changes)):(r=e.changes.map(t.changes),s=t.changes.mapDesc(e.changes,!0),o=t.changes.compose(r)),{changes:o,selection:e.selection?e.selection.map(s):null===(n=t.selection)||void 0===n?void 0:n.map(r),effects:ct.mapEffects(t.effects,r).concat(ct.mapEffects(e.effects,s)),annotations:t.annotations.length?t.annotations.concat(e.annotations):e.annotations,scrollIntoView:t.scrollIntoView||e.scrollIntoView}}function ft(t,e,i){let n=e.selection,r=mt(e.annotations);return e.userEvent&&(r=r.concat(Ot.userEvent.of(e.userEvent))),{changes:e.changes instanceof P?e.changes:P.of(e.changes||[],i,t.facet(it)),selection:n&&(n instanceof _?n:_.single(n.anchor,n.head)),effects:mt(e.effects),annotations:r,scrollIntoView:!!e.scrollIntoView}}function pt(t,e,i){let n=ft(t,e.length?e[0]:{},t.doc.length);e.length&&!1===e[0].filter&&(i=!1);for(let r=1;r<e.length;r++){!1===e[r].filter&&(i=!1);let s=!!e[r].sequential;n=dt(n,ft(t,e[r],s?n.changes.newLength:t.doc.length),s)}let r=Ot.create(t,n.changes,n.selection,n.effects,n.annotations,n.scrollIntoView);return function(t){let e=t.startState,i=e.facet(st),n=t;for(let r=i.length-1;r>=0;r--){let s=i[r](t);s&&Object.keys(s).length&&(n=dt(n,ft(e,s,t.changes.newLength),!0))}return n==t?t:Ot.create(e,t.changes,t.selection,n.effects,n.annotations,n.scrollIntoView)}(i?function(t){let e=t.startState,i=!0;for(let n of e.facet(nt)){let e=n(t);if(!1===e){i=!1;break}Array.isArray(e)&&(i=!0===i?e:ut(i,e))}if(!0!==i){let n,r;if(!1===i)r=t.changes.invertedDesc,n=P.empty(e.doc.length);else{let e=t.changes.filter(i);n=e.changes,r=e.filtered.mapDesc(e.changes).invertedDesc}t=Ot.create(e,n,t.selection&&t.selection.map(r),ct.mapEffects(t.effects,r),t.annotations,t.scrollIntoView)}let n=e.facet(rt);for(let i=n.length-1;i>=0;i--){let r=n[i](t);t=r instanceof Ot?r:Array.isArray(r)&&1==r.length&&r[0]instanceof Ot?r[0]:pt(e,mt(r),!1)}return t}(r):r)}Ot.time=at.define(),Ot.userEvent=at.define(),Ot.addToHistory=at.define(),Ot.remote=at.define();const gt=[];function mt(t){return null==t?gt:Array.isArray(t)?t:[t]}var Qt=function(t){return t[t.Word=0]="Word",t[t.Space=1]="Space",t[t.Other=2]="Other",t}(Qt||(Qt={}));const St=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let xt;try{xt=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch(t){}class yt{constructor(t,e,i,n,r,s){this.config=t,this.doc=e,this.selection=i,this.values=n,this.status=t.statusTemplate.slice(),this.computeSlot=r,s&&(s._state=this);for(let t=0;t<this.config.dynamicSlots.length;t++)K(this,t<<1);this.computeSlot=null}field(t,e=!0){let i=this.config.address[t.id];if(null!=i)return K(this,i),J(this,i);if(e)throw new RangeError("Field is not present in this state")}update(...t){return pt(this,t,!0)}applyTransaction(t){let e,i=this.config,{base:n,compartments:r}=i;for(let e of t.effects)e.is(U.reconfigure)?(i&&(r=new Map,i.compartments.forEach((t,e)=>r.set(e,t)),i=null),r.set(e.value.compartment,e.value.extension)):e.is(ct.reconfigure)?(i=null,n=e.value):e.is(ct.appendConfig)&&(i=null,n=mt(n).concat(e.value));i?e=t.startState.values.slice():(i=F.resolve(n,r,this),e=new yt(i,this.doc,this.selection,i.dynamicSlots.map(()=>null),(t,e)=>e.reconfigure(t,this),null).values);let s=t.startState.facet(et)?t.newSelection:t.newSelection.asSingle();new yt(i,t.newDoc,s,e,(e,i)=>i.update(e,t),t)}replaceSelection(t){return"string"==typeof t&&(t=this.toText(t)),this.changeByRange(e=>({changes:{from:e.from,to:e.to,insert:t},range:_.cursor(e.from+t.length)}))}changeByRange(t){let e=this.selection,i=t(e.ranges[0]),n=this.changes(i.changes),r=[i.range],s=mt(i.effects);for(let i=1;i<e.ranges.length;i++){let o=t(e.ranges[i]),a=this.changes(o.changes),l=a.map(n);for(let t=0;t<i;t++)r[t]=r[t].map(l);let h=n.mapDesc(a,!0);r.push(o.range.map(h)),n=n.compose(l),s=ct.mapEffects(s,l).concat(ct.mapEffects(mt(o.effects),h))}return{changes:n,selection:_.create(r,e.mainIndex),effects:s}}changes(t=[]){return t instanceof P?t:P.of(t,this.doc.length,this.facet(yt.lineSeparator))}toText(t){return O.of(t.split(this.facet(yt.lineSeparator)||k))}sliceDoc(t=0,e=this.doc.length){return this.doc.sliceString(t,e,this.lineBreak)}facet(t){let e=this.config.address[t.id];return null==e?t.default:(K(this,e),J(this,e))}toJSON(t){let e={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(t)for(let i in t){let n=t[i];n instanceof B&&null!=this.config.address[n.id]&&(e[i]=n.spec.toJSON(this.field(t[i]),this))}return e}static fromJSON(t,e={},i){if(!t||"string"!=typeof t.doc)throw new RangeError("Invalid JSON representation for EditorState");let n=[];if(i)for(let e in i)if(Object.prototype.hasOwnProperty.call(t,e)){let r=i[e],s=t[e];n.push(r.init(t=>r.spec.fromJSON(s,t)))}return yt.create({doc:t.doc,selection:_.fromJSON(t.selection),extensions:e.extensions?n.concat([e.extensions]):n})}static create(t={}){let e=F.resolve(t.extensions||[],new Map),i=t.doc instanceof O?t.doc:O.of((t.doc||"").split(e.staticFacet(yt.lineSeparator)||k)),n=t.selection?t.selection instanceof _?t.selection:_.single(t.selection.anchor,t.selection.head):_.single(0);return z(n,i.length),e.staticFacet(et)||(n=n.asSingle()),new yt(e,i,n,e.dynamicSlots.map(()=>null),(t,e)=>e.create(t),null)}get tabSize(){return this.facet(yt.tabSize)}get lineBreak(){return this.facet(yt.lineSeparator)||"\n"}get readOnly(){return this.facet(ot)}phrase(t,...e){for(let e of this.facet(yt.phrases))if(Object.prototype.hasOwnProperty.call(e,t)){t=e[t];break}return e.length&&(t=t.replace(/\$(\$|\d*)/g,(t,i)=>{if("$"==i)return"$";let n=+(i||1);return!n||n>e.length?t:e[n-1]})),t}languageDataAt(t,e,i=-1){let n=[];for(let r of this.facet(tt))for(let s of r(this,e,i))Object.prototype.hasOwnProperty.call(s,t)&&n.push(s[t]);return n}charCategorizer(t){return e=this.languageDataAt("wordChars",t).join(""),t=>{if(!/\S/.test(t))return Qt.Space;if(function(t){if(xt)return xt.test(t);for(let e=0;e<t.length;e++){let i=t[e];if(/\w/.test(i)||i>""&&(i.toUpperCase()!=i.toLowerCase()||St.test(i)))return!0}return!1}(t))return Qt.Word;for(let i=0;i<e.length;i++)if(t.indexOf(e[i])>-1)return Qt.Word;return Qt.Other};var e}wordAt(t){let{text:e,from:i,length:n}=this.doc.lineAt(t),r=this.charCategorizer(t),s=t-i,o=t-i;for(;s>0;){let t=y(e,s,!1);if(r(e.slice(t,s))!=Qt.Word)break;s=t}for(;o<n;){let t=y(e,o);if(r(e.slice(o,t))!=Qt.Word)break;o=t}return s==o?null:_.range(s+i,o+i)}}function wt(t,e,i={}){let n={};for(let e of t)for(let t of Object.keys(e)){let r=e[t],s=n[t];if(void 0===s)n[t]=r;else if(s===r||void 0===r);else{if(!Object.hasOwnProperty.call(i,t))throw new Error("Config merge conflict for field "+t);n[t]=i[t](s,r)}}for(let t in e)void 0===n[t]&&(n[t]=e[t]);return n}yt.allowMultipleSelections=et,yt.tabSize=E.define({combine:t=>t.length?t[0]:4}),yt.lineSeparator=it,yt.readOnly=ot,yt.phrases=E.define({compare(t,e){let i=Object.keys(t),n=Object.keys(e);return i.length==n.length&&i.every(i=>t[i]==e[i])}}),yt.languageData=tt,yt.changeFilter=nt,yt.transactionFilter=rt,yt.transactionExtender=st,U.reconfigure=ct.define();class bt{eq(t){return this==t}range(t,e=t){return kt.create(t,e,this)}}bt.prototype.startSide=bt.prototype.endSide=0,bt.prototype.point=!1,bt.prototype.mapMode=v.TrackDel;class kt{constructor(t,e,i){this.from=t,this.to=e,this.value=i}static create(t,e,i){return new kt(t,e,i)}}function vt(t,e){return t.from-e.from||t.value.startSide-e.value.startSide}class $t{constructor(t,e,i,n){this.from=t,this.to=e,this.value=i,this.maxPoint=n}get length(){return this.to[this.to.length-1]}findIndex(t,e,i,n=0){let r=i?this.to:this.from;for(let s=n,o=r.length;;){if(s==o)return s;let n=s+o>>1,a=r[n]-t||(i?this.value[n].endSide:this.value[n].startSide)-e;if(n==s)return a>=0?s:o;a>=0?o=n:s=n+1}}between(t,e,i,n){for(let r=this.findIndex(e,-1e9,!0),s=this.findIndex(i,1e9,!1,r);r<s;r++)if(!1===n(this.from[r]+t,this.to[r]+t,this.value[r]))return!1}map(t,e){let i=[],n=[],r=[],s=-1,o=-1;for(let a=0;a<this.value.length;a++){let l,h,c=this.value[a],O=this.from[a]+t,u=this.to[a]+t;if(O==u){let t=e.mapPos(O,c.startSide,c.mapMode);if(null==t)continue;if(l=h=t,c.startSide!=c.endSide&&(h=e.mapPos(O,c.endSide),h<l))continue}else if(l=e.mapPos(O,c.startSide),h=e.mapPos(u,c.endSide),l>h||l==h&&c.startSide>0&&c.endSide<=0)continue;(h-l||c.endSide-c.startSide)<0||(s<0&&(s=l),c.point&&(o=Math.max(o,h-l)),i.push(c),n.push(l-s),r.push(h-s))}return{mapped:i.length?new $t(n,r,i,o):null,pos:s}}}class Pt{constructor(t,e,i,n){this.chunkPos=t,this.chunk=e,this.nextLayer=i,this.maxPoint=n}static create(t,e,i,n){return new Pt(t,e,i,n)}get length(){let t=this.chunk.length-1;return t<0?0:Math.max(this.chunkEnd(t),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let t=this.nextLayer.size;for(let e of this.chunk)t+=e.value.length;return t}chunkEnd(t){return this.chunkPos[t]+this.chunk[t].length}update(t){let{add:e=[],sort:i=!1,filterFrom:n=0,filterTo:r=this.length}=t,s=t.filter;if(0==e.length&&!s)return this;if(i&&(e=e.slice().sort(vt)),this.isEmpty)return e.length?Pt.of(e):this;let o=new Xt(this,null,-1).goto(0),a=0,l=[],h=new Tt;for(;o.value||a<e.length;)if(a<e.length&&(o.from-e[a].from||o.startSide-e[a].value.startSide)>=0){let t=e[a++];h.addInner(t.from,t.to,t.value)||l.push(t)}else 1==o.rangeIndex&&o.chunkIndex<this.chunk.length&&(a==e.length||this.chunkEnd(o.chunkIndex)<e[a].from)&&(!s||n>this.chunkEnd(o.chunkIndex)||r<this.chunkPos[o.chunkIndex])&&h.addChunk(this.chunkPos[o.chunkIndex],this.chunk[o.chunkIndex])?o.nextChunk():((!s||n>o.to||r<o.from||s(o.from,o.to,o.value))&&(h.addInner(o.from,o.to,o.value)||l.push(kt.create(o.from,o.to,o.value))),o.next());return h.finishInner(this.nextLayer.isEmpty&&!l.length?Pt.empty:this.nextLayer.update({add:l,filter:s,filterFrom:n,filterTo:r}))}map(t){if(t.empty||this.isEmpty)return this;let e=[],i=[],n=-1;for(let r=0;r<this.chunk.length;r++){let s=this.chunkPos[r],o=this.chunk[r],a=t.touchesRange(s,s+o.length);if(!1===a)n=Math.max(n,o.maxPoint),e.push(o),i.push(t.mapPos(s));else if(!0===a){let{mapped:r,pos:a}=o.map(s,t);r&&(n=Math.max(n,r.maxPoint),e.push(r),i.push(a))}}let r=this.nextLayer.map(t);return 0==e.length?r:new Pt(i,e,r||Pt.empty,n)}between(t,e,i){if(!this.isEmpty){for(let n=0;n<this.chunk.length;n++){let r=this.chunkPos[n],s=this.chunk[n];if(e>=r&&t<=r+s.length&&!1===s.between(r,t-r,e-r,i))return}this.nextLayer.between(t,e,i)}}iter(t=0){return At.from([this]).goto(t)}get isEmpty(){return this.nextLayer==this}static iter(t,e=0){return At.from(t).goto(e)}static compare(t,e,i,n,r=-1){let s=t.filter(t=>t.maxPoint>0||!t.isEmpty&&t.maxPoint>=r),o=e.filter(t=>t.maxPoint>0||!t.isEmpty&&t.maxPoint>=r),a=Zt(s,o,i),l=new Mt(s,a,r),h=new Mt(o,a,r);i.iterGaps((t,e,i)=>Rt(l,t,h,e,i,n)),i.empty&&0==i.length&&Rt(l,0,h,0,0,n)}static eq(t,e,i=0,n){null==n&&(n=999999999);let r=t.filter(t=>!t.isEmpty&&e.indexOf(t)<0),s=e.filter(e=>!e.isEmpty&&t.indexOf(e)<0);if(r.length!=s.length)return!1;if(!r.length)return!0;let o=Zt(r,s),a=new Mt(r,o,0).goto(i),l=new Mt(s,o,0).goto(i);for(;;){if(a.to!=l.to||!_t(a.active,l.active)||a.point&&(!l.point||!a.point.eq(l.point)))return!1;if(a.to>n)return!0;a.next(),l.next()}}static spans(t,e,i,n,r=-1){let s=new Mt(t,null,r).goto(e),o=e,a=s.openStart;for(;;){let t=Math.min(s.to,i);if(s.point){let i=s.activeForPoint(s.to),r=s.pointFrom<e?i.length+1:s.point.startSide<0?i.length:Math.min(i.length,a);n.point(o,t,s.point,i,r,s.pointRank),a=Math.min(s.openEnd(t),i.length)}else t>o&&(n.span(o,t,s.active,a),a=s.openEnd(t));if(s.to>i)return a+(s.point&&s.to>i?1:0);o=s.to,s.next()}}static of(t,e=!1){let i=new Tt;for(let n of t instanceof kt?[t]:e?function(t){if(t.length>1)for(let e=t[0],i=1;i<t.length;i++){let n=t[i];if(vt(e,n)>0)return t.slice().sort(vt);e=n}return t}(t):t)i.add(n.from,n.to,n.value);return i.finish()}static join(t){if(!t.length)return Pt.empty;let e=t[t.length-1];for(let i=t.length-2;i>=0;i--)for(let n=t[i];n!=Pt.empty;n=n.nextLayer)e=new Pt(n.chunkPos,n.chunk,e,Math.max(n.maxPoint,e.maxPoint));return e}}Pt.empty=new Pt([],[],null,-1),Pt.empty.nextLayer=Pt.empty;class Tt{finishChunk(t){this.chunks.push(new $t(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,t&&(this.from=[],this.to=[],this.value=[])}constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}add(t,e,i){this.addInner(t,e,i)||(this.nextLayer||(this.nextLayer=new Tt)).add(t,e,i)}addInner(t,e,i){let n=t-this.lastTo||i.startSide-this.last.endSide;if(n<=0&&(t-this.lastFrom||i.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return!(n<0||(250==this.from.length&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=t),this.from.push(t-this.chunkStart),this.to.push(e-this.chunkStart),this.last=i,this.lastFrom=t,this.lastTo=e,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,e-t)),0))}addChunk(t,e){if((t-this.lastTo||e.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,e.maxPoint),this.chunks.push(e),this.chunkPos.push(t);let i=e.value.length-1;return this.last=e.value[i],this.lastFrom=e.from[i]+t,this.lastTo=e.to[i]+t,!0}finish(){return this.finishInner(Pt.empty)}finishInner(t){if(this.from.length&&this.finishChunk(!1),0==this.chunks.length)return t;let e=Pt.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(t):t,this.setMaxPoint);return this.from=null,e}}function Zt(t,e,i){let n=new Map;for(let e of t)for(let t=0;t<e.chunk.length;t++)e.chunk[t].maxPoint<=0&&n.set(e.chunk[t],e.chunkPos[t]);let r=new Set;for(let t of e)for(let e=0;e<t.chunk.length;e++){let s=n.get(t.chunk[e]);null==s||(i?i.mapPos(s):s)!=t.chunkPos[e]||(null==i?void 0:i.touchesRange(s,s+t.chunk[e].length))||r.add(t.chunk[e])}return r}class Xt{constructor(t,e,i,n=0){this.layer=t,this.skip=e,this.minPoint=i,this.rank=n}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(t,e=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(t,e,!1),this}gotoInner(t,e,i){for(;this.chunkIndex<this.layer.chunk.length;){let e=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(e)||this.layer.chunkEnd(this.chunkIndex)<t||e.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1}if(this.chunkIndex<this.layer.chunk.length){let n=this.layer.chunk[this.chunkIndex].findIndex(t-this.layer.chunkPos[this.chunkIndex],e,!0);(!i||this.rangeIndex<n)&&this.setRangeIndex(n)}this.next()}forward(t,e){(this.to-t||this.endSide-e)<0&&this.gotoInner(t,e,!0)}next(){for(;;){if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}{let t=this.layer.chunkPos[this.chunkIndex],e=this.layer.chunk[this.chunkIndex],i=t+e.from[this.rangeIndex];if(this.from=i,this.to=t+e.to[this.rangeIndex],this.value=e.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}}setRangeIndex(t){if(t==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=t}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(t){return this.from-t.from||this.startSide-t.startSide||this.rank-t.rank||this.to-t.to||this.endSide-t.endSide}}class At{constructor(t){this.heap=t}static from(t,e=null,i=-1){let n=[];for(let r=0;r<t.length;r++)for(let s=t[r];!s.isEmpty;s=s.nextLayer)s.maxPoint>=i&&n.push(new Xt(s,e,i,r));return 1==n.length?n[0]:new At(n)}get startSide(){return this.value?this.value.startSide:0}goto(t,e=-1e9){for(let i of this.heap)i.goto(t,e);for(let t=this.heap.length>>1;t>=0;t--)Ct(this.heap,t);return this.next(),this}forward(t,e){for(let i of this.heap)i.forward(t,e);for(let t=this.heap.length>>1;t>=0;t--)Ct(this.heap,t);(this.to-t||this.value.endSide-e)<0&&this.next()}next(){if(0==this.heap.length)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let t=this.heap[0];this.from=t.from,this.to=t.to,this.value=t.value,this.rank=t.rank,t.value&&t.next(),Ct(this.heap,0)}}}function Ct(t,e){for(let i=t[e];;){let n=1+(e<<1);if(n>=t.length)break;let r=t[n];if(n+1<t.length&&r.compare(t[n+1])>=0&&(r=t[n+1],n++),i.compare(r)<0)break;t[n]=i,t[e]=r,e=n}}class Mt{constructor(t,e,i){this.minPoint=i,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=At.from(t,e,i)}goto(t,e=-1e9){return this.cursor.goto(t,e),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=t,this.endSide=e,this.openStart=-1,this.next(),this}forward(t,e){for(;this.minActive>-1&&(this.activeTo[this.minActive]-t||this.active[this.minActive].endSide-e)<0;)this.removeActive(this.minActive);this.cursor.forward(t,e)}removeActive(t){zt(this.active,t),zt(this.activeTo,t),zt(this.activeRank,t),this.minActive=Et(this.active,this.activeTo)}addActive(t){let e=0,{value:i,to:n,rank:r}=this.cursor;for(;e<this.activeRank.length&&(r-this.activeRank[e]||n-this.activeTo[e])>0;)e++;Yt(this.active,e,i),Yt(this.activeTo,e,n),Yt(this.activeRank,e,r),t&&Yt(t,e,this.cursor.from),this.minActive=Et(this.active,this.activeTo)}next(){let t=this.to,e=this.point;this.point=null;let i=this.openStart<0?[]:null;for(;;){let n=this.minActive;if(n>-1&&(this.activeTo[n]-this.cursor.from||this.active[n].endSide-this.cursor.startSide)<0){if(this.activeTo[n]>t){this.to=this.activeTo[n],this.endSide=this.active[n].endSide;break}this.removeActive(n),i&&zt(i,n)}else{if(!this.cursor.value){this.to=this.endSide=1e9;break}if(this.cursor.from>t){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}{let t=this.cursor.value;if(t.point){if(!(e&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)){this.point=t,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=t.endSide,this.cursor.next(),this.forward(this.to,this.endSide);break}this.cursor.next()}else this.addActive(i),this.cursor.next()}}}if(i){this.openStart=0;for(let e=i.length-1;e>=0&&i[e]<t;e--)this.openStart++}}activeForPoint(t){if(!this.active.length)return this.active;let e=[];for(let i=this.active.length-1;i>=0&&!(this.activeRank[i]<this.pointRank);i--)(this.activeTo[i]>t||this.activeTo[i]==t&&this.active[i].endSide>=this.point.endSide)&&e.push(this.active[i]);return e.reverse()}openEnd(t){let e=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>t;i--)e++;return e}}function Rt(t,e,i,n,r,s){t.goto(e),i.goto(n);let o=n+r,a=n,l=n-e;for(;;){let e=t.to+l-i.to,n=e||t.endSide-i.endSide,r=n<0?t.to+l:i.to,h=Math.min(r,o);if(t.point||i.point?t.point&&i.point&&(t.point==i.point||t.point.eq(i.point))&&_t(t.activeForPoint(t.to),i.activeForPoint(i.to))||s.comparePoint(a,h,t.point,i.point):h>a&&!_t(t.active,i.active)&&s.compareRange(a,h,t.active,i.active),r>o)break;(e||t.openEnd!=i.openEnd)&&s.boundChange&&s.boundChange(r),a=r,n<=0&&t.next(),n>=0&&i.next()}}function _t(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(t[i]!=e[i]&&!t[i].eq(e[i]))return!1;return!0}function zt(t,e){for(let i=e,n=t.length-1;i<n;i++)t[i]=t[i+1];t.pop()}function Yt(t,e,i){for(let i=t.length-1;i>=e;i--)t[i+1]=t[i];t[e]=i}function Et(t,e){let i=-1,n=1e9;for(let r=0;r<e.length;r++)(e[r]-n||t[r].endSide-t[i].endSide)<0&&(i=r,n=e[r]);return i}function Vt(t,e,i=t.length){let n=0;for(let r=0;r<i&&r<t.length;)9==t.charCodeAt(r)?(n+=e-n%e,r++):(n++,r=y(t,r));return n}const qt="undefined"==typeof Symbol?"__ͼ":Symbol.for("ͼ"),Lt="undefined"==typeof Symbol?"__styleSet"+Math.floor(1e8*Math.random()):Symbol("styleSet"),Dt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:{};class Wt{constructor(t,e){this.rules=[];let{finish:i}=e||{};function n(t){return/^@/.test(t)?[t]:t.split(/,\s*/)}function r(t,e,s,o){let a=[],l=/^@(\w+)\b/.exec(t[0]),h=l&&"keyframes"==l[1];if(l&&null==e)return s.push(t[0]+";");for(let i in e){let o=e[i];if(/&/.test(i))r(i.split(/,\s*/).map(e=>t.map(t=>e.replace(/&/,t))).reduce((t,e)=>t.concat(e)),o,s);else if(o&&"object"==typeof o){if(!l)throw new RangeError("The value of a property ("+i+") should be a primitive value.");r(n(i),o,a,h)}else null!=o&&a.push(i.replace(/_.*/,"").replace(/[A-Z]/g,t=>"-"+t.toLowerCase())+": "+o+";")}(a.length||h)&&s.push((!i||l||o?t:t.map(i)).join(", ")+" {"+a.join(" ")+"}")}for(let e in t)r(n(e),t[e],this.rules)}getRules(){return this.rules.join("\n")}static newName(){let t=Dt[qt]||1;return Dt[qt]=t+1,"ͼ"+t.toString(36)}static mount(t,e,i){let n=t[Lt],r=i&&i.nonce;n?r&&n.setNonce(r):n=new Bt(t,r),n.mount(Array.isArray(e)?e:[e],t)}}let jt=new Map;class Bt{constructor(t,e){let i=t.ownerDocument||t,n=i.defaultView;if(!t.head&&t.adoptedStyleSheets&&n.CSSStyleSheet){let e=jt.get(i);if(e)return t[Lt]=e;this.sheet=new n.CSSStyleSheet,jt.set(i,this)}else this.styleTag=i.createElement("style"),e&&this.styleTag.setAttribute("nonce",e);this.modules=[],t[Lt]=this}mount(t,e){let i=this.sheet,n=0,r=0;for(let e=0;e<t.length;e++){let s=t[e],o=this.modules.indexOf(s);if(o<r&&o>-1&&(this.modules.splice(o,1),r--,o=-1),-1==o){if(this.modules.splice(r++,0,s),i)for(let t=0;t<s.rules.length;t++)i.insertRule(s.rules[t],n++)}else{for(;r<o;)n+=this.modules[r++].rules.length;n+=s.rules.length,r++}}if(i)e.adoptedStyleSheets.indexOf(this.sheet)<0&&(e.adoptedStyleSheets=[this.sheet,...e.adoptedStyleSheets]);else{let t="";for(let e=0;e<this.modules.length;e++)t+=this.modules[e].getRules()+"\n";this.styleTag.textContent=t;let i=e.head||e;this.styleTag.parentNode!=i&&i.insertBefore(this.styleTag,i.firstChild)}}setNonce(t){this.styleTag&&this.styleTag.getAttribute("nonce")!=t&&this.styleTag.setAttribute("nonce",t)}}for(var It={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},Gt={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Nt="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),Ut="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),Ht=0;Ht<10;Ht++)It[48+Ht]=It[96+Ht]=String(Ht);for(Ht=1;Ht<=24;Ht++)It[Ht+111]="F"+Ht;for(Ht=65;Ht<=90;Ht++)It[Ht]=String.fromCharCode(Ht+32),Gt[Ht]=String.fromCharCode(Ht);for(var Ft in It)Gt.hasOwnProperty(Ft)||(Gt[Ft]=It[Ft]);function Kt(t){let e;return e=11==t.nodeType?t.getSelection?t:t.ownerDocument:t,e.getSelection()}function Jt(t,e){return!!e&&(t==e||t.contains(1!=e.nodeType?e.parentNode:e))}function te(t,e){if(!e.anchorNode)return!1;try{return Jt(t,e.anchorNode)}catch(t){return!1}}function ee(t){return 3==t.nodeType?fe(t,0,t.nodeValue.length).getClientRects():1==t.nodeType?t.getClientRects():[]}function ie(t,e,i,n){return!!i&&(se(t,e,i,n,-1)||se(t,e,i,n,1))}function ne(t){for(var e=0;;e++)if(!(t=t.previousSibling))return e}function re(t){return 1==t.nodeType&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(t.nodeName)}function se(t,e,i,n,r){for(;;){if(t==i&&e==n)return!0;if(e==(r<0?0:oe(t))){if("DIV"==t.nodeName)return!1;let i=t.parentNode;if(!i||1!=i.nodeType)return!1;e=ne(t)+(r<0?0:1),t=i}else{if(1!=t.nodeType)return!1;if(1==(t=t.childNodes[e+(r<0?-1:0)]).nodeType&&"false"==t.contentEditable)return!1;e=r<0?oe(t):0}}}function oe(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function ae(t,e){let i=e?t.left:t.right;return{left:i,right:i,top:t.top,bottom:t.bottom}}function le(t){let e=t.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:t.innerWidth,top:0,bottom:t.innerHeight}}function he(t,e){let i=e.width/t.offsetWidth,n=e.height/t.offsetHeight;return(i>.995&&i<1.005||!isFinite(i)||Math.abs(e.width-t.offsetWidth)<1)&&(i=1),(n>.995&&n<1.005||!isFinite(n)||Math.abs(e.height-t.offsetHeight)<1)&&(n=1),{scaleX:i,scaleY:n}}class ce{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(t){return this.anchorNode==t.anchorNode&&this.anchorOffset==t.anchorOffset&&this.focusNode==t.focusNode&&this.focusOffset==t.focusOffset}setRange(t){let{anchorNode:e,focusNode:i}=t;this.set(e,Math.min(t.anchorOffset,e?oe(e):0),i,Math.min(t.focusOffset,i?oe(i):0))}set(t,e,i,n){this.anchorNode=t,this.anchorOffset=e,this.focusNode=i,this.focusOffset=n}}let Oe,ue=null;function de(t){if(t.setActive)return t.setActive();if(ue)return t.focus(ue);let e=[];for(let i=t;i&&(e.push(i,i.scrollTop,i.scrollLeft),i!=i.ownerDocument);i=i.parentNode);if(t.focus(null==ue?{get preventScroll(){return ue={preventScroll:!0},!0}}:void 0),!ue){ue=!1;for(let t=0;t<e.length;){let i=e[t++],n=e[t++],r=e[t++];i.scrollTop!=n&&(i.scrollTop=n),i.scrollLeft!=r&&(i.scrollLeft=r)}}}function fe(t,e,i=e){let n=Oe||(Oe=document.createRange());return n.setEnd(t,i),n.setStart(t,e),n}function pe(t,e,i,n){let r={key:e,code:e,keyCode:i,which:i,cancelable:!0};n&&({altKey:r.altKey,ctrlKey:r.ctrlKey,shiftKey:r.shiftKey,metaKey:r.metaKey}=n);let s=new KeyboardEvent("keydown",r);s.synthetic=!0,t.dispatchEvent(s);let o=new KeyboardEvent("keyup",r);return o.synthetic=!0,t.dispatchEvent(o),s.defaultPrevented||o.defaultPrevented}function ge(t){for(;t.attributes.length;)t.removeAttributeNode(t.attributes[0])}function me(t){return t.scrollTop>Math.max(1,t.scrollHeight-t.clientHeight-4)}function Qe(t,e){for(let i=t,n=e;;){if(3==i.nodeType&&n>0)return{node:i,offset:n};if(1==i.nodeType&&n>0){if("false"==i.contentEditable)return null;i=i.childNodes[n-1],n=oe(i)}else{if(!i.parentNode||re(i))return null;n=ne(i),i=i.parentNode}}}function Se(t,e){for(let i=t,n=e;;){if(3==i.nodeType&&n<i.nodeValue.length)return{node:i,offset:n};if(1==i.nodeType&&n<i.childNodes.length){if("false"==i.contentEditable)return null;i=i.childNodes[n],n=0}else{if(!i.parentNode||re(i))return null;n=ne(i)+1,i=i.parentNode}}}class xe{constructor(t,e,i=!0){this.node=t,this.offset=e,this.precise=i}static before(t,e){return new xe(t.parentNode,ne(t),e)}static after(t,e){return new xe(t.parentNode,ne(t)+1,e)}}const ye=[];class we{constructor(){this.parent=null,this.dom=null,this.flags=2}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(t){let e=this.posAtStart;for(let i of this.children){if(i==t)return e;e+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(t){return this.posBefore(t)+t.length}sync(t,e){if(2&this.flags){let i,n=this.dom,r=null;for(let s of this.children){if(7&s.flags){if(!s.dom&&(i=r?r.nextSibling:n.firstChild)){let t=we.get(i);(!t||!t.parent&&t.canReuseDOM(s))&&s.reuseDOM(i)}s.sync(t,e),s.flags&=-8}if(i=r?r.nextSibling:n.firstChild,e&&!e.written&&e.node==n&&i!=s.dom&&(e.written=!0),s.dom.parentNode==n)for(;i&&i!=s.dom;)i=be(i);else n.insertBefore(s.dom,i);r=s.dom}for(i=r?r.nextSibling:n.firstChild,i&&e&&e.node==n&&(e.written=!0);i;)i=be(i)}else if(1&this.flags)for(let i of this.children)7&i.flags&&(i.sync(t,e),i.flags&=-8)}reuseDOM(t){}localPosFromDOM(t,e){let i;if(t==this.dom)i=this.dom.childNodes[e];else{let n=0==oe(t)?0:0==e?-1:1;for(;;){let e=t.parentNode;if(e==this.dom)break;0==n&&e.firstChild!=e.lastChild&&(n=t==e.firstChild?-1:1),t=e}i=n<0?t:t.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!we.get(i);)i=i.nextSibling;if(!i)return this.length;for(let t=0,e=0;;t++){let n=this.children[t];if(n.dom==i)return e;e+=n.length+n.breakAfter}}domBoundsAround(t,e,i=0){let n=-1,r=-1,s=-1,o=-1;for(let a=0,l=i,h=i;a<this.children.length;a++){let i=this.children[a],c=l+i.length;if(l<t&&c>e)return i.domBoundsAround(t,e,l);if(c>=t&&-1==n&&(n=a,r=l),l>e&&i.dom.parentNode==this.dom){s=a,o=h;break}h=c,l=c+i.breakAfter}return{from:r,to:o<0?i+this.length:o,startDOM:(n?this.children[n-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:s<this.children.length&&s>=0?this.children[s].dom:null}}markDirty(t=!1){this.flags|=2,this.markParentsDirty(t)}markParentsDirty(t){for(let e=this.parent;e;e=e.parent){if(t&&(e.flags|=2),1&e.flags)return;e.flags|=1,t=!1}}setParent(t){this.parent!=t&&(this.parent=t,7&this.flags&&this.markParentsDirty(!0))}setDOM(t){this.dom!=t&&(this.dom&&(this.dom.cmView=null),this.dom=t,t.cmView=this)}get rootView(){for(let t=this;;){let e=t.parent;if(!e)return t;t=e}}replaceChildren(t,e,i=ye){this.markDirty();for(let n=t;n<e;n++){let t=this.children[n];t.parent==this&&i.indexOf(t)<0&&t.destroy()}i.length<250?this.children.splice(t,e-t,...i):this.children=[].concat(this.children.slice(0,t),i,this.children.slice(e));for(let t=0;t<i.length;t++)i[t].setParent(this)}ignoreMutation(t){return!1}ignoreEvent(t){return!1}childCursor(t=this.length){return new ke(this.children,t,this.children.length)}childPos(t,e=1){return this.childCursor().findPos(t,e)}toString(){let t=this.constructor.name.replace("View","");return t+(this.children.length?"("+this.children.join()+")":this.length?"["+("Text"==t?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(t){return t.cmView}get isEditable(){return!0}get isWidget(){return!1}get isHidden(){return!1}merge(t,e,i,n,r,s){return!1}become(t){return!1}canReuseDOM(t){return t.constructor==this.constructor&&!(8&(this.flags|t.flags))}getSide(){return 0}destroy(){for(let t of this.children)t.parent==this&&t.destroy();this.parent=null}}function be(t){let e=t.nextSibling;return t.parentNode.removeChild(t),e}we.prototype.breakAfter=0;class ke{constructor(t,e,i){this.children=t,this.pos=e,this.i=i,this.off=0}findPos(t,e=1){for(;;){if(t>this.pos||t==this.pos&&(e>0||0==this.i||this.children[this.i-1].breakAfter))return this.off=t-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function ve(t,e,i,n,r,s,o,a,l){let{children:h}=t,c=h.length?h[e]:null,O=s.length?s[s.length-1]:null,u=O?O.breakAfter:o;if(!(e==n&&c&&!o&&!u&&s.length<2&&c.merge(i,r,s.length?O:null,0==i,a,l))){if(n<h.length){let t=h[n];t&&(r<t.length||t.breakAfter&&(null==O?void 0:O.breakAfter))?(e==n&&(t=t.split(r),r=0),!u&&O&&t.merge(0,r,O,!0,0,l)?s[s.length-1]=t:((r||t.children.length&&!t.children[0].length)&&t.merge(0,r,null,!1,0,l),s.push(t))):(null==t?void 0:t.breakAfter)&&(O?O.breakAfter=1:o=1),n++}for(c&&(c.breakAfter=o,i>0&&(!o&&s.length&&c.merge(i,c.length,s[0],!1,a,0)?c.breakAfter=s.shift().breakAfter:(i<c.length||c.children.length&&0==c.children[c.children.length-1].length)&&c.merge(i,c.length,null,!1,a,0),e++));e<n&&s.length;)if(h[n-1].become(s[s.length-1]))n--,s.pop(),l=s.length?0:a;else{if(!h[e].become(s[0]))break;e++,s.shift(),a=s.length?0:l}!s.length&&e&&n<h.length&&!h[e-1].breakAfter&&h[n].merge(0,0,h[e-1],!1,a,l)&&e--,(e<n||s.length)&&t.replaceChildren(e,n,s)}}function $e(t,e,i,n,r,s){let o=t.childCursor(),{i:a,off:l}=o.findPos(i,1),{i:h,off:c}=o.findPos(e,-1),O=e-i;for(let t of n)O+=t.length;t.length+=O,ve(t,h,c,a,l,n,0,r,s)}let Pe="undefined"!=typeof navigator?navigator:{userAgent:"",vendor:"",platform:""},Te="undefined"!=typeof document?document:{documentElement:{style:{}}};const Ze=/Edge\/(\d+)/.exec(Pe.userAgent),Xe=/MSIE \d/.test(Pe.userAgent),Ae=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(Pe.userAgent),Ce=!!(Xe||Ae||Ze),Me=!Ce&&/gecko\/(\d+)/i.test(Pe.userAgent),Re=!Ce&&/Chrome\/(\d+)/.exec(Pe.userAgent),_e="webkitFontSmoothing"in Te.documentElement.style,ze=!Ce&&/Apple Computer/.test(Pe.vendor),Ye=ze&&(/Mobile\/\w+/.test(Pe.userAgent)||Pe.maxTouchPoints>2);var Ee={mac:Ye||/Mac/.test(Pe.platform),windows:/Win/.test(Pe.platform),linux:/Linux|X11/.test(Pe.platform),ie:Ce,ie_version:Xe?Te.documentMode||6:Ae?+Ae[1]:Ze?+Ze[1]:0,gecko:Me,gecko_version:Me?+(/Firefox\/(\d+)/.exec(Pe.userAgent)||[0,0])[1]:0,chrome:!!Re,chrome_version:Re?+Re[1]:0,ios:Ye,android:/Android\b/.test(Pe.userAgent),webkit:_e,safari:ze,webkit_version:_e?+(/\bAppleWebKit\/(\d+)/.exec(Pe.userAgent)||[0,0])[1]:0,tabSize:null!=Te.documentElement.style.tabSize?"tab-size":"-moz-tab-size"};class Ve extends we{constructor(t){super(),this.text=t}get length(){return this.text.length}createDOM(t){this.setDOM(t||document.createTextNode(this.text))}sync(t,e){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(e&&e.node==this.dom&&(e.written=!0),this.dom.nodeValue=this.text)}reuseDOM(t){3==t.nodeType&&this.createDOM(t)}merge(t,e,i){return!(8&this.flags||i&&(!(i instanceof Ve)||this.length-(e-t)+i.length>256||8&i.flags)||(this.text=this.text.slice(0,t)+(i?i.text:"")+this.text.slice(e),this.markDirty(),0))}split(t){let e=new Ve(this.text.slice(t));return this.text=this.text.slice(0,t),this.markDirty(),e.flags|=8&this.flags,e}localPosFromDOM(t,e){return t==this.dom?e:e?this.text.length:0}domAtPos(t){return new xe(this.dom,t)}domBoundsAround(t,e,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(t,e){return function(t,e,i){let n=t.nodeValue.length;e>n&&(e=n);let r=e,s=e,o=0;0==e&&i<0||e==n&&i>=0?Ee.chrome||Ee.gecko||(e?(r--,o=1):s<n&&(s++,o=-1)):i<0?r--:s<n&&s++;let a=fe(t,r,s).getClientRects();if(!a.length)return null;let l=a[(o?o<0:i>=0)?0:a.length-1];return Ee.safari&&!o&&0==l.width&&(l=Array.prototype.find.call(a,t=>t.width)||l),o?ae(l,o<0):l||null}(this.dom,t,e)}}class qe extends we{constructor(t,e=[],i=0){super(),this.mark=t,this.children=e,this.length=i;for(let t of e)t.setParent(this)}setAttrs(t){if(ge(t),this.mark.class&&(t.className=this.mark.class),this.mark.attrs)for(let e in this.mark.attrs)t.setAttribute(e,this.mark.attrs[e]);return t}canReuseDOM(t){return super.canReuseDOM(t)&&!(8&(this.flags|t.flags))}reuseDOM(t){t.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(t),this.flags|=6)}sync(t,e){this.dom?4&this.flags&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(t,e)}merge(t,e,i,n,r,s){return!(i&&(!(i instanceof qe&&i.mark.eq(this.mark))||t&&r<=0||e<this.length&&s<=0)||($e(this,t,e,i?i.children.slice():[],r-1,s-1),this.markDirty(),0))}split(t){let e=[],i=0,n=-1,r=0;for(let s of this.children){let o=i+s.length;o>t&&e.push(i<t?s.split(t-i):s),n<0&&i>=t&&(n=r),i=o,r++}let s=this.length-t;return this.length=t,n>-1&&(this.children.length=n,this.markDirty()),new qe(this.mark,e,s)}domAtPos(t){return We(this,t)}coordsAt(t,e){return Be(this,t,e)}}class Le extends we{static create(t,e,i){return new Le(t,e,i)}constructor(t,e,i){super(),this.widget=t,this.length=e,this.side=i,this.prevWidget=null}split(t){let e=Le.create(this.widget,this.length-t,this.side);return this.length-=t,e}sync(t){this.dom&&this.widget.updateDOM(this.dom,t)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}getSide(){return this.side}merge(t,e,i,n,r,s){return!(i&&(!(i instanceof Le&&this.widget.compare(i.widget))||t>0&&r<=0||e<this.length&&s<=0)||(this.length=t+(i?i.length:0)+(this.length-e),0))}become(t){return t instanceof Le&&t.side==this.side&&this.widget.constructor==t.widget.constructor&&(this.widget.compare(t.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get overrideDOMText(){if(0==this.length)return O.empty;let t=this;for(;t.parent;)t=t.parent;let{view:e}=t,i=e&&e.state.doc,n=this.posAtStart;return i?i.slice(n,n+this.length):O.empty}domAtPos(t){return(this.length?0==t:this.side>0)?xe.before(this.dom):xe.after(this.dom,t==this.length)}domBoundsAround(){return null}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);if(i)return i;let n=this.dom.getClientRects(),r=null;if(!n.length)return null;let s=this.side?this.side<0:t>0;for(let e=s?n.length-1:0;r=n[e],!(t>0?0==e:e==n.length-1||r.top<r.bottom);e+=s?-1:1);return ae(r,!s)}get isEditable(){return!1}get isWidget(){return!0}get isHidden(){return this.widget.isHidden}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class De extends we{constructor(t){super(),this.side=t}get length(){return 0}merge(){return!1}become(t){return t instanceof De&&t.side==this.side}split(){return new De(this.side)}sync(){if(!this.dom){let t=document.createElement("img");t.className="cm-widgetBuffer",t.setAttribute("aria-hidden","true"),this.setDOM(t)}}getSide(){return this.side}domAtPos(t){return this.side>0?xe.before(this.dom):xe.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(t){return this.dom.getBoundingClientRect()}get overrideDOMText(){return O.empty}get isHidden(){return!0}}function We(t,e){let i=t.dom,{children:n}=t,r=0;for(let t=0;r<n.length;r++){let s=n[r],o=t+s.length;if(!(o==t&&s.getSide()<=0)){if(e>t&&e<o&&s.dom.parentNode==i)return s.domAtPos(e-t);if(e<=t)break;t=o}}for(let t=r;t>0;t--){let e=n[t-1];if(e.dom.parentNode==i)return e.domAtPos(e.length)}for(let t=r;t<n.length;t++){let e=n[t];if(e.dom.parentNode==i)return e.domAtPos(0)}return new xe(i,0)}function je(t,e,i){let n,{children:r}=t;i>0&&e instanceof qe&&r.length&&(n=r[r.length-1])instanceof qe&&n.mark.eq(e.mark)?je(n,e.children[0],i-1):(r.push(e),e.setParent(t)),t.length+=e.length}function Be(t,e,i){let n=null,r=-1,s=null,o=-1;!function t(e,a){for(let l=0,h=0;l<e.children.length&&h<=a;l++){let c=e.children[l],O=h+c.length;O>=a&&(c.children.length?t(c,a-h):(!s||s.isHidden&&(i>0||Ie(s,c)))&&(O>a||h==O&&c.getSide()>0)?(s=c,o=a-h):(h<a||h==O&&c.getSide()<0&&!c.isHidden)&&(n=c,r=a-h)),h=O}}(t,e);let a=(i<0?n:s)||n||s;return a?a.coordsAt(Math.max(0,a==n?r:o),i):function(t){let e=t.dom.lastChild;if(!e)return t.dom.getBoundingClientRect();let i=ee(e);return i[i.length-1]||null}(t)}function Ie(t,e){let i=t.coordsAt(0,1),n=e.coordsAt(0,1);return i&&n&&n.top<i.bottom}function Ge(t,e){for(let i in t)"class"==i&&e.class?e.class+=" "+t.class:"style"==i&&e.style?e.style+=";"+t.style:e[i]=t[i];return e}Ve.prototype.children=Le.prototype.children=De.prototype.children=ye;const Ne=Object.create(null);function Ue(t,e,i){if(t==e)return!0;t||(t=Ne),e||(e=Ne);let n=Object.keys(t),r=Object.keys(e);if(n.length-(i&&n.indexOf(i)>-1?1:0)!=r.length-(i&&r.indexOf(i)>-1?1:0))return!1;for(let s of n)if(s!=i&&(-1==r.indexOf(s)||t[s]!==e[s]))return!1;return!0}function He(t,e,i){let n=!1;if(e)for(let r in e)i&&r in i||(n=!0,"style"==r?t.style.cssText="":t.removeAttribute(r));if(i)for(let r in i)e&&e[r]==i[r]||(n=!0,"style"==r?t.style.cssText=i[r]:t.setAttribute(r,i[r]));return n}function Fe(t){let e=Object.create(null);for(let i=0;i<t.attributes.length;i++){let n=t.attributes[i];e[n.name]=n.value}return e}class Ke{eq(t){return!1}updateDOM(t,e){return!1}compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}get estimatedHeight(){return-1}get lineBreaks(){return 0}ignoreEvent(t){return!0}coordsAt(t,e,i){return null}get isHidden(){return!1}get editable(){return!1}destroy(t){}}var Je=function(t){return t[t.Text=0]="Text",t[t.WidgetBefore=1]="WidgetBefore",t[t.WidgetAfter=2]="WidgetAfter",t[t.WidgetRange=3]="WidgetRange",t}(Je||(Je={}));class ti extends bt{constructor(t,e,i,n){super(),this.startSide=t,this.endSide=e,this.widget=i,this.spec=n}get heightRelevant(){return!1}static mark(t){return new ei(t)}static widget(t){let e=Math.max(-1e4,Math.min(1e4,t.side||0)),i=!!t.block;return e+=i&&!t.inlineOrder?e>0?3e8:-4e8:e>0?1e8:-1e8,new ni(t,e,e,i,t.widget||null,!1)}static replace(t){let e,i,n=!!t.block;if(t.isBlockGap)e=-5e8,i=4e8;else{let{start:r,end:s}=ri(t,n);e=(r?n?-3e8:-1:5e8)-1,i=1+(s?n?2e8:1:-6e8)}return new ni(t,e,i,n,t.widget||null,!0)}static line(t){return new ii(t)}static set(t,e=!1){return Pt.of(t,e)}hasHeight(){return!!this.widget&&this.widget.estimatedHeight>-1}}ti.none=Pt.empty;class ei extends ti{constructor(t){let{start:e,end:i}=ri(t);super(e?-1:5e8,i?1:-6e8,null,t),this.tagName=t.tagName||"span",this.class=t.class||"",this.attrs=t.attributes||null}eq(t){var e,i;return this==t||t instanceof ei&&this.tagName==t.tagName&&(this.class||(null===(e=this.attrs)||void 0===e?void 0:e.class))==(t.class||(null===(i=t.attrs)||void 0===i?void 0:i.class))&&Ue(this.attrs,t.attrs,"class")}range(t,e=t){if(t>=e)throw new RangeError("Mark decorations may not be empty");return super.range(t,e)}}ei.prototype.point=!1;class ii extends ti{constructor(t){super(-2e8,-2e8,null,t)}eq(t){return t instanceof ii&&this.spec.class==t.spec.class&&Ue(this.spec.attributes,t.spec.attributes)}range(t,e=t){if(e!=t)throw new RangeError("Line decoration ranges must be zero-length");return super.range(t,e)}}ii.prototype.mapMode=v.TrackBefore,ii.prototype.point=!0;class ni extends ti{constructor(t,e,i,n,r,s){super(e,i,r,t),this.block=n,this.isReplace=s,this.mapMode=n?e<=0?v.TrackBefore:v.TrackAfter:v.TrackDel}get type(){return this.startSide!=this.endSide?Je.WidgetRange:this.startSide<=0?Je.WidgetBefore:Je.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&(this.widget.estimatedHeight>=5||this.widget.lineBreaks>0)}eq(t){return t instanceof ni&&((e=this.widget)==(i=t.widget)||!!(e&&i&&e.compare(i)))&&this.block==t.block&&this.startSide==t.startSide&&this.endSide==t.endSide;var e,i}range(t,e=t){if(this.isReplace&&(t>e||t==e&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&e!=t)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(t,e)}}function ri(t,e=!1){let{inclusiveStart:i,inclusiveEnd:n}=t;return null==i&&(i=t.inclusive),null==n&&(n=t.inclusive),{start:null!=i?i:e,end:null!=n?n:e}}function si(t,e,i,n=0){let r=i.length-1;r>=0&&i[r]+n>=t?i[r]=Math.max(i[r],e):i.push(t,e)}ni.prototype.point=!0;class oi extends we{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(t,e,i,n,r,s){if(i){if(!(i instanceof oi))return!1;this.dom||i.transferDOM(this)}return n&&this.setDeco(i?i.attrs:null),$e(this,t,e,i?i.children.slice():[],r,s),!0}split(t){let e=new oi;if(e.breakAfter=this.breakAfter,0==this.length)return e;let{i,off:n}=this.childPos(t);n&&(e.append(this.children[i].split(n),0),this.children[i].merge(n,this.children[i].length,null,!1,0,0),i++);for(let t=i;t<this.children.length;t++)e.append(this.children[t],0);for(;i>0&&0==this.children[i-1].length;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=t,e}transferDOM(t){this.dom&&(this.markDirty(),t.setDOM(this.dom),t.prevAttrs=void 0===this.prevAttrs?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(t){Ue(this.attrs,t)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=t)}append(t,e){je(this,t,e)}addLineDeco(t){let e=t.spec.attributes,i=t.spec.class;e&&(this.attrs=Ge(e,this.attrs||{})),i&&(this.attrs=Ge({class:i},this.attrs||{}))}domAtPos(t){return We(this,t)}reuseDOM(t){"DIV"==t.nodeName&&(this.setDOM(t),this.flags|=6)}sync(t,e){var i;this.dom?4&this.flags&&(ge(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),void 0!==this.prevAttrs&&(He(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(t,e);let n=this.dom.lastChild;for(;n&&we.get(n)instanceof qe;)n=n.lastChild;if(!(n&&this.length&&("BR"==n.nodeName||0!=(null===(i=we.get(n))||void 0===i?void 0:i.isEditable)||Ee.ios&&this.children.some(t=>t instanceof Ve)))){let t=document.createElement("BR");t.cmIgnore=!0,this.dom.appendChild(t)}}measureTextSize(){if(0==this.children.length||this.length>20)return null;let t,e=0;for(let i of this.children){if(!(i instanceof Ve)||/[^ -~]/.test(i.text))return null;let n=ee(i.dom);if(1!=n.length)return null;e+=n[0].width,t=n[0].height}return e?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:e/this.length,textHeight:t}:null}coordsAt(t,e){let i=Be(this,t,e);if(!this.children.length&&i&&this.parent){let{heightOracle:t}=this.parent.view.viewState,e=i.bottom-i.top;if(Math.abs(e-t.lineHeight)<2&&t.textHeight<e){let n=(e-t.textHeight)/2;return{top:i.top+n,bottom:i.bottom-n,left:i.left,right:i.left}}}return i}become(t){return t instanceof oi&&0==this.children.length&&0==t.children.length&&Ue(this.attrs,t.attrs)&&this.breakAfter==t.breakAfter}covers(){return!0}static find(t,e){for(let i=0,n=0;i<t.children.length;i++){let r=t.children[i],s=n+r.length;if(s>=e){if(r instanceof oi)return r;if(s>e)break}n=s+r.breakAfter}return null}}class ai extends we{constructor(t,e,i){super(),this.widget=t,this.length=e,this.deco=i,this.breakAfter=0,this.prevWidget=null}merge(t,e,i,n,r,s){return!(i&&(!(i instanceof ai&&this.widget.compare(i.widget))||t>0&&r<=0||e<this.length&&s<=0)||(this.length=t+(i?i.length:0)+(this.length-e),0))}domAtPos(t){return 0==t?xe.before(this.dom):xe.after(this.dom,t==this.length)}split(t){let e=this.length-t;this.length=t;let i=new ai(this.widget,e,this.deco);return i.breakAfter=this.breakAfter,i}get children(){return ye}sync(t){this.dom&&this.widget.updateDOM(this.dom,t)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):O.empty}domBoundsAround(){return null}become(t){return t instanceof ai&&t.widget.constructor==this.widget.constructor&&(t.widget.compare(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,this.deco=t.deco,this.breakAfter=t.breakAfter,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get isEditable(){return!1}get isWidget(){return!0}coordsAt(t,e){return this.widget.coordsAt(this.dom,t,e)||(this.widget instanceof li?null:ae(this.dom.getBoundingClientRect(),this.length?0==t:e<=0))}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}covers(t){let{startSide:e,endSide:i}=this.deco;return e!=i&&(t<0?e<0:i>0)}}class li extends Ke{constructor(t){super(),this.height=t}toDOM(){let t=document.createElement("div");return t.className="cm-gap",this.updateDOM(t),t}eq(t){return t.height==this.height}updateDOM(t){return t.style.height=this.height+"px",!0}get editable(){return!0}get estimatedHeight(){return this.height}ignoreEvent(){return!1}}class hi{constructor(t,e,i,n){this.doc=t,this.pos=e,this.end=i,this.disallowBlockEffectsFor=n,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.bufferMarks=[],this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=t.iter(),this.skip=e}posCovered(){if(0==this.content.length)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let t=this.content[this.content.length-1];return!(t.breakAfter||t instanceof ai&&t.deco.endSide<0)}getLine(){return this.curLine||(this.content.push(this.curLine=new oi),this.atCursorPos=!0),this.curLine}flushBuffer(t=this.bufferMarks){this.pendingBuffer&&(this.curLine.append(ci(new De(-1),t),t.length),this.pendingBuffer=0)}addBlockWidget(t){this.flushBuffer(),this.curLine=null,this.content.push(t)}finish(t){this.pendingBuffer&&t<=this.bufferMarks.length?this.flushBuffer():this.pendingBuffer=0,this.posCovered()||t&&this.content.length&&this.content[this.content.length-1]instanceof ai||this.getLine()}buildText(t,e,i){for(;t>0;){if(this.textOff==this.text.length){let{value:e,lineBreak:i,done:n}=this.cursor.next(this.skip);if(this.skip=0,n)throw new Error("Ran out of text content when drawing inline views");if(i){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer(),this.curLine=null,this.atCursorPos=!0,t--;continue}this.text=e,this.textOff=0}let n=Math.min(this.text.length-this.textOff,t),r=Math.min(n,512);this.flushBuffer(e.slice(e.length-i)),this.getLine().append(ci(new Ve(this.text.slice(this.textOff,this.textOff+r)),e),i),this.atCursorPos=!0,this.textOff+=r,t-=r,i=n<=r?0:e.length}}span(t,e,i,n){this.buildText(e-t,i,n),this.pos=e,this.openStart<0&&(this.openStart=n)}point(t,e,i,n,r,s){if(this.disallowBlockEffectsFor[s]&&i instanceof ni){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(e>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let o=e-t;if(i instanceof ni)if(i.block)i.startSide>0&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new ai(i.widget||Oi.block,o,i));else{let s=Le.create(i.widget||Oi.inline,o,o?0:i.startSide),a=this.atCursorPos&&!s.isEditable&&r<=n.length&&(t<e||i.startSide>0),l=!s.isEditable&&(t<e||r>n.length||i.startSide<=0),h=this.getLine();2!=this.pendingBuffer||a||s.isEditable||(this.pendingBuffer=0),this.flushBuffer(n),a&&(h.append(ci(new De(1),n),r),r=n.length+Math.max(0,r-n.length)),h.append(ci(s,n),r),this.atCursorPos=l,this.pendingBuffer=l?t<e||r>n.length?1:2:0,this.pendingBuffer&&(this.bufferMarks=n.slice())}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);o&&(this.textOff+o<=this.text.length?this.textOff+=o:(this.skip+=o-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=e),this.openStart<0&&(this.openStart=r)}static build(t,e,i,n,r){let s=new hi(t,e,i,r);return s.openEnd=Pt.spans(n,e,i,s),s.openStart<0&&(s.openStart=s.openEnd),s.finish(s.openEnd),s}}function ci(t,e){for(let i of e)t=new qe(i,[t],t.length);return t}class Oi extends Ke{constructor(t){super(),this.tag=t}eq(t){return t.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(t){return t.nodeName.toLowerCase()==this.tag}get isHidden(){return!0}}Oi.inline=new Oi("span"),Oi.block=new Oi("div");var ui=function(t){return t[t.LTR=0]="LTR",t[t.RTL=1]="RTL",t}(ui||(ui={}));const di=ui.LTR,fi=ui.RTL;function pi(t){let e=[];for(let i=0;i<t.length;i++)e.push(1<<+t[i]);return e}const gi=pi("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),mi=pi("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),Qi=Object.create(null),Si=[];for(let t of["()","[]","{}"]){let e=t.charCodeAt(0),i=t.charCodeAt(1);Qi[e]=i,Qi[i]=-e}function xi(t){return t<=247?gi[t]:1424<=t&&t<=1524?2:1536<=t&&t<=1785?mi[t-1536]:1774<=t&&t<=2220?4:8192<=t&&t<=8204?256:64336<=t&&t<=65023?4:1}const yi=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class wi{get dir(){return this.level%2?fi:di}constructor(t,e,i){this.from=t,this.to=e,this.level=i}side(t,e){return this.dir==e==t?this.to:this.from}forward(t,e){return t==(this.dir==e)}static find(t,e,i,n){let r=-1;for(let s=0;s<t.length;s++){let o=t[s];if(o.from<=e&&o.to>=e){if(o.level==i)return s;(r<0||(0!=n?n<0?o.from<e:o.to>e:t[r].level>o.level))&&(r=s)}}if(r<0)throw new RangeError("Index out of range");return r}}function bi(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let n=t[i],r=e[i];if(n.from!=r.from||n.to!=r.to||n.direction!=r.direction||!bi(n.inner,r.inner))return!1}return!0}const ki=[];function vi(t,e,i,n,r,s,o){let a=n%2?2:1;if(n%2==r%2)for(let l=e,h=0;l<i;){let e=!0,c=!1;if(h==s.length||l<s[h].from){let t=ki[l];t!=a&&(e=!1,c=16==t)}let O=e||1!=a?null:[],u=e?n:n+1,d=l;t:for(;;)if(h<s.length&&d==s[h].from){if(c)break t;let f=s[h];if(!e)for(let t=f.to,e=h+1;;){if(t==i)break t;if(!(e<s.length&&s[e].from==t)){if(ki[t]==a)break t;break}t=s[e++].to}h++,O?O.push(f):(f.from>l&&o.push(new wi(l,f.from,u)),$i(t,f.direction==di!=!(u%2)?n+1:n,r,f.inner,f.from,f.to,o),l=f.to),d=f.to}else{if(d==i||(e?ki[d]!=a:ki[d]==a))break;d++}O?vi(t,l,d,n+1,r,O,o):l<d&&o.push(new wi(l,d,u)),l=d}else for(let l=i,h=s.length;l>e;){let i=!0,c=!1;if(!h||l>s[h-1].to){let t=ki[l-1];t!=a&&(i=!1,c=16==t)}let O=i||1!=a?null:[],u=i?n:n+1,d=l;t:for(;;)if(h&&d==s[h-1].to){if(c)break t;let f=s[--h];if(!i)for(let t=f.from,i=h;;){if(t==e)break t;if(!i||s[i-1].to!=t){if(ki[t-1]==a)break t;break}t=s[--i].from}O?O.push(f):(f.to<l&&o.push(new wi(f.to,l,u)),$i(t,f.direction==di!=!(u%2)?n+1:n,r,f.inner,f.from,f.to,o),l=f.from),d=f.from}else{if(d==e||(i?ki[d-1]!=a:ki[d-1]==a))break;d--}O?vi(t,d,l,n+1,r,O,o):d<l&&o.push(new wi(d,l,u)),l=d}}function $i(t,e,i,n,r,s,o){let a=e%2?2:1;!function(t,e,i,n,r){for(let s=0;s<=n.length;s++){let o=s?n[s-1].to:e,a=s<n.length?n[s].from:i,l=s?256:r;for(let e=o,i=l,n=l;e<a;e++){let r=xi(t.charCodeAt(e));512==r?r=i:8==r&&4==n&&(r=16),ki[e]=4==r?2:r,7&r&&(n=r),i=r}for(let t=o,e=l,n=l;t<a;t++){let r=ki[t];if(128==r)t<a-1&&e==ki[t+1]&&24&e?r=ki[t]=e:ki[t]=256;else if(64==r){let r=t+1;for(;r<a&&64==ki[r];)r++;let s=t&&8==e||r<i&&8==ki[r]?1==n?1:8:256;for(let e=t;e<r;e++)ki[e]=s;t=r-1}else 8==r&&1==n&&(ki[t]=1);e=r,7&r&&(n=r)}}}(t,r,s,n,a),function(t,e,i,n,r){let s=1==r?2:1;for(let o=0,a=0,l=0;o<=n.length;o++){let h=o?n[o-1].to:e,c=o<n.length?n[o].from:i;for(let e,i,n,o=h;o<c;o++)if(i=Qi[e=t.charCodeAt(o)])if(i<0){for(let t=a-3;t>=0;t-=3)if(Si[t+1]==-i){let e=Si[t+2],i=2&e?r:4&e?1&e?s:r:0;i&&(ki[o]=ki[Si[t]]=i),a=t;break}}else{if(189==Si.length)break;Si[a++]=o,Si[a++]=e,Si[a++]=l}else if(2==(n=ki[o])||1==n){let t=n==r;l=t?0:1;for(let e=a-3;e>=0;e-=3){let i=Si[e+2];if(2&i)break;if(t)Si[e+2]|=2;else{if(4&i)break;Si[e+2]|=4}}}}}(t,r,s,n,a),function(t,e,i,n){for(let r=0,s=n;r<=i.length;r++){let o=r?i[r-1].to:t,a=r<i.length?i[r].from:e;for(let l=o;l<a;){let o=ki[l];if(256==o){let o=l+1;for(;;)if(o==a){if(r==i.length)break;o=i[r++].to,a=r<i.length?i[r].from:e}else{if(256!=ki[o])break;o++}let h=1==s,c=h==(1==(o<e?ki[o]:n))?h?1:2:n;for(let e=o,n=r,s=n?i[n-1].to:t;e>l;)e==s&&(e=i[--n].from,s=n?i[n-1].to:t),ki[--e]=c;l=o}else s=o,l++}}}(r,s,n,a),vi(t,r,s,e,i,n,o)}function Pi(t){return[new wi(0,t,0)]}let Ti="";function Zi(t,e,i,n,r){var s;let o=n.head-t.from,a=wi.find(e,o,null!==(s=n.bidiLevel)&&void 0!==s?s:-1,n.assoc),l=e[a],h=l.side(r,i);if(o==h){let t=a+=r?1:-1;if(t<0||t>=e.length)return null;l=e[a=t],o=l.side(!r,i),h=l.side(r,i)}let c=y(t.text,o,l.forward(r,i));(c<l.from||c>l.to)&&(c=h),Ti=t.text.slice(Math.min(o,c),Math.max(o,c));let O=a==(r?e.length-1:0)?null:e[a+(r?1:-1)];return O&&c==h&&O.level+(r?0:1)<l.level?_.cursor(O.side(!r,i)+t.from,O.forward(r,i)?1:-1,O.level):_.cursor(c+t.from,l.forward(r,i)?-1:1,l.level)}function Xi(t,e,i){for(let n=e;n<i;n++){let e=xi(t.charCodeAt(n));if(1==e)return di;if(2==e||4==e)return fi}return di}const Ai=E.define(),Ci=E.define(),Mi=E.define(),Ri=E.define(),_i=E.define(),zi=E.define(),Yi=E.define(),Ei=E.define(),Vi=E.define(),qi=E.define({combine:t=>t.some(t=>t)}),Li=E.define({combine:t=>t.some(t=>t)}),Di=E.define();class Wi{constructor(t,e="nearest",i="nearest",n=5,r=5,s=!1){this.range=t,this.y=e,this.x=i,this.yMargin=n,this.xMargin=r,this.isSnapshot=s}map(t){return t.empty?this:new Wi(this.range.map(t),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}clip(t){return this.range.to<=t.doc.length?this:new Wi(_.cursor(t.doc.length),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}}const ji=ct.define({map:(t,e)=>t.map(e)}),Bi=ct.define();function Ii(t,e,i){let n=t.facet(Ri);n.length?n[0](e):window.onerror&&window.onerror(String(e),i,void 0,void 0,e)||(i?console.error(i+":",e):console.error(e))}const Gi=E.define({combine:t=>!t.length||t[0]});let Ni=0;const Ui=E.define({combine:t=>t.filter((e,i)=>{for(let n=0;n<i;n++)if(t[n].plugin==e.plugin)return!1;return!0})});class Hi{constructor(t,e,i,n,r){this.id=t,this.create=e,this.domEventHandlers=i,this.domEventObservers=n,this.baseExtensions=r(this),this.extension=this.baseExtensions.concat(Ui.of({plugin:this,arg:void 0}))}of(t){return this.baseExtensions.concat(Ui.of({plugin:this,arg:t}))}static define(t,e){const{eventHandlers:i,eventObservers:n,provide:r,decorations:s}=e||{};return new Hi(Ni++,t,i,n,t=>{let e=[];return s&&e.push(tn.of(e=>{let i=e.plugin(t);return i?s(i):ti.none})),r&&e.push(r(t)),e})}static fromClass(t,e){return Hi.define((e,i)=>new t(e,i),e)}}class Fi{constructor(t){this.spec=t,this.mustUpdate=null,this.value=null}get plugin(){return this.spec&&this.spec.plugin}update(t){if(this.value){if(this.mustUpdate){let t=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(t)}catch(e){if(Ii(t.state,e,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch(t){}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.plugin.create(t,this.spec.arg)}catch(e){Ii(t.state,e,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(t){var e;if(null===(e=this.value)||void 0===e?void 0:e.destroy)try{this.value.destroy()}catch(e){Ii(t.state,e,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const Ki=E.define(),Ji=E.define(),tn=E.define(),en=E.define(),nn=E.define(),rn=E.define();function sn(t,e){let i=t.state.facet(rn);if(!i.length)return i;let n=i.map(e=>e instanceof Function?e(t):e),r=[];return Pt.spans(n,e.from,e.to,{point(){},span(t,i,n,s){let o=t-e.from,a=i-e.from,l=r;for(let t=n.length-1;t>=0;t--,s--){let i,r=n[t].spec.bidiIsolate;if(null==r&&(r=Xi(e.text,o,a)),s>0&&l.length&&(i=l[l.length-1]).to==o&&i.direction==r)i.to=a,l=i.inner;else{let t={from:o,to:a,direction:r,inner:[]};l.push(t),l=t.inner}}}}),r}const on=E.define();function an(t){let e=0,i=0,n=0,r=0;for(let s of t.state.facet(on)){let o=s(t);o&&(null!=o.left&&(e=Math.max(e,o.left)),null!=o.right&&(i=Math.max(i,o.right)),null!=o.top&&(n=Math.max(n,o.top)),null!=o.bottom&&(r=Math.max(r,o.bottom)))}return{left:e,right:i,top:n,bottom:r}}const ln=E.define();class hn{constructor(t,e,i,n){this.fromA=t,this.toA=e,this.fromB=i,this.toB=n}join(t){return new hn(Math.min(this.fromA,t.fromA),Math.max(this.toA,t.toA),Math.min(this.fromB,t.fromB),Math.max(this.toB,t.toB))}addToSet(t){let e=t.length,i=this;for(;e>0;e--){let n=t[e-1];if(!(n.fromA>i.toA)){if(n.toA<i.fromA)break;i=i.join(n),t.splice(e-1,1)}}return t.splice(e,0,i),t}static extendWithRanges(t,e){if(0==e.length)return t;let i=[];for(let n=0,r=0,s=0,o=0;;n++){let a=n==t.length?null:t[n],l=s-o,h=a?a.fromB:1e9;for(;r<e.length&&e[r]<h;){let t=e[r],n=e[r+1],s=Math.max(o,t),a=Math.min(h,n);if(s<=a&&new hn(s+l,a+l,s,a).addToSet(i),n>h)break;r+=2}if(!a)return i;new hn(a.fromA,a.toA,a.fromB,a.toB).addToSet(i),s=a.toA,o=a.toB}}}class cn{constructor(t,e,i){this.view=t,this.state=e,this.transactions=i,this.flags=0,this.startState=t.state,this.changes=P.empty(this.startState.doc.length);for(let t of i)this.changes=this.changes.compose(t.changes);let n=[];this.changes.iterChangedRanges((t,e,i,r)=>n.push(new hn(t,e,i,r))),this.changedRanges=n}static create(t,e,i){return new cn(t,e,i)}get viewportChanged(){return(4&this.flags)>0}get viewportMoved(){return(8&this.flags)>0}get heightChanged(){return(2&this.flags)>0}get geometryChanged(){return this.docChanged||(18&this.flags)>0}get focusChanged(){return(1&this.flags)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some(t=>t.selection)}get empty(){return 0==this.flags&&0==this.transactions.length}}class On extends we{get length(){return this.view.state.doc.length}constructor(t){super(),this.view=t,this.decorations=[],this.dynamicDecorationMap=[!1],this.domChanged=null,this.hasComposition=null,this.markedForComposition=new Set,this.editContextFormatting=ti.none,this.lastCompositionAfterCursor=!1,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(t.contentDOM),this.children=[new oi],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new hn(0,0,0,t.state.doc.length)],0,null)}update(t){var e;let i=t.changedRanges;this.minWidth>0&&i.length&&(i.every(({fromA:t,toA:e})=>e<this.minWidthFrom||t>this.minWidthTo)?(this.minWidthFrom=t.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=t.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0),this.updateEditContextFormatting(t);let n=-1;this.view.inputState.composing>=0&&!this.view.observer.editContext&&((null===(e=this.domChanged)||void 0===e?void 0:e.newSel)?n=this.domChanged.newSel.head:function(t,e){let i=!1;return e&&t.iterChangedRanges((t,n)=>{t<e.to&&n>e.from&&(i=!0)}),i}(t.changes,this.hasComposition)||t.selectionSet||(n=t.state.selection.main.head));let r=n>-1?function(t,e,i){let n=un(t,i);if(!n)return null;let{node:r,from:s,to:o}=n,a=r.nodeValue;if(/[\n\r]/.test(a))return null;if(t.state.doc.sliceString(n.from,n.to)!=a)return null;let l=e.invertedDesc,h=new hn(l.mapPos(s),l.mapPos(o),s,o),c=[];for(let e=r.parentNode;;e=e.parentNode){let i=we.get(e);if(i instanceof qe)c.push({node:e,deco:i.mark});else{if(i instanceof oi||"DIV"==e.nodeName&&e.parentNode==t.contentDOM)return{range:h,text:r,marks:c,line:e};if(e==t.contentDOM)return null;c.push({node:e,deco:new ei({inclusive:!0,attributes:Fe(e),tagName:e.tagName.toLowerCase()})})}}}(this.view,t.changes,n):null;if(this.domChanged=null,this.hasComposition){this.markedForComposition.clear();let{from:e,to:n}=this.hasComposition;i=new hn(e,n,t.changes.mapPos(e,-1),t.changes.mapPos(n,1)).addToSet(i.slice())}this.hasComposition=r?{from:r.range.fromB,to:r.range.toB}:null,(Ee.ie||Ee.chrome)&&!r&&t&&t.state.doc.lines!=t.startState.doc.lines&&(this.forceSelection=!0);let s=function(t,e,i){let n=new dn;return Pt.compare(t,e,i,n),n.changes}(this.decorations,this.updateDeco(),t.changes);return i=hn.extendWithRanges(i,s),!!(7&this.flags||0!=i.length)&&(this.updateInner(i,t.startState.doc.length,r),t.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(t,e,i){this.view.viewState.mustMeasureContent=!0,this.updateChildren(t,e,i);let{observer:n}=this.view;n.ignore(()=>{this.dom.style.height=this.view.viewState.contentHeight/this.view.scaleY+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let t=Ee.chrome||Ee.ios?{node:n.selectionRange.focusNode,written:!1}:void 0;this.sync(this.view,t),this.flags&=-8,t&&(t.written||n.selectionRange.focusNode!=t.node)&&(this.forceSelection=!0),this.dom.style.height=""}),this.markedForComposition.forEach(t=>t.flags&=-9);let r=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let t of this.children)t instanceof ai&&t.widget instanceof li&&r.push(t.dom);n.updateGaps(r)}updateChildren(t,e,i){let n=i?i.range.addToSet(t.slice()):t,r=this.childCursor(e);for(let t=n.length-1;;t--){let e=t>=0?n[t]:null;if(!e)break;let s,o,a,l,{fromA:h,toA:c,fromB:O,toB:u}=e;if(i&&i.range.fromB<u&&i.range.toB>O){let t=hi.build(this.view.state.doc,O,i.range.fromB,this.decorations,this.dynamicDecorationMap),e=hi.build(this.view.state.doc,i.range.toB,u,this.decorations,this.dynamicDecorationMap);o=t.breakAtStart,a=t.openStart,l=e.openEnd;let n=this.compositionView(i);e.breakAtStart?n.breakAfter=1:e.content.length&&n.merge(n.length,n.length,e.content[0],!1,e.openStart,0)&&(n.breakAfter=e.content[0].breakAfter,e.content.shift()),t.content.length&&n.merge(0,0,t.content[t.content.length-1],!0,0,t.openEnd)&&t.content.pop(),s=t.content.concat(n).concat(e.content)}else({content:s,breakAtStart:o,openStart:a,openEnd:l}=hi.build(this.view.state.doc,O,u,this.decorations,this.dynamicDecorationMap));let{i:d,off:f}=r.findPos(c,1),{i:p,off:g}=r.findPos(h,-1);ve(this,p,g,d,f,s,o,a,l)}i&&this.fixCompositionDOM(i)}updateEditContextFormatting(t){this.editContextFormatting=this.editContextFormatting.map(t.changes);for(let e of t.transactions)for(let t of e.effects)t.is(Bi)&&(this.editContextFormatting=t.value)}compositionView(t){let e=new Ve(t.text.nodeValue);e.flags|=8;for(let{deco:i}of t.marks)e=new qe(i,[e],e.length);let i=new oi;return i.append(e,0),i}fixCompositionDOM(t){let e=(t,e)=>{e.flags|=8|(e.children.some(t=>7&t.flags)?1:0),this.markedForComposition.add(e);let i=we.get(t);i&&i!=e&&(i.dom=null),e.setDOM(t)},i=this.childPos(t.range.fromB,1),n=this.children[i.i];e(t.line,n);for(let r=t.marks.length-1;r>=-1;r--)i=n.childPos(i.off,1),n=n.children[i.i],e(r>=0?t.marks[r].node:t.text,n)}updateSelection(t=!1,e=!1){!t&&this.view.observer.selectionRange.focusNode||this.view.observer.readSelectionRange();let i=this.view.root.activeElement,n=i==this.dom,r=!n&&!(this.view.state.facet(Gi)||this.dom.tabIndex>-1)&&te(this.dom,this.view.observer.selectionRange)&&!(i&&this.dom.contains(i));if(!(n||e||r))return;let s=this.forceSelection;this.forceSelection=!1;let o=this.view.state.selection.main,a=this.moveToLine(this.domAtPos(o.anchor)),l=o.empty?a:this.moveToLine(this.domAtPos(o.head));if(Ee.gecko&&o.empty&&!this.hasComposition&&1==(h=a).node.nodeType&&h.node.firstChild&&(0==h.offset||"false"==h.node.childNodes[h.offset-1].contentEditable)&&(h.offset==h.node.childNodes.length||"false"==h.node.childNodes[h.offset].contentEditable)){let t=document.createTextNode("");this.view.observer.ignore(()=>a.node.insertBefore(t,a.node.childNodes[a.offset]||null)),a=l=new xe(t,0),s=!0}var h;let c=this.view.observer.selectionRange;!s&&c.focusNode&&(ie(a.node,a.offset,c.anchorNode,c.anchorOffset)&&ie(l.node,l.offset,c.focusNode,c.focusOffset)||this.suppressWidgetCursorChange(c,o))||(this.view.observer.ignore(()=>{Ee.android&&Ee.chrome&&this.dom.contains(c.focusNode)&&function(t,e){for(let i=t;i&&i!=e;i=i.assignedSlot||i.parentNode)if(1==i.nodeType&&"false"==i.contentEditable)return!0;return!1}(c.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let t=Kt(this.view.root);if(t)if(o.empty){if(Ee.gecko){let t=(e=a.node,n=a.offset,1!=e.nodeType?0:(n&&"false"==e.childNodes[n-1].contentEditable?1:0)|(n<e.childNodes.length&&"false"==e.childNodes[n].contentEditable?2:0));if(t&&3!=t){let e=(1==t?Qe:Se)(a.node,a.offset);e&&(a=new xe(e.node,e.offset))}}t.collapse(a.node,a.offset),null!=o.bidiLevel&&void 0!==t.caretBidiLevel&&(t.caretBidiLevel=o.bidiLevel)}else if(t.extend){t.collapse(a.node,a.offset);try{t.extend(l.node,l.offset)}catch(t){}}else{let e=document.createRange();o.anchor>o.head&&([a,l]=[l,a]),e.setEnd(l.node,l.offset),e.setStart(a.node,a.offset),t.removeAllRanges(),t.addRange(e)}var e,n;r&&this.view.root.activeElement==this.dom&&(this.dom.blur(),i&&i.focus())}),this.view.observer.setSelectionRange(a,l)),this.impreciseAnchor=a.precise?null:new xe(c.anchorNode,c.anchorOffset),this.impreciseHead=l.precise?null:new xe(c.focusNode,c.focusOffset)}suppressWidgetCursorChange(t,e){return this.hasComposition&&e.empty&&ie(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)&&this.posFromDOM(t.focusNode,t.focusOffset)==e.head}enforceCursorAssoc(){if(this.hasComposition)return;let{view:t}=this,e=t.state.selection.main,i=Kt(t.root),{anchorNode:n,anchorOffset:r}=t.observer.selectionRange;if(!(i&&e.empty&&e.assoc&&i.modify))return;let s=oi.find(this,e.head);if(!s)return;let o=s.posAtStart;if(e.head==o||e.head==o+s.length)return;let a=this.coordsAt(e.head,-1),l=this.coordsAt(e.head,1);if(!a||!l||a.bottom>l.top)return;let h=this.domAtPos(e.head+e.assoc);i.collapse(h.node,h.offset),i.modify("move",e.assoc<0?"forward":"backward","lineboundary"),t.observer.readSelectionRange();let c=t.observer.selectionRange;t.docView.posFromDOM(c.anchorNode,c.anchorOffset)!=e.from&&i.collapse(n,r)}moveToLine(t){let e,i=this.dom;if(t.node!=i)return t;for(let n=t.offset;!e&&n<i.childNodes.length;n++){let t=we.get(i.childNodes[n]);t instanceof oi&&(e=t.domAtPos(0))}for(let n=t.offset-1;!e&&n>=0;n--){let t=we.get(i.childNodes[n]);t instanceof oi&&(e=t.domAtPos(t.length))}return e?new xe(e.node,e.offset,!0):t}nearest(t){for(let e=t;e;){let t=we.get(e);if(t&&t.rootView==this)return t;e=e.parentNode}return null}posFromDOM(t,e){let i=this.nearest(t);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(t,e)+i.posAtStart}domAtPos(t){let{i:e,off:i}=this.childCursor().findPos(t,-1);for(;e<this.children.length-1;){let t=this.children[e];if(i<t.length||t instanceof oi)break;e++,i=0}return this.children[e].domAtPos(i)}coordsAt(t,e){let i=null,n=0;for(let r=this.length,s=this.children.length-1;s>=0;s--){let o=this.children[s],a=r-o.breakAfter,l=a-o.length;if(a<t)break;if(l<=t&&(l<t||o.covers(-1))&&(a>t||o.covers(1))&&(!i||o instanceof oi&&!(i instanceof oi&&e>=0)))i=o,n=l;else if(i&&l==t&&a==t&&o instanceof ai&&Math.abs(e)<2){if(o.deco.startSide<0)break;s&&(i=null)}r=l}return i?i.coordsAt(t-n,e):null}coordsForChar(t){let{i:e,off:i}=this.childPos(t,1),n=this.children[e];if(!(n instanceof oi))return null;for(;n.children.length;){let{i:t,off:e}=n.childPos(i,1);for(;;t++){if(t==n.children.length)return null;if((n=n.children[t]).length)break}i=e}if(!(n instanceof Ve))return null;let r=y(n.text,i);if(r==i)return null;let s=fe(n.dom,i,r).getClientRects();for(let t=0;t<s.length;t++){let e=s[t];if(t==s.length-1||e.top<e.bottom&&e.left<e.right)return e}return null}measureVisibleLineHeights(t){let e=[],{from:i,to:n}=t,r=this.view.contentDOM.clientWidth,s=r>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,o=-1,a=this.view.textDirection==ui.LTR;for(let t=0,l=0;l<this.children.length;l++){let h=this.children[l],c=t+h.length;if(c>n)break;if(t>=i){let i=h.dom.getBoundingClientRect();if(e.push(i.height),s){let e=h.dom.lastChild,n=e?ee(e):[];if(n.length){let e=n[n.length-1],s=a?e.right-i.left:i.right-e.left;s>o&&(o=s,this.minWidth=r,this.minWidthFrom=t,this.minWidthTo=c)}}}t=c+h.breakAfter}return e}textDirectionAt(t){let{i:e}=this.childPos(t,1);return"rtl"==getComputedStyle(this.children[e].dom).direction?ui.RTL:ui.LTR}measureTextSize(){for(let t of this.children)if(t instanceof oi){let e=t.measureTextSize();if(e)return e}let t,e,i,n=document.createElement("div");return n.className="cm-line",n.style.width="99999px",n.style.position="absolute",n.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore(()=>{this.dom.appendChild(n);let r=ee(n.firstChild)[0];t=n.getBoundingClientRect().height,e=r?r.width/27:7,i=r?r.height:t,n.remove()}),{lineHeight:t,charWidth:e,textHeight:i}}childCursor(t=this.length){let e=this.children.length;return e&&(t-=this.children[--e].length),new ke(this.children,t,e)}computeBlockGapDeco(){let t=[],e=this.view.viewState;for(let i=0,n=0;;n++){let r=n==e.viewports.length?null:e.viewports[n],s=r?r.from-1:this.length;if(s>i){let n=(e.lineBlockAt(s).bottom-e.lineBlockAt(i).top)/this.view.scaleY;t.push(ti.replace({widget:new li(n),block:!0,inclusive:!0,isBlockGap:!0}).range(i,s))}if(!r)break;i=r.to+1}return ti.set(t)}updateDeco(){let t=1,e=this.view.state.facet(tn).map(e=>(this.dynamicDecorationMap[t++]="function"==typeof e)?e(this.view):e),i=!1,n=this.view.state.facet(en).map((t,e)=>{let n="function"==typeof t;return n&&(i=!0),n?t(this.view):t});for(n.length&&(this.dynamicDecorationMap[t++]=i,e.push(Pt.join(n))),this.decorations=[this.editContextFormatting,...e,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco];t<this.decorations.length;)this.dynamicDecorationMap[t++]=!1;return this.decorations}scrollIntoView(t){if(t.isSnapshot){let e=this.view.viewState.lineBlockAt(t.range.head);return this.view.scrollDOM.scrollTop=e.top-t.yMargin,void(this.view.scrollDOM.scrollLeft=t.xMargin)}for(let e of this.view.state.facet(Di))try{if(e(this.view,t.range,t))return!0}catch(t){Ii(this.view.state,t,"scroll handler")}let e,{range:i}=t,n=this.coordsAt(i.head,i.empty?i.assoc:i.head>i.anchor?-1:1);if(!n)return;!i.empty&&(e=this.coordsAt(i.anchor,i.anchor>i.head?-1:1))&&(n={left:Math.min(n.left,e.left),top:Math.min(n.top,e.top),right:Math.max(n.right,e.right),bottom:Math.max(n.bottom,e.bottom)});let r=an(this.view),s={left:n.left-r.left,top:n.top-r.top,right:n.right+r.right,bottom:n.bottom+r.bottom},{offsetWidth:o,offsetHeight:a}=this.view.scrollDOM;!function(t,e,i,n,r,s,o,a){let l=t.ownerDocument,h=l.defaultView||window;for(let c=t,O=!1;c&&!O;)if(1==c.nodeType){let t,u=c==l.body,d=1,f=1;if(u)t=le(h);else{if(/^(fixed|sticky)$/.test(getComputedStyle(c).position)&&(O=!0),c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let e=c.getBoundingClientRect();({scaleX:d,scaleY:f}=he(c,e)),t={left:e.left,right:e.left+c.clientWidth*d,top:e.top,bottom:e.top+c.clientHeight*f}}let p=0,g=0;if("nearest"==r)e.top<t.top?(g=e.top-(t.top+o),i>0&&e.bottom>t.bottom+g&&(g=e.bottom-t.bottom+o)):e.bottom>t.bottom&&(g=e.bottom-t.bottom+o,i<0&&e.top-g<t.top&&(g=e.top-(t.top+o)));else{let n=e.bottom-e.top,s=t.bottom-t.top;g=("center"==r&&n<=s?e.top+n/2-s/2:"start"==r||"center"==r&&i<0?e.top-o:e.bottom-s+o)-t.top}if("nearest"==n?e.left<t.left?(p=e.left-(t.left+s),i>0&&e.right>t.right+p&&(p=e.right-t.right+s)):e.right>t.right&&(p=e.right-t.right+s,i<0&&e.left<t.left+p&&(p=e.left-(t.left+s))):p=("center"==n?e.left+(e.right-e.left)/2-(t.right-t.left)/2:"start"==n==a?e.left-s:e.right-(t.right-t.left)+s)-t.left,p||g)if(u)h.scrollBy(p,g);else{let t=0,i=0;if(g){let t=c.scrollTop;c.scrollTop+=g/f,i=(c.scrollTop-t)*f}if(p){let e=c.scrollLeft;c.scrollLeft+=p/d,t=(c.scrollLeft-e)*d}e={left:e.left-t,top:e.top-i,right:e.right-t,bottom:e.bottom-i},t&&Math.abs(t-p)<1&&(n="nearest"),i&&Math.abs(i-g)<1&&(r="nearest")}if(u)break;(e.top<t.top||e.bottom>t.bottom||e.left<t.left||e.right>t.right)&&(e={left:Math.max(e.left,t.left),right:Math.min(e.right,t.right),top:Math.max(e.top,t.top),bottom:Math.min(e.bottom,t.bottom)}),c=c.assignedSlot||c.parentNode}else{if(11!=c.nodeType)break;c=c.host}}(this.view.scrollDOM,s,i.head<i.anchor?-1:1,t.x,t.y,Math.max(Math.min(t.xMargin,o),-o),Math.max(Math.min(t.yMargin,a),-a),this.view.textDirection==ui.LTR)}}function un(t,e){let i=t.observer.selectionRange;if(!i.focusNode)return null;let n=Qe(i.focusNode,i.focusOffset),r=Se(i.focusNode,i.focusOffset),s=n||r;if(r&&n&&r.node!=n.node){let e=we.get(r.node);if(!e||e instanceof Ve&&e.text!=r.node.nodeValue)s=r;else if(t.docView.lastCompositionAfterCursor){let t=we.get(n.node);!t||t instanceof Ve&&t.text!=n.node.nodeValue||(s=r)}}if(t.docView.lastCompositionAfterCursor=s!=n,!s)return null;let o=e-s.offset;return{from:o,to:o+s.node.nodeValue.length,node:s.node}}let dn=class{constructor(){this.changes=[]}compareRange(t,e){si(t,e,this.changes)}comparePoint(t,e){si(t,e,this.changes)}boundChange(t){si(t,t,this.changes)}};function fn(t,e){return e.left>t?e.left-t:Math.max(0,t-e.right)}function pn(t,e){return e.top>t?e.top-t:Math.max(0,t-e.bottom)}function gn(t,e){return t.top<e.bottom-1&&t.bottom>e.top+1}function mn(t,e){return e<t.top?{top:e,left:t.left,right:t.right,bottom:t.bottom}:t}function Qn(t,e){return e>t.bottom?{top:t.top,left:t.left,right:t.right,bottom:e}:t}function Sn(t,e,i){let n,r,s,o,a,l,h,c,O=!1;for(let u=t.firstChild;u;u=u.nextSibling){let t=ee(u);for(let d=0;d<t.length;d++){let f=t[d];r&&gn(r,f)&&(f=mn(Qn(f,r.bottom),r.top));let p=fn(e,f),g=pn(i,f);if(0==p&&0==g)return 3==u.nodeType?xn(u,e,i):Sn(u,e,i);(!n||o>g||o==g&&s>p)&&(n=u,r=f,s=p,o=g,O=!p||(e<f.left?d>0:d<t.length-1)),0==p?i>f.bottom&&(!h||h.bottom<f.bottom)?(a=u,h=f):i<f.top&&(!c||c.top>f.top)&&(l=u,c=f):h&&gn(h,f)?h=Qn(h,f.bottom):c&&gn(c,f)&&(c=mn(c,f.top))}}if(h&&h.bottom>=i?(n=a,r=h):c&&c.top<=i&&(n=l,r=c),!n)return{node:t,offset:0};let u=Math.max(r.left,Math.min(r.right,e));return 3==n.nodeType?xn(n,u,i):O&&"false"!=n.contentEditable?Sn(n,u,i):{node:t,offset:Array.prototype.indexOf.call(t.childNodes,n)+(e>=(r.left+r.right)/2?1:0)}}function xn(t,e,i){let n=t.nodeValue.length,r=-1,s=1e9,o=0;for(let a=0;a<n;a++){let n=fe(t,a,a+1).getClientRects();for(let l=0;l<n.length;l++){let h=n[l];if(h.top==h.bottom)continue;o||(o=e-h.left);let c=(h.top>i?h.top-i:i-h.bottom)-1;if(h.left-1<=e&&h.right+1>=e&&c<s){let i=e>=(h.left+h.right)/2,n=i;if((Ee.chrome||Ee.gecko)&&fe(t,a).getBoundingClientRect().left==h.right&&(n=!i),c<=0)return{node:t,offset:a+(n?1:0)};r=a+(n?1:0),s=c}}}return{node:t,offset:r>-1?r:o>0?t.nodeValue.length:0}}function yn(t,e,i,n=-1){var r,s;let o,a=t.contentDOM.getBoundingClientRect(),l=a.top+t.viewState.paddingTop,{docHeight:h}=t.viewState,{x:c,y:O}=e,u=O-l;if(u<0)return 0;if(u>h)return t.state.doc.length;for(let e=t.viewState.heightOracle.textHeight/2,r=!1;o=t.elementAtHeight(u),o.type!=Je.Text;)for(;u=n>0?o.bottom+e:o.top-e,!(u>=0&&u<=h);){if(r)return i?null:0;r=!0,n=-n}O=l+u;let d=o.from;if(d<t.viewport.from)return 0==t.viewport.from?0:i?null:wn(t,a,o,c,O);if(d>t.viewport.to)return t.viewport.to==t.state.doc.length?t.state.doc.length:i?null:wn(t,a,o,c,O);let f=t.dom.ownerDocument,p=t.root.elementFromPoint?t.root:f,g=p.elementFromPoint(c,O);g&&!t.contentDOM.contains(g)&&(g=null),g||(c=Math.max(a.left+1,Math.min(a.right-1,c)),g=p.elementFromPoint(c,O),g&&!t.contentDOM.contains(g)&&(g=null));let m,Q=-1;if(g&&0!=(null===(r=t.docView.nearest(g))||void 0===r?void 0:r.isEditable)){if(f.caretPositionFromPoint){let t=f.caretPositionFromPoint(c,O);t&&({offsetNode:m,offset:Q}=t)}else if(f.caretRangeFromPoint){let t=f.caretRangeFromPoint(c,O);t&&({startContainer:m,startOffset:Q}=t)}m&&(!t.contentDOM.contains(m)||Ee.safari&&function(t,e,i){return bn(t,e,i)}(m,Q,c)||Ee.chrome&&function(t,e,i){if(0!=e)return bn(t,e,i);for(let e=t;;){let t=e.parentNode;if(!t||1!=t.nodeType||t.firstChild!=e)return!1;if(t.classList.contains("cm-line"))break;e=t}return i-(1==t.nodeType?t.getBoundingClientRect():fe(t,0,Math.max(t.nodeValue.length,1)).getBoundingClientRect()).left>5}(m,Q,c))&&(m=void 0),m&&(Q=Math.min(oe(m),Q))}if(!m||!t.docView.dom.contains(m)){let e=oi.find(t.docView,d);if(!e)return u>o.top+o.height/2?o.to:o.from;({node:m,offset:Q}=Sn(e.dom,c,O))}let S=t.docView.nearest(m);if(!S)return null;if(S.isWidget&&1==(null===(s=S.dom)||void 0===s?void 0:s.nodeType)){let t=S.dom.getBoundingClientRect();return e.y<t.top||e.y<=t.bottom&&e.x<=(t.left+t.right)/2?S.posAtStart:S.posAtEnd}return S.localPosFromDOM(m,Q)+S.posAtStart}function wn(t,e,i,n,r){let s=Math.round((n-e.left)*t.defaultCharacterWidth);if(t.lineWrapping&&i.height>1.5*t.defaultLineHeight){let e=t.viewState.heightOracle.textHeight;s+=Math.floor((r-i.top-.5*(t.defaultLineHeight-e))/e)*t.viewState.heightOracle.lineLength}let o=t.state.sliceDoc(i.from,i.to);return i.from+function(t,e,i){for(let n=0,r=0;;){if(r>=e)return n;if(n==t.length)break;r+=9==t.charCodeAt(n)?i-r%i:1,n=y(t,n)}return t.length}(o,s,t.state.tabSize)}function bn(t,e,i){let n,r=t;if(3!=t.nodeType||e!=(n=t.nodeValue.length))return!1;for(;;){let t=r.nextSibling;if(t){if("BR"==t.nodeName)break;return!1}{let t=r.parentNode;if(!t||"DIV"==t.nodeName)break;r=t}}return fe(t,n-1,n).getBoundingClientRect().right>i}function kn(t,e,i,n){let r=t.state.doc.lineAt(e.head),s=t.bidiSpans(r),o=t.textDirectionAt(r.from);for(let a=e,l=null;;){let e=Zi(r,s,o,a,i),h=Ti;if(!e){if(r.number==(i?t.state.doc.lines:1))return a;h="\n",r=t.state.doc.line(r.number+(i?1:-1)),s=t.bidiSpans(r),e=t.visualLineSide(r,!i)}if(l){if(!l(h))return a}else{if(!n)return e;l=n(h)}a=e}}function vn(t,e,i){for(;;){let n=0;for(let r of t)r.between(e-1,e+1,(t,r,s)=>{if(e>t&&e<r){let s=n||i||(e-t<r-e?-1:1);e=s<0?t:r,n=s}});if(!n)return e}}function $n(t,e){let i=null;for(let n=0;n<e.ranges.length;n++){let r=e.ranges[n],s=null;if(r.empty){let e=vn(t,r.from,0);e!=r.from&&(s=_.cursor(e,-1))}else{let e=vn(t,r.from,-1),i=vn(t,r.to,1);e==r.from&&i==r.to||(s=_.range(r.from==r.anchor?e:i,r.from==r.head?e:i))}s&&(i||(i=e.ranges.slice()),i[n]=s)}return i?_.create(i,e.mainIndex):e}function Pn(t,e,i){let n=vn(t.state.facet(nn).map(e=>e(t)),i.from,e.head>i.from?-1:1);return n==i.from?i:_.cursor(n,n<i.from?1:-1)}const Tn="￿";class Zn{constructor(t,e){this.points=t,this.text="",this.lineSeparator=e.facet(yt.lineSeparator)}append(t){this.text+=t}lineBreak(){this.text+=Tn}readRange(t,e){if(!t)return this;let i=t.parentNode;for(let n=t;;){this.findPointBefore(i,n);let t=this.text.length;this.readNode(n);let r=n.nextSibling;if(r==e)break;let s=we.get(n),o=we.get(r);(s&&o?s.breakAfter:(s?s.breakAfter:re(n))||re(r)&&("BR"!=n.nodeName||n.cmIgnore)&&this.text.length>t)&&this.lineBreak(),n=r}return this.findPointBefore(i,e),this}readTextNode(t){let e=t.nodeValue;for(let i of this.points)i.node==t&&(i.pos=this.text.length+Math.min(i.offset,e.length));for(let i=0,n=this.lineSeparator?null:/\r\n?|\n/g;;){let r,s=-1,o=1;if(this.lineSeparator?(s=e.indexOf(this.lineSeparator,i),o=this.lineSeparator.length):(r=n.exec(e))&&(s=r.index,o=r[0].length),this.append(e.slice(i,s<0?e.length:s)),s<0)break;if(this.lineBreak(),o>1)for(let e of this.points)e.node==t&&e.pos>this.text.length&&(e.pos-=o-1);i=s+o}}readNode(t){if(t.cmIgnore)return;let e=we.get(t),i=e&&e.overrideDOMText;if(null!=i){this.findPointInside(t,i.length);for(let t=i.iter();!t.next().done;)t.lineBreak?this.lineBreak():this.append(t.value)}else 3==t.nodeType?this.readTextNode(t):"BR"==t.nodeName?t.nextSibling&&this.lineBreak():1==t.nodeType&&this.readRange(t.firstChild,null)}findPointBefore(t,e){for(let i of this.points)i.node==t&&t.childNodes[i.offset]==e&&(i.pos=this.text.length)}findPointInside(t,e){for(let i of this.points)(3==t.nodeType?i.node==t:t.contains(i.node))&&(i.pos=this.text.length+(Xn(t,i.node,i.offset)?e:0))}}function Xn(t,e,i){for(;;){if(!e||i<oe(e))return!1;if(e==t)return!0;i=ne(e)+1,e=e.parentNode}}class An{constructor(t,e){this.node=t,this.offset=e,this.pos=-1}}class Cn{constructor(t,e,i,n){this.typeOver=n,this.bounds=null,this.text="",this.domChanged=e>-1;let{impreciseHead:r,impreciseAnchor:s}=t.docView;if(t.state.readOnly&&e>-1)this.newSel=null;else if(e>-1&&(this.bounds=t.docView.domBoundsAround(e,i,0))){let e=r||s?[]:function(t){let e=[];if(t.root.activeElement!=t.contentDOM)return e;let{anchorNode:i,anchorOffset:n,focusNode:r,focusOffset:s}=t.observer.selectionRange;return i&&(e.push(new An(i,n)),r==i&&s==n||e.push(new An(r,s))),e}(t),i=new Zn(e,t.state);i.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=i.text,this.newSel=function(t,e){if(0==t.length)return null;let i=t[0].pos,n=2==t.length?t[1].pos:i;return i>-1&&n>-1?_.single(i+e,n+e):null}(e,this.bounds.from)}else{let e=t.observer.selectionRange,i=r&&r.node==e.focusNode&&r.offset==e.focusOffset||!Jt(t.contentDOM,e.focusNode)?t.state.selection.main.head:t.docView.posFromDOM(e.focusNode,e.focusOffset),n=s&&s.node==e.anchorNode&&s.offset==e.anchorOffset||!Jt(t.contentDOM,e.anchorNode)?t.state.selection.main.anchor:t.docView.posFromDOM(e.anchorNode,e.anchorOffset),o=t.viewport;if((Ee.ios||Ee.chrome)&&t.state.selection.main.empty&&i!=n&&(o.from>0||o.to<t.state.doc.length)){let e=Math.min(i,n),r=Math.max(i,n),s=o.from-e,a=o.to-r;0!=s&&1!=s&&0!=e||0!=a&&-1!=a&&r!=t.state.doc.length||(i=0,n=t.state.doc.length)}this.newSel=_.single(n,i)}}}function Mn(t,e){let i,{newSel:n}=e,r=t.state.selection.main,s=t.inputState.lastKeyTime>Date.now()-100?t.inputState.lastKeyCode:-1;if(e.bounds){let{from:n,to:o}=e.bounds,a=r.from,l=null;(8===s||Ee.android&&e.text.length<o-n)&&(a=r.to,l="end");let h=function(t,e,i,n){let r=Math.min(t.length,e.length),s=0;for(;s<r&&t.charCodeAt(s)==e.charCodeAt(s);)s++;if(s==r&&t.length==e.length)return null;let o=t.length,a=e.length;for(;o>0&&a>0&&t.charCodeAt(o-1)==e.charCodeAt(a-1);)o--,a--;return"end"==n&&(i-=o+Math.max(0,s-Math.min(o,a))-s),o<s&&t.length<e.length?(s-=i<=s&&i>=o?s-i:0,a=s+(a-o),o=s):a<s&&(s-=i<=s&&i>=a?s-i:0,o=s+(o-a),a=s),{from:s,toA:o,toB:a}}(t.state.doc.sliceString(n,o,Tn),e.text,a-n,l);h&&(Ee.chrome&&13==s&&h.toB==h.from+2&&e.text.slice(h.from,h.toB)==Tn+Tn&&h.toB--,i={from:n+h.from,to:n+h.toA,insert:O.of(e.text.slice(h.from,h.toB).split(Tn))})}else n&&(!t.hasFocus&&t.state.facet(Gi)||n.main.eq(r))&&(n=null);if(!i&&!n)return!1;if(!i&&e.typeOver&&!r.empty&&n&&n.main.empty?i={from:r.from,to:r.to,insert:t.state.doc.slice(r.from,r.to)}:(Ee.mac||Ee.android)&&i&&i.from==i.to&&i.from==r.head-1&&/^\. ?$/.test(i.insert.toString())&&"off"==t.contentDOM.getAttribute("autocorrect")?(n&&2==i.insert.length&&(n=_.single(n.main.anchor-1,n.main.head-1)),i={from:i.from,to:i.to,insert:O.of([i.insert.toString().replace("."," ")])}):i&&i.from>=r.from&&i.to<=r.to&&(i.from!=r.from||i.to!=r.to)&&r.to-r.from-(i.to-i.from)<=4?i={from:r.from,to:r.to,insert:t.state.doc.slice(r.from,i.from).append(i.insert).append(t.state.doc.slice(i.to,r.to))}:Ee.chrome&&i&&i.from==i.to&&i.from==r.head&&"\n "==i.insert.toString()&&t.lineWrapping&&(n&&(n=_.single(n.main.anchor-1,n.main.head-1)),i={from:r.from,to:r.to,insert:O.of([" "])}),i)return Rn(t,i,n,s);if(n&&!n.main.eq(r)){let e=!1,i="select";return t.inputState.lastSelectionTime>Date.now()-50&&("select"==t.inputState.lastSelectionOrigin&&(e=!0),i=t.inputState.lastSelectionOrigin,"select.pointer"==i&&(n=$n(t.state.facet(nn).map(e=>e(t)),n))),t.dispatch({selection:n,scrollIntoView:e,userEvent:i}),!0}return!1}function Rn(t,e,i,n=-1){if(Ee.ios&&t.inputState.flushIOSKey(e))return!0;let r=t.state.selection.main;if(Ee.android&&(e.to==r.to&&(e.from==r.from||e.from==r.from-1&&" "==t.state.sliceDoc(e.from,r.from))&&1==e.insert.length&&2==e.insert.lines&&pe(t.contentDOM,"Enter",13)||(e.from==r.from-1&&e.to==r.to&&0==e.insert.length||8==n&&e.insert.length<e.to-e.from&&e.to>r.head)&&pe(t.contentDOM,"Backspace",8)||e.from==r.from&&e.to==r.to+1&&0==e.insert.length&&pe(t.contentDOM,"Delete",46)))return!0;let s,o=e.insert.toString();t.inputState.composing>=0&&t.inputState.composing++;let a=()=>s||(s=function(t,e,i){let n,r=t.state,s=r.selection.main,o=-1;if(e.from==e.to&&e.from<s.from||e.from>s.to){let i=e.from<s.from?-1:1,n=i<0?s.from:s.to,a=vn(r.facet(nn).map(e=>e(t)),n,i);e.from==a&&(o=a)}if(o>-1)n={changes:e,selection:_.cursor(e.from+e.insert.length,-1)};else if(e.from>=s.from&&e.to<=s.to&&e.to-e.from>=(s.to-s.from)/3&&(!i||i.main.empty&&i.main.from==e.from+e.insert.length)&&t.inputState.composing<0){let i=s.from<e.from?r.sliceDoc(s.from,e.from):"",o=s.to>e.to?r.sliceDoc(e.to,s.to):"";n=r.replaceSelection(t.state.toText(i+e.insert.sliceString(0,void 0,t.state.lineBreak)+o))}else{let o=r.changes(e),a=i&&i.main.to<=o.newLength?i.main:void 0;if(r.selection.ranges.length>1&&t.inputState.composing>=0&&e.to<=s.to&&e.to>=s.to-10){let l,h=t.state.sliceDoc(e.from,e.to),c=i&&un(t,i.main.head);if(c){let t=e.insert.length-(e.to-e.from);l={from:c.from,to:c.to-t}}else l=t.state.doc.lineAt(s.head);let O=s.to-e.to,u=s.to-s.from;n=r.changeByRange(i=>{if(i.from==s.from&&i.to==s.to)return{changes:o,range:a||i.map(o)};let n=i.to-O,c=n-h.length;if(i.to-i.from!=u||t.state.sliceDoc(c,n)!=h||i.to>=l.from&&i.from<=l.to)return{range:i};let d=r.changes({from:c,to:n,insert:e.insert}),f=i.to-s.to;return{changes:d,range:a?_.range(Math.max(0,a.anchor+f),Math.max(0,a.head+f)):i.map(d)}})}else n={changes:o,selection:a&&r.selection.replaceRange(a)}}let a="input.type";return(t.composing||t.inputState.compositionPendingChange&&t.inputState.compositionEndedAt>Date.now()-50)&&(t.inputState.compositionPendingChange=!1,a+=".compose",t.inputState.compositionFirstChange&&(a+=".start",t.inputState.compositionFirstChange=!1)),r.update(n,{userEvent:a,scrollIntoView:!0})}(t,e,i));return t.state.facet(zi).some(i=>i(t,e.from,e.to,o,a))||t.dispatch(a()),!0}class _n{setSelectionOrigin(t){this.lastSelectionOrigin=t,this.lastSelectionTime=Date.now()}constructor(t){this.view=t,this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.pendingIOSKey=void 0,this.tabFocusMode=-1,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastContextMenu=0,this.scrollHandlers=[],this.handlers=Object.create(null),this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.compositionPendingKey=!1,this.compositionPendingChange=!1,this.mouseSelection=null,this.draggedContent=null,this.handleEvent=this.handleEvent.bind(this),this.notifiedFocused=t.hasFocus,Ee.safari&&t.contentDOM.addEventListener("input",()=>null),Ee.gecko&&function(t){lr.has(t)||(lr.add(t),t.addEventListener("copy",()=>{}),t.addEventListener("cut",()=>{}))}(t.contentDOM.ownerDocument)}handleEvent(t){(function(t,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let i,n=e.target;n!=t.contentDOM;n=n.parentNode)if(!n||11==n.nodeType||(i=we.get(n))&&i.ignoreEvent(e))return!1;return!0})(this.view,t)&&!this.ignoreDuringComposition(t)&&("keydown"==t.type&&this.keydown(t)||(0!=this.view.updateState?Promise.resolve().then(()=>this.runHandlers(t.type,t)):this.runHandlers(t.type,t)))}runHandlers(t,e){let i=this.handlers[t];if(i){for(let t of i.observers)t(this.view,e);for(let t of i.handlers){if(e.defaultPrevented)break;if(t(this.view,e)){e.preventDefault();break}}}}ensureHandlers(t){let e=Yn(t),i=this.handlers,n=this.view.contentDOM;for(let t in e)if("scroll"!=t){let r=!e[t].handlers.length,s=i[t];s&&r!=!s.handlers.length&&(n.removeEventListener(t,this.handleEvent),s=null),s||n.addEventListener(t,this.handleEvent,{passive:r})}for(let t in i)"scroll"==t||e[t]||n.removeEventListener(t,this.handleEvent);this.handlers=e}keydown(t){if(this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),9==t.keyCode&&this.tabFocusMode>-1&&(!this.tabFocusMode||Date.now()<=this.tabFocusMode))return!0;if(this.tabFocusMode>0&&27!=t.keyCode&&qn.indexOf(t.keyCode)<0&&(this.tabFocusMode=-1),Ee.android&&Ee.chrome&&!t.synthetic&&(13==t.keyCode||8==t.keyCode))return this.view.observer.delayAndroidKey(t.key,t.keyCode),!0;let e;return!Ee.ios||t.synthetic||t.altKey||t.metaKey||!((e=En.find(e=>e.keyCode==t.keyCode))&&!t.ctrlKey||Vn.indexOf(t.key)>-1&&t.ctrlKey&&!t.shiftKey)?(229!=t.keyCode&&this.view.observer.forceFlush(),!1):(this.pendingIOSKey=e||t,setTimeout(()=>this.flushIOSKey(),250),!0)}flushIOSKey(t){let e=this.pendingIOSKey;return!!e&&!("Enter"==e.key&&t&&t.from<t.to&&/^\S+$/.test(t.insert.toString()))&&(this.pendingIOSKey=void 0,pe(this.view.contentDOM,e.key,e.keyCode,e instanceof KeyboardEvent?e:void 0))}ignoreDuringComposition(t){return!!/^key/.test(t.type)&&(this.composing>0||!!(Ee.safari&&!Ee.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100)&&(this.compositionPendingKey=!1,!0))}startMouseSelection(t){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=t}update(t){this.view.observer.update(t),this.mouseSelection&&this.mouseSelection.update(t),this.draggedContent&&t.docChanged&&(this.draggedContent=this.draggedContent.map(t.changes)),t.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}function zn(t,e){return(i,n)=>{try{return e.call(t,n,i)}catch(t){Ii(i.state,t)}}}function Yn(t){let e=Object.create(null);function i(t){return e[t]||(e[t]={observers:[],handlers:[]})}for(let e of t){let t=e.spec,n=t&&t.plugin.domEventHandlers,r=t&&t.plugin.domEventObservers;if(n)for(let t in n){let r=n[t];r&&i(t).handlers.push(zn(e.value,r))}if(r)for(let t in r){let n=r[t];n&&i(t).observers.push(zn(e.value,n))}}for(let t in Wn)i(t).handlers.push(Wn[t]);for(let t in jn)i(t).observers.push(jn[t]);return e}const En=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Enter",keyCode:13,inputType:"insertLineBreak"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],Vn="dthko",qn=[16,17,18,20,91,92,224,225];function Ln(t){return.7*Math.max(0,t)+8}class Dn{constructor(t,e,i,n){this.view=t,this.startEvent=e,this.style=i,this.mustSelect=n,this.scrollSpeed={x:0,y:0},this.scrolling=-1,this.lastEvent=e,this.scrollParents=function(t){let e,i,n=t.ownerDocument;for(let r=t.parentNode;r&&!(r==n.body||e&&i);)if(1==r.nodeType)!i&&r.scrollHeight>r.clientHeight&&(i=r),!e&&r.scrollWidth>r.clientWidth&&(e=r),r=r.assignedSlot||r.parentNode;else{if(11!=r.nodeType)break;r=r.host}return{x:e,y:i}}(t.contentDOM),this.atoms=t.state.facet(nn).map(e=>e(t));let r=t.contentDOM.ownerDocument;r.addEventListener("mousemove",this.move=this.move.bind(this)),r.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=e.shiftKey,this.multiple=t.state.facet(yt.allowMultipleSelections)&&function(t,e){let i=t.state.facet(Ai);return i.length?i[0](e):Ee.mac?e.metaKey:e.ctrlKey}(t,e),this.dragging=!(!function(t,e){let{main:i}=t.state.selection;if(i.empty)return!1;let n=Kt(t.root);if(!n||0==n.rangeCount)return!0;let r=n.getRangeAt(0).getClientRects();for(let t=0;t<r.length;t++){let i=r[t];if(i.left<=e.clientX&&i.right>=e.clientX&&i.top<=e.clientY&&i.bottom>=e.clientY)return!0}return!1}(t,e)||1!=ir(e))&&null}start(t){!1===this.dragging&&this.select(t)}move(t){if(0==t.buttons)return this.destroy();if(this.dragging||null==this.dragging&&(e=this.startEvent,i=t,Math.max(Math.abs(e.clientX-i.clientX),Math.abs(e.clientY-i.clientY))<10))return;var e,i;this.select(this.lastEvent=t);let n=0,r=0,s=0,o=0,a=this.view.win.innerWidth,l=this.view.win.innerHeight;this.scrollParents.x&&({left:s,right:a}=this.scrollParents.x.getBoundingClientRect()),this.scrollParents.y&&({top:o,bottom:l}=this.scrollParents.y.getBoundingClientRect());let h=an(this.view);t.clientX-h.left<=s+6?n=-Ln(s-t.clientX):t.clientX+h.right>=a-6&&(n=Ln(t.clientX-a)),t.clientY-h.top<=o+6?r=-Ln(o-t.clientY):t.clientY+h.bottom>=l-6&&(r=Ln(t.clientY-l)),this.setScrollSpeed(n,r)}up(t){null==this.dragging&&this.select(this.lastEvent),this.dragging||t.preventDefault(),this.destroy()}destroy(){this.setScrollSpeed(0,0);let t=this.view.contentDOM.ownerDocument;t.removeEventListener("mousemove",this.move),t.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=this.view.inputState.draggedContent=null}setScrollSpeed(t,e){this.scrollSpeed={x:t,y:e},t||e?this.scrolling<0&&(this.scrolling=setInterval(()=>this.scroll(),50)):this.scrolling>-1&&(clearInterval(this.scrolling),this.scrolling=-1)}scroll(){let{x:t,y:e}=this.scrollSpeed;t&&this.scrollParents.x&&(this.scrollParents.x.scrollLeft+=t,t=0),e&&this.scrollParents.y&&(this.scrollParents.y.scrollTop+=e,e=0),(t||e)&&this.view.win.scrollBy(t,e),!1===this.dragging&&this.select(this.lastEvent)}select(t){let{view:e}=this,i=$n(this.atoms,this.style.get(t,this.extend,this.multiple));!this.mustSelect&&i.eq(e.state.selection,!1===this.dragging)||this.view.dispatch({selection:i,userEvent:"select.pointer"}),this.mustSelect=!1}update(t){t.transactions.some(t=>t.isUserEvent("input.type"))?this.destroy():this.style.update(t)&&setTimeout(()=>this.select(this.lastEvent),20)}}const Wn=Object.create(null),jn=Object.create(null),Bn=Ee.ie&&Ee.ie_version<15||Ee.ios&&Ee.webkit_version<604;function In(t,e,i){for(let n of t.facet(e))i=n(i,t);return i}function Gn(t,e){e=In(t.state,Ei,e);let i,{state:n}=t,r=1,s=n.toText(e),o=s.lines==n.selection.ranges.length;if(null!=rr&&n.selection.ranges.every(t=>t.empty)&&rr==s.toString()){let t=-1;i=n.changeByRange(i=>{let a=n.doc.lineAt(i.from);if(a.from==t)return{range:i};t=a.from;let l=n.toText((o?s.line(r++).text:e)+n.lineBreak);return{changes:{from:a.from,insert:l},range:_.cursor(i.from+l.length)}})}else i=o?n.changeByRange(t=>{let e=s.line(r++);return{changes:{from:t.from,to:t.to,insert:e.text},range:_.cursor(t.from+e.length)}}):n.replaceSelection(s);t.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0})}function Nn(t,e,i,n){if(1==n)return _.cursor(e,i);if(2==n)return function(t,e,i=1){let n=t.charCategorizer(e),r=t.doc.lineAt(e),s=e-r.from;if(0==r.length)return _.cursor(e);0==s?i=1:s==r.length&&(i=-1);let o=s,a=s;i<0?o=y(r.text,s,!1):a=y(r.text,s);let l=n(r.text.slice(o,a));for(;o>0;){let t=y(r.text,o,!1);if(n(r.text.slice(t,o))!=l)break;o=t}for(;a<r.length;){let t=y(r.text,a);if(n(r.text.slice(a,t))!=l)break;a=t}return _.range(o+r.from,a+r.from)}(t.state,e,i);{let i=oi.find(t.docView,e),n=t.state.doc.lineAt(i?i.posAtEnd:e),r=i?i.posAtStart:n.from,s=i?i.posAtEnd:n.to;return s<t.state.doc.length&&s==n.to&&s++,_.range(r,s)}}jn.scroll=t=>{t.inputState.lastScrollTop=t.scrollDOM.scrollTop,t.inputState.lastScrollLeft=t.scrollDOM.scrollLeft},Wn.keydown=(t,e)=>(t.inputState.setSelectionOrigin("select"),27==e.keyCode&&0!=t.inputState.tabFocusMode&&(t.inputState.tabFocusMode=Date.now()+2e3),!1),jn.touchstart=(t,e)=>{t.inputState.lastTouchTime=Date.now(),t.inputState.setSelectionOrigin("select.pointer")},jn.touchmove=t=>{t.inputState.setSelectionOrigin("select.pointer")},Wn.mousedown=(t,e)=>{if(t.observer.flush(),t.inputState.lastTouchTime>Date.now()-2e3)return!1;let i=null;for(let n of t.state.facet(Mi))if(i=n(t,e),i)break;if(i||0!=e.button||(i=function(t,e){let i=Fn(t,e),n=ir(e),r=t.state.selection;return{update(t){t.docChanged&&(i.pos=t.changes.mapPos(i.pos),r=r.map(t.changes))},get(e,s,o){let a,l=Fn(t,e),h=Nn(t,l.pos,l.bias,n);if(i.pos!=l.pos&&!s){let e=Nn(t,i.pos,i.bias,n),r=Math.min(e.from,h.from),s=Math.max(e.to,h.to);h=r<h.from?_.range(r,s):_.range(s,r)}return s?r.replaceRange(r.main.extend(h.from,h.to)):o&&1==n&&r.ranges.length>1&&(a=function(t,e){for(let i=0;i<t.ranges.length;i++){let{from:n,to:r}=t.ranges[i];if(n<=e&&r>=e)return _.create(t.ranges.slice(0,i).concat(t.ranges.slice(i+1)),t.mainIndex==i?0:t.mainIndex-(t.mainIndex>i?1:0))}return null}(r,l.pos))?a:o?r.addRange(h):_.create([h])}}}(t,e)),i){let n=!t.hasFocus;t.inputState.startMouseSelection(new Dn(t,e,i,n)),n&&t.observer.ignore(()=>{de(t.contentDOM);let e=t.root.activeElement;e&&!e.contains(t.contentDOM)&&e.blur()});let r=t.inputState.mouseSelection;if(r)return r.start(e),!1===r.dragging}else t.inputState.setSelectionOrigin("select.pointer");return!1};let Un=(t,e,i)=>e>=i.top&&e<=i.bottom&&t>=i.left&&t<=i.right;function Hn(t,e,i,n){let r=oi.find(t.docView,e);if(!r)return 1;let s=e-r.posAtStart;if(0==s)return 1;if(s==r.length)return-1;let o=r.coordsAt(s,-1);if(o&&Un(i,n,o))return-1;let a=r.coordsAt(s,1);return a&&Un(i,n,a)?1:o&&o.bottom>=n?-1:1}function Fn(t,e){let i=t.posAtCoords({x:e.clientX,y:e.clientY},!1);return{pos:i,bias:Hn(t,i,e.clientX,e.clientY)}}const Kn=Ee.ie&&Ee.ie_version<=11;let Jn=null,tr=0,er=0;function ir(t){if(!Kn)return t.detail;let e=Jn,i=er;return Jn=t,er=Date.now(),tr=!e||i>Date.now()-400&&Math.abs(e.clientX-t.clientX)<2&&Math.abs(e.clientY-t.clientY)<2?(tr+1)%3:1}function nr(t,e,i,n){if(!(i=In(t.state,Ei,i)))return;let r=t.posAtCoords({x:e.clientX,y:e.clientY},!1),{draggedContent:s}=t.inputState,o=n&&s&&function(t,e){let i=t.state.facet(Ci);return i.length?i[0](e):Ee.mac?!e.altKey:!e.ctrlKey}(t,e)?{from:s.from,to:s.to}:null,a={from:r,insert:i},l=t.state.changes(o?[o,a]:a);t.focus(),t.dispatch({changes:l,selection:{anchor:l.mapPos(r,-1),head:l.mapPos(r,1)},userEvent:o?"move.drop":"input.drop"}),t.inputState.draggedContent=null}Wn.dragstart=(t,e)=>{let{selection:{main:i}}=t.state;if(e.target.draggable){let n=t.docView.nearest(e.target);if(n&&n.isWidget){let t=n.posAtStart,e=t+n.length;(t>=i.to||e<=i.from)&&(i=_.range(t,e))}}let{inputState:n}=t;return n.mouseSelection&&(n.mouseSelection.dragging=!0),n.draggedContent=i,e.dataTransfer&&(e.dataTransfer.setData("Text",In(t.state,Vi,t.state.sliceDoc(i.from,i.to))),e.dataTransfer.effectAllowed="copyMove"),!1},Wn.dragend=t=>(t.inputState.draggedContent=null,!1),Wn.drop=(t,e)=>{if(!e.dataTransfer)return!1;if(t.state.readOnly)return!0;let i=e.dataTransfer.files;if(i&&i.length){let n=Array(i.length),r=0,s=()=>{++r==i.length&&nr(t,e,n.filter(t=>null!=t).join(t.state.lineBreak),!1)};for(let t=0;t<i.length;t++){let e=new FileReader;e.onerror=s,e.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(e.result)||(n[t]=e.result),s()},e.readAsText(i[t])}return!0}{let i=e.dataTransfer.getData("Text");if(i)return nr(t,e,i,!0),!0}return!1},Wn.paste=(t,e)=>{if(t.state.readOnly)return!0;t.observer.flush();let i=Bn?null:e.clipboardData;return i?(Gn(t,i.getData("text/plain")||i.getData("text/uri-list")),!0):(function(t){let e=t.dom.parentNode;if(!e)return;let i=e.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.focus(),setTimeout(()=>{t.focus(),i.remove(),Gn(t,i.value)},50)}(t),!1)};let rr=null;Wn.copy=Wn.cut=(t,e)=>{let{text:i,ranges:n,linewise:r}=function(t){let e=[],i=[],n=!1;for(let n of t.selection.ranges)n.empty||(e.push(t.sliceDoc(n.from,n.to)),i.push(n));if(!e.length){let r=-1;for(let{from:n}of t.selection.ranges){let s=t.doc.lineAt(n);s.number>r&&(e.push(s.text),i.push({from:s.from,to:Math.min(t.doc.length,s.to+1)})),r=s.number}n=!0}return{text:In(t,Vi,e.join(t.lineBreak)),ranges:i,linewise:n}}(t.state);if(!i&&!r)return!1;rr=r?i:null,"cut"!=e.type||t.state.readOnly||t.dispatch({changes:n,scrollIntoView:!0,userEvent:"delete.cut"});let s=Bn?null:e.clipboardData;return s?(s.clearData(),s.setData("text/plain",i),!0):(function(t,e){let i=t.dom.parentNode;if(!i)return;let n=i.appendChild(document.createElement("textarea"));n.style.cssText="position: fixed; left: -10000px; top: 10px",n.value=e,n.focus(),n.selectionEnd=e.length,n.selectionStart=0,setTimeout(()=>{n.remove(),t.focus()},50)}(t,i),!1)};const sr=at.define();function or(t,e){let i=[];for(let n of t.facet(Yi)){let r=n(t,e);r&&i.push(r)}return i.length?t.update({effects:i,annotations:sr.of(!0)}):null}function ar(t){setTimeout(()=>{let e=t.hasFocus;if(e!=t.inputState.notifiedFocused){let i=or(t.state,e);i?t.dispatch(i):t.update([])}},10)}jn.focus=t=>{t.inputState.lastFocusTime=Date.now(),t.scrollDOM.scrollTop||!t.inputState.lastScrollTop&&!t.inputState.lastScrollLeft||(t.scrollDOM.scrollTop=t.inputState.lastScrollTop,t.scrollDOM.scrollLeft=t.inputState.lastScrollLeft),ar(t)},jn.blur=t=>{t.observer.clearSelectionRange(),ar(t)},jn.compositionstart=jn.compositionupdate=t=>{t.observer.editContext||(null==t.inputState.compositionFirstChange&&(t.inputState.compositionFirstChange=!0),t.inputState.composing<0&&(t.inputState.composing=0))},jn.compositionend=t=>{t.observer.editContext||(t.inputState.composing=-1,t.inputState.compositionEndedAt=Date.now(),t.inputState.compositionPendingKey=!0,t.inputState.compositionPendingChange=t.observer.pendingRecords().length>0,t.inputState.compositionFirstChange=null,Ee.chrome&&Ee.android?t.observer.flushSoon():t.inputState.compositionPendingChange?Promise.resolve().then(()=>t.observer.flush()):setTimeout(()=>{t.inputState.composing<0&&t.docView.hasComposition&&t.update([])},50))},jn.contextmenu=t=>{t.inputState.lastContextMenu=Date.now()},Wn.beforeinput=(t,e)=>{var i,n;if("insertReplacementText"==e.inputType&&t.observer.editContext){let n=null===(i=e.dataTransfer)||void 0===i?void 0:i.getData("text/plain"),r=e.getTargetRanges();if(n&&r.length){let e=r[0],i=t.posAtDOM(e.startContainer,e.startOffset),s=t.posAtDOM(e.endContainer,e.endOffset);return Rn(t,{from:i,to:s,insert:t.state.toText(n)},null),!0}}let r;if(Ee.chrome&&Ee.android&&(r=En.find(t=>t.inputType==e.inputType))&&(t.observer.delayAndroidKey(r.key,r.keyCode),"Backspace"==r.key||"Delete"==r.key)){let e=(null===(n=window.visualViewport)||void 0===n?void 0:n.height)||0;setTimeout(()=>{var i;((null===(i=window.visualViewport)||void 0===i?void 0:i.height)||0)>e+10&&t.hasFocus&&(t.contentDOM.blur(),t.focus())},100)}return Ee.ios&&"deleteContentForward"==e.inputType&&t.observer.flushSoon(),Ee.safari&&"insertText"==e.inputType&&t.inputState.composing>=0&&setTimeout(()=>jn.compositionend(t,e),20),!1};const lr=new Set,hr=["pre-wrap","normal","pre-line","break-spaces"];let cr=!1;function Or(){cr=!1}class ur{constructor(t){this.lineWrapping=t,this.doc=O.empty,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.textHeight=14,this.lineLength=30}heightForGap(t,e){let i=this.doc.lineAt(e).number-this.doc.lineAt(t).number+1;return this.lineWrapping&&(i+=Math.max(0,Math.ceil((e-t-i*this.lineLength*.5)/this.lineLength))),this.lineHeight*i}heightForLine(t){return this.lineWrapping?(1+Math.max(0,Math.ceil((t-this.lineLength)/Math.max(1,this.lineLength-5))))*this.lineHeight:this.lineHeight}setDoc(t){return this.doc=t,this}mustRefreshForWrapping(t){return hr.indexOf(t)>-1!=this.lineWrapping}mustRefreshForHeights(t){let e=!1;for(let i=0;i<t.length;i++){let n=t[i];n<0?i++:this.heightSamples[Math.floor(10*n)]||(e=!0,this.heightSamples[Math.floor(10*n)]=!0)}return e}refresh(t,e,i,n,r,s){let o=hr.indexOf(t)>-1,a=Math.round(e)!=Math.round(this.lineHeight)||this.lineWrapping!=o;if(this.lineWrapping=o,this.lineHeight=e,this.charWidth=i,this.textHeight=n,this.lineLength=r,a){this.heightSamples={};for(let t=0;t<s.length;t++){let e=s[t];e<0?t++:this.heightSamples[Math.floor(10*e)]=!0}}return a}}class dr{constructor(t,e){this.from=t,this.heights=e,this.index=0}get more(){return this.index<this.heights.length}}class fr{constructor(t,e,i,n,r){this.from=t,this.length=e,this.top=i,this.height=n,this._content=r}get type(){return"number"==typeof this._content?Je.Text:Array.isArray(this._content)?this._content:this._content.type}get to(){return this.from+this.length}get bottom(){return this.top+this.height}get widget(){return this._content instanceof ni?this._content.widget:null}get widgetLineBreaks(){return"number"==typeof this._content?this._content:0}join(t){let e=(Array.isArray(this._content)?this._content:[this]).concat(Array.isArray(t._content)?t._content:[t]);return new fr(this.from,this.length+t.length,this.top,this.height+t.height,e)}}var pr=function(t){return t[t.ByPos=0]="ByPos",t[t.ByHeight=1]="ByHeight",t[t.ByPosNoHeight=2]="ByPosNoHeight",t}(pr||(pr={}));const gr=.001;class mr{constructor(t,e,i=2){this.length=t,this.height=e,this.flags=i}get outdated(){return(2&this.flags)>0}set outdated(t){this.flags=(t?2:0)|-3&this.flags}setHeight(t){this.height!=t&&(Math.abs(this.height-t)>gr&&(cr=!0),this.height=t)}replace(t,e,i){return mr.of(i)}decomposeLeft(t,e){e.push(this)}decomposeRight(t,e){e.push(this)}applyChanges(t,e,i,n){let r=this,s=i.doc;for(let o=n.length-1;o>=0;o--){let{fromA:a,toA:l,fromB:h,toB:c}=n[o],O=r.lineAt(a,pr.ByPosNoHeight,i.setDoc(e),0,0),u=O.to>=l?O:r.lineAt(l,pr.ByPosNoHeight,i,0,0);for(c+=u.to-l,l=u.to;o>0&&O.from<=n[o-1].toA;)a=n[o-1].fromA,h=n[o-1].fromB,o--,a<O.from&&(O=r.lineAt(a,pr.ByPosNoHeight,i,0,0));h+=O.from-a,a=O.from;let d=kr.build(i.setDoc(s),t,h,c);r=Qr(r,r.replace(a,l,d))}return r.updateHeight(i,0)}static empty(){return new xr(0,0)}static of(t){if(1==t.length)return t[0];let e=0,i=t.length,n=0,r=0;for(;;)if(e==i)if(n>2*r){let r=t[e-1];r.break?t.splice(--e,1,r.left,null,r.right):t.splice(--e,1,r.left,r.right),i+=1+r.break,n-=r.size}else{if(!(r>2*n))break;{let e=t[i];e.break?t.splice(i,1,e.left,null,e.right):t.splice(i,1,e.left,e.right),i+=2+e.break,r-=e.size}}else if(n<r){let i=t[e++];i&&(n+=i.size)}else{let e=t[--i];e&&(r+=e.size)}let s=0;return null==t[e-1]?(s=1,e--):null==t[e]&&(s=1,i++),new wr(mr.of(t.slice(0,e)),s,mr.of(t.slice(i)))}}function Qr(t,e){return t==e?t:(t.constructor!=e.constructor&&(cr=!0),e)}mr.prototype.size=1;class Sr extends mr{constructor(t,e,i){super(t,e),this.deco=i}blockAt(t,e,i,n){return new fr(n,this.length,i,this.height,this.deco||0)}lineAt(t,e,i,n,r){return this.blockAt(0,i,n,r)}forEachLine(t,e,i,n,r,s){t<=r+this.length&&e>=r&&s(this.blockAt(0,i,n,r))}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more&&this.setHeight(n.heights[n.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class xr extends Sr{constructor(t,e){super(t,e,null),this.collapsed=0,this.widgetHeight=0,this.breaks=0}blockAt(t,e,i,n){return new fr(n,this.length,i,this.height,this.breaks)}replace(t,e,i){let n=i[0];return 1==i.length&&(n instanceof xr||n instanceof yr&&4&n.flags)&&Math.abs(this.length-n.length)<10?(n instanceof yr?n=new xr(n.length,this.height):n.height=this.height,this.outdated||(n.outdated=!1),n):mr.of(i)}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more?this.setHeight(n.heights[n.index++]):(i||this.outdated)&&this.setHeight(Math.max(this.widgetHeight,t.heightForLine(this.length-this.collapsed))+this.breaks*t.lineHeight),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class yr extends mr{constructor(t){super(t,0)}heightMetrics(t,e){let i,n=t.doc.lineAt(e).number,r=t.doc.lineAt(e+this.length).number,s=r-n+1,o=0;if(t.lineWrapping){let e=Math.min(this.height,t.lineHeight*s);i=e/s,this.length>s+1&&(o=(this.height-e)/(this.length-s-1))}else i=this.height/s;return{firstLine:n,lastLine:r,perLine:i,perChar:o}}blockAt(t,e,i,n){let{firstLine:r,lastLine:s,perLine:o,perChar:a}=this.heightMetrics(e,n);if(e.lineWrapping){let r=n+(t<e.lineHeight?0:Math.round(Math.max(0,Math.min(1,(t-i)/this.height))*this.length)),s=e.doc.lineAt(r),l=o+s.length*a,h=Math.max(i,t-l/2);return new fr(s.from,s.length,h,l,0)}{let n=Math.max(0,Math.min(s-r,Math.floor((t-i)/o))),{from:a,length:l}=e.doc.line(r+n);return new fr(a,l,i+o*n,o,0)}}lineAt(t,e,i,n,r){if(e==pr.ByHeight)return this.blockAt(t,i,n,r);if(e==pr.ByPosNoHeight){let{from:e,to:n}=i.doc.lineAt(t);return new fr(e,n-e,0,0,0)}let{firstLine:s,perLine:o,perChar:a}=this.heightMetrics(i,r),l=i.doc.lineAt(t),h=o+l.length*a,c=l.number-s,O=n+o*c+a*(l.from-r-c);return new fr(l.from,l.length,Math.max(n,Math.min(O,n+this.height-h)),h,0)}forEachLine(t,e,i,n,r,s){t=Math.max(t,r),e=Math.min(e,r+this.length);let{firstLine:o,perLine:a,perChar:l}=this.heightMetrics(i,r);for(let h=t,c=n;h<=e;){let e=i.doc.lineAt(h);if(h==t){let i=e.number-o;c+=a*i+l*(t-r-i)}let n=a+l*e.length;s(new fr(e.from,e.length,c,n,0)),c+=n,h=e.to+1}}replace(t,e,i){let n=this.length-e;if(n>0){let t=i[i.length-1];t instanceof yr?i[i.length-1]=new yr(t.length+n):i.push(null,new yr(n-1))}if(t>0){let e=i[0];e instanceof yr?i[0]=new yr(t+e.length):i.unshift(new yr(t-1),null)}return mr.of(i)}decomposeLeft(t,e){e.push(new yr(t-1),null)}decomposeRight(t,e){e.push(null,new yr(this.length-t-1))}updateHeight(t,e=0,i=!1,n){let r=e+this.length;if(n&&n.from<=e+this.length&&n.more){let i=[],s=Math.max(e,n.from),o=-1;for(n.from>e&&i.push(new yr(n.from-e-1).updateHeight(t,e));s<=r&&n.more;){let e=t.doc.lineAt(s).length;i.length&&i.push(null);let r=n.heights[n.index++];-1==o?o=r:Math.abs(r-o)>=gr&&(o=-2);let a=new xr(e,r);a.outdated=!1,i.push(a),s+=e+1}s<=r&&i.push(null,new yr(r-s).updateHeight(t,s));let a=mr.of(i);return(o<0||Math.abs(a.height-this.height)>=gr||Math.abs(o-this.heightMetrics(t,e).perLine)>=gr)&&(cr=!0),Qr(this,a)}return(i||this.outdated)&&(this.setHeight(t.heightForGap(e,e+this.length)),this.outdated=!1),this}toString(){return`gap(${this.length})`}}class wr extends mr{constructor(t,e,i){super(t.length+e+i.length,t.height+i.height,e|(t.outdated||i.outdated?2:0)),this.left=t,this.right=i,this.size=t.size+i.size}get break(){return 1&this.flags}blockAt(t,e,i,n){let r=i+this.left.height;return t<r?this.left.blockAt(t,e,i,n):this.right.blockAt(t,e,r,n+this.left.length+this.break)}lineAt(t,e,i,n,r){let s=n+this.left.height,o=r+this.left.length+this.break,a=e==pr.ByHeight?t<s:t<o,l=a?this.left.lineAt(t,e,i,n,r):this.right.lineAt(t,e,i,s,o);if(this.break||(a?l.to<o:l.from>o))return l;let h=e==pr.ByPosNoHeight?pr.ByPosNoHeight:pr.ByPos;return a?l.join(this.right.lineAt(o,h,i,s,o)):this.left.lineAt(o,h,i,n,r).join(l)}forEachLine(t,e,i,n,r,s){let o=n+this.left.height,a=r+this.left.length+this.break;if(this.break)t<a&&this.left.forEachLine(t,e,i,n,r,s),e>=a&&this.right.forEachLine(t,e,i,o,a,s);else{let l=this.lineAt(a,pr.ByPos,i,n,r);t<l.from&&this.left.forEachLine(t,l.from-1,i,n,r,s),l.to>=t&&l.from<=e&&s(l),e>l.to&&this.right.forEachLine(l.to+1,e,i,o,a,s)}}replace(t,e,i){let n=this.left.length+this.break;if(e<n)return this.balanced(this.left.replace(t,e,i),this.right);if(t>this.left.length)return this.balanced(this.left,this.right.replace(t-n,e-n,i));let r=[];t>0&&this.decomposeLeft(t,r);let s=r.length;for(let t of i)r.push(t);if(t>0&&br(r,s-1),e<this.length){let t=r.length;this.decomposeRight(e,r),br(r,t)}return mr.of(r)}decomposeLeft(t,e){let i=this.left.length;if(t<=i)return this.left.decomposeLeft(t,e);e.push(this.left),this.break&&(i++,t>=i&&e.push(null)),t>i&&this.right.decomposeLeft(t-i,e)}decomposeRight(t,e){let i=this.left.length,n=i+this.break;if(t>=n)return this.right.decomposeRight(t-n,e);t<i&&this.left.decomposeRight(t,e),this.break&&t<n&&e.push(null),e.push(this.right)}balanced(t,e){return t.size>2*e.size||e.size>2*t.size?mr.of(this.break?[t,null,e]:[t,e]):(this.left=Qr(this.left,t),this.right=Qr(this.right,e),this.setHeight(t.height+e.height),this.outdated=t.outdated||e.outdated,this.size=t.size+e.size,this.length=t.length+this.break+e.length,this)}updateHeight(t,e=0,i=!1,n){let{left:r,right:s}=this,o=e+r.length+this.break,a=null;return n&&n.from<=e+r.length&&n.more?a=r=r.updateHeight(t,e,i,n):r.updateHeight(t,e,i),n&&n.from<=o+s.length&&n.more?a=s=s.updateHeight(t,o,i,n):s.updateHeight(t,o,i),a?this.balanced(r,s):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function br(t,e){let i,n;null==t[e]&&(i=t[e-1])instanceof yr&&(n=t[e+1])instanceof yr&&t.splice(e-1,3,new yr(i.length+1+n.length))}class kr{constructor(t,e){this.pos=t,this.oracle=e,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=t}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(t,e){if(this.lineStart>-1){let t=Math.min(e,this.lineEnd),i=this.nodes[this.nodes.length-1];i instanceof xr?i.length+=t-this.pos:(t>this.pos||!this.isCovered)&&this.nodes.push(new xr(t-this.pos,-1)),this.writtenTo=t,e>t&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=e}point(t,e,i){if(t<e||i.heightRelevant){let n=i.widget?i.widget.estimatedHeight:0,r=i.widget?i.widget.lineBreaks:0;n<0&&(n=this.oracle.lineHeight);let s=e-t;i.block?this.addBlock(new Sr(s,n,i)):(s||r||n>=5)&&this.addLineDeco(n,r,s)}else e>t&&this.span(t,e);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:t,to:e}=this.oracle.doc.lineAt(this.pos);this.lineStart=t,this.lineEnd=e,this.writtenTo<t&&((this.writtenTo<t-1||null==this.nodes[this.nodes.length-1])&&this.nodes.push(this.blankContent(this.writtenTo,t-1)),this.nodes.push(null)),this.pos>t&&this.nodes.push(new xr(this.pos-t,-1)),this.writtenTo=this.pos}blankContent(t,e){let i=new yr(e-t);return this.oracle.doc.lineAt(t).to==e&&(i.flags|=4),i}ensureLine(){this.enterLine();let t=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(t instanceof xr)return t;let e=new xr(0,-1);return this.nodes.push(e),e}addBlock(t){this.enterLine();let e=t.deco;e&&e.startSide>0&&!this.isCovered&&this.ensureLine(),this.nodes.push(t),this.writtenTo=this.pos=this.pos+t.length,e&&e.endSide>0&&(this.covering=t)}addLineDeco(t,e,i){let n=this.ensureLine();n.length+=i,n.collapsed+=i,n.widgetHeight=Math.max(n.widgetHeight,t),n.breaks+=e,this.writtenTo=this.pos=this.pos+i}finish(t){let e=0==this.nodes.length?null:this.nodes[this.nodes.length-1];!(this.lineStart>-1)||e instanceof xr||this.isCovered?(this.writtenTo<this.pos||null==e)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos)):this.nodes.push(new xr(0,-1));let i=t;for(let t of this.nodes)t instanceof xr&&t.updateHeight(this.oracle,i),i+=t?t.length:1;return this.nodes}static build(t,e,i,n){let r=new kr(i,t);return Pt.spans(e,i,n,r,0),r.finish(i)}}class vr{constructor(){this.changes=[]}compareRange(){}comparePoint(t,e,i,n){(t<e||i&&i.heightRelevant||n&&n.heightRelevant)&&si(t,e,this.changes,5)}}function $r(t,e){let i=t.getBoundingClientRect(),n=t.ownerDocument,r=n.defaultView||window,s=Math.max(0,i.left),o=Math.min(r.innerWidth,i.right),a=Math.max(0,i.top),l=Math.min(r.innerHeight,i.bottom);for(let e=t.parentNode;e&&e!=n.body;)if(1==e.nodeType){let i=e,n=window.getComputedStyle(i);if((i.scrollHeight>i.clientHeight||i.scrollWidth>i.clientWidth)&&"visible"!=n.overflow){let n=i.getBoundingClientRect();s=Math.max(s,n.left),o=Math.min(o,n.right),a=Math.max(a,n.top),l=Math.min(e==t.parentNode?r.innerHeight:l,n.bottom)}e="absolute"==n.position||"fixed"==n.position?i.offsetParent:i.parentNode}else{if(11!=e.nodeType)break;e=e.host}return{left:s-i.left,right:Math.max(s,o)-i.left,top:a-(i.top+e),bottom:Math.max(a,l)-(i.top+e)}}function Pr(t,e){let i=t.getBoundingClientRect();return{left:0,right:i.right-i.left,top:e,bottom:i.bottom-(i.top+e)}}class Tr{constructor(t,e,i,n){this.from=t,this.to=e,this.size=i,this.displaySize=n}static same(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let n=t[i],r=e[i];if(n.from!=r.from||n.to!=r.to||n.size!=r.size)return!1}return!0}draw(t,e){return ti.replace({widget:new Zr(this.displaySize*(e?t.scaleY:t.scaleX),e)}).range(this.from,this.to)}}class Zr extends Ke{constructor(t,e){super(),this.size=t,this.vertical=e}eq(t){return t.size==this.size&&t.vertical==this.vertical}toDOM(){let t=document.createElement("div");return this.vertical?t.style.height=this.size+"px":(t.style.width=this.size+"px",t.style.height="2px",t.style.display="inline-block"),t}get estimatedHeight(){return this.vertical?this.size:-1}}class Xr{constructor(t){this.state=t,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.scrollTop=0,this.scrolledToBottom=!1,this.scaleX=1,this.scaleY=1,this.scrollAnchorPos=0,this.scrollAnchorHeight=-1,this.scaler=Rr,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=ui.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1;let e=t.facet(Ji).some(t=>"function"!=typeof t&&"cm-lineWrapping"==t.class);this.heightOracle=new ur(e),this.stateDeco=t.facet(tn).filter(t=>"function"!=typeof t),this.heightMap=mr.empty().applyChanges(this.stateDeco,O.empty,this.heightOracle.setDoc(t.doc),[new hn(0,0,0,t.doc.length)]);for(let t=0;t<2&&(this.viewport=this.getViewport(0,null),this.updateForViewport());t++);this.updateViewportLines(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=ti.set(this.lineGaps.map(t=>t.draw(this,!1))),this.computeVisibleRanges()}updateForViewport(){let t=[this.viewport],{main:e}=this.state.selection;for(let i=0;i<=1;i++){let n=i?e.head:e.anchor;if(!t.some(({from:t,to:e})=>n>=t&&n<=e)){let{from:e,to:i}=this.lineBlockAt(n);t.push(new Ar(e,i))}}return this.viewports=t.sort((t,e)=>t.from-e.from),this.updateScaler()}updateScaler(){let t=this.scaler;return this.scaler=this.heightMap.height<=7e6?Rr:new _r(this.heightOracle,this.heightMap,this.viewports),t.eq(this.scaler)?0:2}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,t=>{this.viewportLines.push(zr(t,this.scaler))})}update(t,e=null){this.state=t.state;let i=this.stateDeco;this.stateDeco=this.state.facet(tn).filter(t=>"function"!=typeof t);let n=t.changedRanges,r=hn.extendWithRanges(n,function(t,e,i){let n=new vr;return Pt.compare(t,e,i,n,0),n.changes}(i,this.stateDeco,t?t.changes:P.empty(this.state.doc.length))),s=this.heightMap.height,o=this.scrolledToBottom?null:this.scrollAnchorAt(this.scrollTop);Or(),this.heightMap=this.heightMap.applyChanges(this.stateDeco,t.startState.doc,this.heightOracle.setDoc(this.state.doc),r),(this.heightMap.height!=s||cr)&&(t.flags|=2),o?(this.scrollAnchorPos=t.changes.mapPos(o.from,-1),this.scrollAnchorHeight=o.top):(this.scrollAnchorPos=-1,this.scrollAnchorHeight=s);let a=r.length?this.mapViewport(this.viewport,t.changes):this.viewport;(e&&(e.range.head<a.from||e.range.head>a.to)||!this.viewportIsAppropriate(a))&&(a=this.getViewport(0,e));let l=a.from!=this.viewport.from||a.to!=this.viewport.to;this.viewport=a,t.flags|=this.updateForViewport(),(l||!t.changes.empty||2&t.flags)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,t.changes))),t.flags|=this.computeVisibleRanges(t.changes),e&&(this.scrollTarget=e),!this.mustEnforceCursorAssoc&&t.selectionSet&&t.view.lineWrapping&&t.state.selection.main.empty&&t.state.selection.main.assoc&&!t.state.facet(Li)&&(this.mustEnforceCursorAssoc=!0)}measure(t){let e=t.contentDOM,i=window.getComputedStyle(e),n=this.heightOracle,r=i.whiteSpace;this.defaultTextDirection="rtl"==i.direction?ui.RTL:ui.LTR;let s=this.heightOracle.mustRefreshForWrapping(r),o=e.getBoundingClientRect(),a=s||this.mustMeasureContent||this.contentDOMHeight!=o.height;this.contentDOMHeight=o.height,this.mustMeasureContent=!1;let l=0,h=0;if(o.width&&o.height){let{scaleX:t,scaleY:i}=he(e,o);(t>.005&&Math.abs(this.scaleX-t)>.005||i>.005&&Math.abs(this.scaleY-i)>.005)&&(this.scaleX=t,this.scaleY=i,l|=16,s=a=!0)}let c=(parseInt(i.paddingTop)||0)*this.scaleY,u=(parseInt(i.paddingBottom)||0)*this.scaleY;this.paddingTop==c&&this.paddingBottom==u||(this.paddingTop=c,this.paddingBottom=u,l|=18),this.editorWidth!=t.scrollDOM.clientWidth&&(n.lineWrapping&&(a=!0),this.editorWidth=t.scrollDOM.clientWidth,l|=16);let d=t.scrollDOM.scrollTop*this.scaleY;this.scrollTop!=d&&(this.scrollAnchorHeight=-1,this.scrollTop=d),this.scrolledToBottom=me(t.scrollDOM);let f=(this.printing?Pr:$r)(e,this.paddingTop),p=f.top-this.pixelViewport.top,g=f.bottom-this.pixelViewport.bottom;this.pixelViewport=f;let m=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(m!=this.inView&&(this.inView=m,m&&(a=!0)),!this.inView&&!this.scrollTarget&&!function(t){let e=t.getBoundingClientRect(),i=t.ownerDocument.defaultView||window;return e.left<i.innerWidth&&e.right>0&&e.top<i.innerHeight&&e.bottom>0}(t.dom))return 0;let Q=o.width;if(this.contentDOMWidth==Q&&this.editorHeight==t.scrollDOM.clientHeight||(this.contentDOMWidth=o.width,this.editorHeight=t.scrollDOM.clientHeight,l|=16),a){let e=t.docView.measureVisibleLineHeights(this.viewport);if(n.mustRefreshForHeights(e)&&(s=!0),s||n.lineWrapping&&Math.abs(Q-this.contentDOMWidth)>n.charWidth){let{lineHeight:i,charWidth:o,textHeight:a}=t.docView.measureTextSize();s=i>0&&n.refresh(r,i,o,a,Math.max(5,Q/o),e),s&&(t.docView.minWidth=0,l|=16)}p>0&&g>0?h=Math.max(p,g):p<0&&g<0&&(h=Math.min(p,g)),Or();for(let i of this.viewports){let r=i.from==this.viewport.from?e:t.docView.measureVisibleLineHeights(i);this.heightMap=(s?mr.empty().applyChanges(this.stateDeco,O.empty,this.heightOracle,[new hn(0,0,0,t.state.doc.length)]):this.heightMap).updateHeight(n,0,s,new dr(i.from,r))}cr&&(l|=2)}let S=!this.viewportIsAppropriate(this.viewport,h)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return S&&(2&l&&(l|=this.updateScaler()),this.viewport=this.getViewport(h,this.scrollTarget),l|=this.updateForViewport()),(2&l||S)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(s?[]:this.lineGaps,t)),l|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,t.docView.enforceCursorAssoc()),l}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(t,e){let i=.5-Math.max(-.5,Math.min(.5,t/1e3/2)),n=this.heightMap,r=this.heightOracle,{visibleTop:s,visibleBottom:o}=this,a=new Ar(n.lineAt(s-1e3*i,pr.ByHeight,r,0,0).from,n.lineAt(o+1e3*(1-i),pr.ByHeight,r,0,0).to);if(e){let{head:t}=e.range;if(t<a.from||t>a.to){let i,s=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),o=n.lineAt(t,pr.ByPos,r,0,0);i="center"==e.y?(o.top+o.bottom)/2-s/2:"start"==e.y||"nearest"==e.y&&t<a.from?o.top:o.bottom-s,a=new Ar(n.lineAt(i-500,pr.ByHeight,r,0,0).from,n.lineAt(i+s+500,pr.ByHeight,r,0,0).to)}}return a}mapViewport(t,e){let i=e.mapPos(t.from,-1),n=e.mapPos(t.to,1);return new Ar(this.heightMap.lineAt(i,pr.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(n,pr.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:t,to:e},i=0){if(!this.inView)return!0;let{top:n}=this.heightMap.lineAt(t,pr.ByPos,this.heightOracle,0,0),{bottom:r}=this.heightMap.lineAt(e,pr.ByPos,this.heightOracle,0,0),{visibleTop:s,visibleBottom:o}=this;return(0==t||n<=s-Math.max(10,Math.min(-i,250)))&&(e==this.state.doc.length||r>=o+Math.max(10,Math.min(i,250)))&&n>s-2e3&&r<o+2e3}mapLineGaps(t,e){if(!t.length||e.empty)return t;let i=[];for(let n of t)e.touchesRange(n.from,n.to)||i.push(new Tr(e.mapPos(n.from),e.mapPos(n.to),n.size,n.displaySize));return i}ensureLineGaps(t,e){let i=this.heightOracle.lineWrapping,n=i?1e4:2e3,r=n>>1,s=n<<1;if(this.defaultTextDirection!=ui.LTR&&!i)return[];let o=[],a=(n,s,l,h)=>{if(s-n<r)return;let c=this.state.selection.main,O=[c.from];c.empty||O.push(c.to);for(let t of O)if(t>n&&t<s)return a(n,t-10,l,h),void a(t+10,s,l,h);let u=function(t,e){for(let i of t)if(e(i))return i}(t,t=>t.from>=l.from&&t.to<=l.to&&Math.abs(t.from-n)<r&&Math.abs(t.to-s)<r&&!O.some(e=>t.from<e&&t.to>e));if(!u){if(s<l.to&&e&&i&&e.visibleRanges.some(t=>t.from<=s&&t.to>=s)){let t=e.moveToLineBoundary(_.cursor(s),!1,!0).head;t>n&&(s=t)}let t=this.gapSize(l,n,s,h);u=new Tr(n,s,t,i||t<2e6?t:2e6)}o.push(u)},l=e=>{if(e.length<s||e.type!=Je.Text)return;let r=function(t,e,i){let n=[],r=t,s=0;return Pt.spans(i,t,e,{span(){},point(t,e){t>r&&(n.push({from:r,to:t}),s+=t-r),r=e}},20),r<e&&(n.push({from:r,to:e}),s+=e-r),{total:s,ranges:n}}(e.from,e.to,this.stateDeco);if(r.total<s)return;let o,l,h=this.scrollTarget?this.scrollTarget.range.head:null;if(i){let t,i,s=n/this.heightOracle.lineLength*this.heightOracle.lineHeight;if(null!=h){let n=Mr(r,h),o=((this.visibleBottom-this.visibleTop)/2+s)/e.height;t=n-o,i=n+o}else t=(this.visibleTop-e.top-s)/e.height,i=(this.visibleBottom-e.top+s)/e.height;o=Cr(r,t),l=Cr(r,i)}else{let i=r.total*this.heightOracle.charWidth,s=n*this.heightOracle.charWidth,a=0;if(i>2e6)for(let i of t)i.from>=e.from&&i.from<e.to&&i.size!=i.displaySize&&i.from*this.heightOracle.charWidth+a<this.pixelViewport.left&&(a=i.size-i.displaySize);let c,O,u=this.pixelViewport.left+a,d=this.pixelViewport.right+a;if(null!=h){let t=Mr(r,h),e=((d-u)/2+s)/i;c=t-e,O=t+e}else c=(u-s)/i,O=(d+s)/i;o=Cr(r,c),l=Cr(r,O)}o>e.from&&a(e.from,o,e,r),l<e.to&&a(l,e.to,e,r)};for(let t of this.viewportLines)Array.isArray(t.type)?t.type.forEach(l):l(t);return o}gapSize(t,e,i,n){let r=Mr(n,i)-Mr(n,e);return this.heightOracle.lineWrapping?t.height*r:n.total*this.heightOracle.charWidth*r}updateLineGaps(t){Tr.same(t,this.lineGaps)||(this.lineGaps=t,this.lineGapDeco=ti.set(t.map(t=>t.draw(this,this.heightOracle.lineWrapping))))}computeVisibleRanges(t){let e=this.stateDeco;this.lineGaps.length&&(e=e.concat(this.lineGapDeco));let i=[];Pt.spans(e,this.viewport.from,this.viewport.to,{span(t,e){i.push({from:t,to:e})},point(){}},20);let n=0;if(i.length!=this.visibleRanges.length)n=12;else for(let e=0;e<i.length&&!(8&n);e++){let r=this.visibleRanges[e],s=i[e];r.from==s.from&&r.to==s.to||(n|=4,t&&t.mapPos(r.from,-1)==s.from&&t.mapPos(r.to,1)==s.to||(n|=8))}return this.visibleRanges=i,n}lineBlockAt(t){return t>=this.viewport.from&&t<=this.viewport.to&&this.viewportLines.find(e=>e.from<=t&&e.to>=t)||zr(this.heightMap.lineAt(t,pr.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(t){return t>=this.viewportLines[0].top&&t<=this.viewportLines[this.viewportLines.length-1].bottom&&this.viewportLines.find(e=>e.top<=t&&e.bottom>=t)||zr(this.heightMap.lineAt(this.scaler.fromDOM(t),pr.ByHeight,this.heightOracle,0,0),this.scaler)}scrollAnchorAt(t){let e=this.lineBlockAtHeight(t+8);return e.from>=this.viewport.from||this.viewportLines[0].top-t>200?e:this.viewportLines[0]}elementAtHeight(t){return zr(this.heightMap.blockAt(this.scaler.fromDOM(t),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class Ar{constructor(t,e){this.from=t,this.to=e}}function Cr({total:t,ranges:e},i){if(i<=0)return e[0].from;if(i>=1)return e[e.length-1].to;let n=Math.floor(t*i);for(let t=0;;t++){let{from:i,to:r}=e[t],s=r-i;if(n<=s)return i+n;n-=s}}function Mr(t,e){let i=0;for(let{from:n,to:r}of t.ranges){if(e<=r){i+=e-n;break}i+=r-n}return i/t.total}const Rr={toDOM:t=>t,fromDOM:t=>t,scale:1,eq(t){return t==this}};class _r{constructor(t,e,i){let n=0,r=0,s=0;this.viewports=i.map(({from:i,to:r})=>{let s=e.lineAt(i,pr.ByPos,t,0,0).top,o=e.lineAt(r,pr.ByPos,t,0,0).bottom;return n+=o-s,{from:i,to:r,top:s,bottom:o,domTop:0,domBottom:0}}),this.scale=(7e6-n)/(e.height-n);for(let t of this.viewports)t.domTop=s+(t.top-r)*this.scale,s=t.domBottom=t.domTop+(t.bottom-t.top),r=t.bottom}toDOM(t){for(let e=0,i=0,n=0;;e++){let r=e<this.viewports.length?this.viewports[e]:null;if(!r||t<r.top)return n+(t-i)*this.scale;if(t<=r.bottom)return r.domTop+(t-r.top);i=r.bottom,n=r.domBottom}}fromDOM(t){for(let e=0,i=0,n=0;;e++){let r=e<this.viewports.length?this.viewports[e]:null;if(!r||t<r.domTop)return i+(t-n)/this.scale;if(t<=r.domBottom)return r.top+(t-r.domTop);i=r.bottom,n=r.domBottom}}eq(t){return t instanceof _r&&this.scale==t.scale&&this.viewports.length==t.viewports.length&&this.viewports.every((e,i)=>e.from==t.viewports[i].from&&e.to==t.viewports[i].to)}}function zr(t,e){if(1==e.scale)return t;let i=e.toDOM(t.top),n=e.toDOM(t.bottom);return new fr(t.from,t.length,i,n-i,Array.isArray(t._content)?t._content.map(t=>zr(t,e)):t._content)}const Yr=E.define({combine:t=>t.join(" ")}),Er=E.define({combine:t=>t.indexOf(!0)>-1}),Vr=Wt.newName(),qr=Wt.newName(),Lr=Wt.newName(),Dr={"&light":"."+qr,"&dark":"."+Lr};function Wr(t,e,i){return new Wt(e,{finish:e=>/&/.test(e)?e.replace(/&\w*/,e=>{if("&"==e)return t;if(!i||!i[e])throw new RangeError(`Unsupported selector: ${e}`);return i[e]}):t+" "+e})}const jr=Wr("."+Vr,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0,overflowAnchor:"none"},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",minHeight:"100%",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#ddd"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},".cm-iso":{unicodeBidi:"isolate"},".cm-announced":{position:"fixed",top:"-10000px"},"@media print":{".cm-announced":{display:"none"}},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",zIndex:200},".cm-gutters-before":{insetInlineStart:0},".cm-gutters-after":{insetInlineEnd:0},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",border:"0px solid #ddd","&.cm-gutters-before":{borderRightWidth:"1px"},"&.cm-gutters-after":{borderLeftWidth:"1px"}},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0,zIndex:300},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-dialog":{padding:"2px 19px 4px 6px",position:"relative","& label":{fontSize:"80%"}},".cm-dialog-close":{position:"absolute",top:"3px",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",fontSize:"14px",padding:"0"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top",userSelect:"none"},".cm-highlightSpace":{backgroundImage:"radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%)",backgroundPosition:"center"},".cm-highlightTab":{backgroundImage:'url(\'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>\')',backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},Dr),Br={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},Ir=Ee.ie&&Ee.ie_version<=11;class Gr{constructor(t){this.view=t,this.active=!1,this.editContext=null,this.selectionRange=new ce,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resizeScroll=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.printQuery=null,this.parentCheck=-1,this.dom=t.contentDOM,this.observer=new MutationObserver(e=>{for(let t of e)this.queue.push(t);(Ee.ie&&Ee.ie_version<=11||Ee.ios&&t.composing)&&e.some(t=>"childList"==t.type&&t.removedNodes.length||"characterData"==t.type&&t.oldValue.length>t.target.nodeValue.length)?this.flushSoon():this.flush()}),!window.EditContext||!Ee.android||!1===t.constructor.EDIT_CONTEXT||Ee.chrome&&Ee.chrome_version<126||(this.editContext=new Hr(t),t.state.facet(Gi)&&(t.contentDOM.editContext=this.editContext.editContext)),Ir&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),window.matchMedia&&(this.printQuery=window.matchMedia("print")),"function"==typeof ResizeObserver&&(this.resizeScroll=new ResizeObserver(()=>{var t;(null===(t=this.view.docView)||void 0===t?void 0:t.lastUpdate)<Date.now()-75&&this.onResize()}),this.resizeScroll.observe(t.scrollDOM)),this.addWindowListeners(this.win=t.win),this.start(),"function"==typeof IntersectionObserver&&(this.intersection=new IntersectionObserver(t=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),t.length>0&&t[t.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))},{threshold:[0,.001]}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver(t=>{t.length>0&&t[t.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))},{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(t){this.view.inputState.runHandlers("scroll",t),this.intersecting&&this.view.measure()}onScroll(t){this.intersecting&&this.flush(!1),this.editContext&&this.view.requestMeasure(this.editContext.measureReq),this.onScrollChanged(t)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout(()=>{this.resizeTimeout=-1,this.view.requestMeasure()},50))}onPrint(t){("change"!=t.type&&t.type||t.matches)&&(this.view.viewState.printing=!0,this.view.measure(),setTimeout(()=>{this.view.viewState.printing=!1,this.view.requestMeasure()},500))}updateGaps(t){if(this.gapIntersection&&(t.length!=this.gaps.length||this.gaps.some((e,i)=>e!=t[i]))){this.gapIntersection.disconnect();for(let e of t)this.gapIntersection.observe(e);this.gaps=t}}onSelectionChange(t){let e=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,n=this.selectionRange;if(i.state.facet(Gi)?i.root.activeElement!=this.dom:!te(this.dom,n))return;let r=n.anchorNode&&i.docView.nearest(n.anchorNode);r&&r.ignoreEvent(t)?e||(this.selectionChanged=!1):(Ee.ie&&Ee.ie_version<=11||Ee.android&&Ee.chrome)&&!i.state.selection.main.empty&&n.focusNode&&ie(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:t}=this,e=Kt(t.root);if(!e)return!1;let i=Ee.safari&&11==t.root.nodeType&&t.root.activeElement==this.dom&&function(t,e){if(e.getComposedRanges){let i=e.getComposedRanges(t.root)[0];if(i)return Ur(t,i)}let i=null;function n(t){t.preventDefault(),t.stopImmediatePropagation(),i=t.getTargetRanges()[0]}return t.contentDOM.addEventListener("beforeinput",n,!0),t.dom.ownerDocument.execCommand("indent"),t.contentDOM.removeEventListener("beforeinput",n,!0),i?Ur(t,i):null}(this.view,e)||e;if(!i||this.selectionRange.eq(i))return!1;let n=te(this.dom,i);return n&&!this.selectionChanged&&t.inputState.lastFocusTime>Date.now()-200&&t.inputState.lastTouchTime<Date.now()-300&&function(t,e){let i=e.focusNode,n=e.focusOffset;if(!i||e.anchorNode!=i||e.anchorOffset!=n)return!1;for(n=Math.min(n,oe(i));;)if(n){if(1!=i.nodeType)return!1;let t=i.childNodes[n-1];"false"==t.contentEditable?n--:(i=t,n=oe(i))}else{if(i==t)return!0;n=ne(i),i=i.parentNode}}(this.dom,i)?(this.view.inputState.lastFocusTime=0,t.docView.updateSelection(),!1):(this.selectionRange.setRange(i),n&&(this.selectionChanged=!0),!0)}setSelectionRange(t,e){this.selectionRange.set(t.node,t.offset,e.node,e.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let t=0,e=null;for(let i=this.dom;i;)if(1==i.nodeType)!e&&t<this.scrollTargets.length&&this.scrollTargets[t]==i?t++:e||(e=this.scrollTargets.slice(0,t)),e&&e.push(i),i=i.assignedSlot||i.parentNode;else{if(11!=i.nodeType)break;i=i.host}if(t<this.scrollTargets.length&&!e&&(e=this.scrollTargets.slice(0,t)),e){for(let t of this.scrollTargets)t.removeEventListener("scroll",this.onScroll);for(let t of this.scrollTargets=e)t.addEventListener("scroll",this.onScroll)}}ignore(t){if(!this.active)return t();try{return this.stop(),t()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,Br),Ir&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),Ir&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(t,e){var i;if(!this.delayedAndroidKey){let t=()=>{let t=this.delayedAndroidKey;t&&(this.clearDelayedAndroidKey(),this.view.inputState.lastKeyCode=t.keyCode,this.view.inputState.lastKeyTime=Date.now(),!this.flush()&&t.force&&pe(this.dom,t.key,t.keyCode))};this.flushingAndroidKey=this.view.win.requestAnimationFrame(t)}this.delayedAndroidKey&&"Enter"!=t||(this.delayedAndroidKey={key:t,keyCode:e,force:this.lastChange<Date.now()-50||!!(null===(i=this.delayedAndroidKey)||void 0===i?void 0:i.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame(()=>{this.delayedFlush=-1,this.flush()}))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}pendingRecords(){for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}processRecords(){let t=this.pendingRecords();t.length&&(this.queue=[]);let e=-1,i=-1,n=!1;for(let r of t){let t=this.readMutation(r);t&&(t.typeOver&&(n=!0),-1==e?({from:e,to:i}=t):(e=Math.min(t.from,e),i=Math.max(t.to,i)))}return{from:e,to:i,typeOver:n}}readChange(){let{from:t,to:e,typeOver:i}=this.processRecords(),n=this.selectionChanged&&te(this.dom,this.selectionRange);if(t<0&&!n)return null;t>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1;let r=new Cn(this.view,t,e,i);return this.view.docView.domChanged={newSel:r.newSel?r.newSel.main:null},r}flush(t=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;t&&this.readSelectionRange();let e=this.readChange();if(!e)return this.view.requestMeasure(),!1;let i=this.view.state,n=Mn(this.view,e);return this.view.state==i&&(e.domChanged||e.newSel&&!e.newSel.main.eq(this.view.state.selection.main))&&this.view.update([]),n}readMutation(t){let e=this.view.docView.nearest(t.target);if(!e||e.ignoreMutation(t))return null;if(e.markDirty("attributes"==t.type),"attributes"==t.type&&(e.flags|=4),"childList"==t.type){let i=Nr(e,t.previousSibling||t.target.previousSibling,-1),n=Nr(e,t.nextSibling||t.target.nextSibling,1);return{from:i?e.posAfter(i):e.posAtStart,to:n?e.posBefore(n):e.posAtEnd,typeOver:!1}}return"characterData"==t.type?{from:e.posAtStart,to:e.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}:null}setWindow(t){t!=this.win&&(this.removeWindowListeners(this.win),this.win=t,this.addWindowListeners(this.win))}addWindowListeners(t){t.addEventListener("resize",this.onResize),this.printQuery?this.printQuery.addEventListener?this.printQuery.addEventListener("change",this.onPrint):this.printQuery.addListener(this.onPrint):t.addEventListener("beforeprint",this.onPrint),t.addEventListener("scroll",this.onScroll),t.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(t){t.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onResize),this.printQuery?this.printQuery.removeEventListener?this.printQuery.removeEventListener("change",this.onPrint):this.printQuery.removeListener(this.onPrint):t.removeEventListener("beforeprint",this.onPrint),t.document.removeEventListener("selectionchange",this.onSelectionChange)}update(t){this.editContext&&(this.editContext.update(t),t.startState.facet(Gi)!=t.state.facet(Gi)&&(t.view.contentDOM.editContext=t.state.facet(Gi)?this.editContext.editContext:null))}destroy(){var t,e,i;this.stop(),null===(t=this.intersection)||void 0===t||t.disconnect(),null===(e=this.gapIntersection)||void 0===e||e.disconnect(),null===(i=this.resizeScroll)||void 0===i||i.disconnect();for(let t of this.scrollTargets)t.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey),this.editContext&&(this.view.contentDOM.editContext=null,this.editContext.destroy())}}function Nr(t,e,i){for(;e;){let n=we.get(e);if(n&&n.parent==t)return n;let r=e.parentNode;e=r!=t.dom?r:i>0?e.nextSibling:e.previousSibling}return null}function Ur(t,e){let i=e.startContainer,n=e.startOffset,r=e.endContainer,s=e.endOffset,o=t.docView.domAtPos(t.state.selection.main.anchor);return ie(o.node,o.offset,r,s)&&([i,n,r,s]=[r,s,i,n]),{anchorNode:i,anchorOffset:n,focusNode:r,focusOffset:s}}class Hr{constructor(t){this.from=0,this.to=0,this.pendingContextChange=null,this.handlers=Object.create(null),this.composing=null,this.resetRange(t.state);let e=this.editContext=new window.EditContext({text:t.state.doc.sliceString(this.from,this.to),selectionStart:this.toContextPos(Math.max(this.from,Math.min(this.to,t.state.selection.main.anchor))),selectionEnd:this.toContextPos(t.state.selection.main.head)});this.handlers.textupdate=i=>{let n=t.state.selection.main,{anchor:r,head:s}=n,o=this.toEditorPos(i.updateRangeStart),a=this.toEditorPos(i.updateRangeEnd);t.inputState.composing>=0&&!this.composing&&(this.composing={contextBase:i.updateRangeStart,editorBase:o,drifted:!1});let l={from:o,to:a,insert:O.of(i.text.split("\n"))};if(l.from==this.from&&r<this.from?l.from=r:l.to==this.to&&r>this.to&&(l.to=r),l.from==l.to&&!l.insert.length){let e=_.single(this.toEditorPos(i.selectionStart),this.toEditorPos(i.selectionEnd));return void(e.main.eq(n)||t.dispatch({selection:e,userEvent:"select"}))}if((Ee.mac||Ee.android)&&l.from==s-1&&/^\. ?$/.test(i.text)&&"off"==t.contentDOM.getAttribute("autocorrect")&&(l={from:o,to:a,insert:O.of([i.text.replace("."," ")])}),this.pendingContextChange=l,!t.state.readOnly){let e=this.to-this.from+(l.to-l.from+l.insert.length);Rn(t,l,_.single(this.toEditorPos(i.selectionStart,e),this.toEditorPos(i.selectionEnd,e)))}this.pendingContextChange&&(this.revertPending(t.state),this.setSelection(t.state)),l.from<l.to&&!l.insert.length&&t.inputState.composing>=0&&!/[\\p{Alphabetic}\\p{Number}_]/.test(e.text.slice(Math.max(0,i.updateRangeStart-1),Math.min(e.text.length,i.updateRangeStart+1)))&&this.handlers.compositionend(i)},this.handlers.characterboundsupdate=i=>{let n=[],r=null;for(let e=this.toEditorPos(i.rangeStart),s=this.toEditorPos(i.rangeEnd);e<s;e++){let i=t.coordsForChar(e);r=i&&new DOMRect(i.left,i.top,i.right-i.left,i.bottom-i.top)||r||new DOMRect,n.push(r)}e.updateCharacterBounds(i.rangeStart,n)},this.handlers.textformatupdate=e=>{let i=[];for(let t of e.getTextFormats()){let e=t.underlineStyle,n=t.underlineThickness;if(!/none/i.test(e)&&!/none/i.test(n)){let r=this.toEditorPos(t.rangeStart),s=this.toEditorPos(t.rangeEnd);if(r<s){let t=`text-decoration: underline ${/^[a-z]/.test(e)?e+" ":"Dashed"==e?"dashed ":"Squiggle"==e?"wavy ":""}${/thin/i.test(n)?1:2}px`;i.push(ti.mark({attributes:{style:t}}).range(r,s))}}}t.dispatch({effects:Bi.of(ti.set(i))})},this.handlers.compositionstart=()=>{t.inputState.composing<0&&(t.inputState.composing=0,t.inputState.compositionFirstChange=!0)},this.handlers.compositionend=()=>{if(t.inputState.composing=-1,t.inputState.compositionFirstChange=null,this.composing){let{drifted:e}=this.composing;this.composing=null,e&&this.reset(t.state)}};for(let t in this.handlers)e.addEventListener(t,this.handlers[t]);this.measureReq={read:t=>{this.editContext.updateControlBounds(t.contentDOM.getBoundingClientRect());let e=Kt(t.root);e&&e.rangeCount&&this.editContext.updateSelectionBounds(e.getRangeAt(0).getBoundingClientRect())}}}applyEdits(t){let e=0,i=!1,n=this.pendingContextChange;return t.changes.iterChanges((r,s,o,a,l)=>{if(i)return;let h=l.length-(s-r);if(n&&s>=n.to){if(n.from==r&&n.to==s&&n.insert.eq(l))return n=this.pendingContextChange=null,e+=h,void(this.to+=h);n=null,this.revertPending(t.state)}if(r+=e,(s+=e)<=this.from)this.from+=h,this.to+=h;else if(r<this.to){if(r<this.from||s>this.to||this.to-this.from+l.length>3e4)return void(i=!0);this.editContext.updateText(this.toContextPos(r),this.toContextPos(s),l.toString()),this.to+=h}e+=h}),n&&!i&&this.revertPending(t.state),!i}update(t){let e=this.pendingContextChange,i=t.startState.selection.main;this.composing&&(this.composing.drifted||!t.changes.touchesRange(i.from,i.to)&&t.transactions.some(t=>!t.isUserEvent("input.type")&&t.changes.touchesRange(this.from,this.to)))?(this.composing.drifted=!0,this.composing.editorBase=t.changes.mapPos(this.composing.editorBase)):this.applyEdits(t)&&this.rangeIsValid(t.state)?(t.docChanged||t.selectionSet||e)&&this.setSelection(t.state):(this.pendingContextChange=null,this.reset(t.state)),(t.geometryChanged||t.docChanged||t.selectionSet)&&t.view.requestMeasure(this.measureReq)}resetRange(t){let{head:e}=t.selection.main;this.from=Math.max(0,e-1e4),this.to=Math.min(t.doc.length,e+1e4)}reset(t){this.resetRange(t),this.editContext.updateText(0,this.editContext.text.length,t.doc.sliceString(this.from,this.to)),this.setSelection(t)}revertPending(t){let e=this.pendingContextChange;this.pendingContextChange=null,this.editContext.updateText(this.toContextPos(e.from),this.toContextPos(e.from+e.insert.length),t.doc.sliceString(e.from,e.to))}setSelection(t){let{main:e}=t.selection,i=this.toContextPos(Math.max(this.from,Math.min(this.to,e.anchor))),n=this.toContextPos(e.head);this.editContext.selectionStart==i&&this.editContext.selectionEnd==n||this.editContext.updateSelection(i,n)}rangeIsValid(t){let{head:e}=t.selection.main;return!(this.from>0&&e-this.from<500||this.to<t.doc.length&&this.to-e<500||this.to-this.from>3e4)}toEditorPos(t,e=this.to-this.from){t=Math.min(t,e);let i=this.composing;return i&&i.drifted?i.editorBase+(t-i.contextBase):t+this.from}toContextPos(t){let e=this.composing;return e&&e.drifted?e.contextBase+(t-e.editorBase):t-this.from}destroy(){for(let t in this.handlers)this.editContext.removeEventListener(t,this.handlers[t])}}class Fr{get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return!!this.inputState&&this.inputState.composing>0}get compositionStarted(){return!!this.inputState&&this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}constructor(t={}){var e;this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.className="cm-announced",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),t.parent&&t.parent.appendChild(this.dom);let{dispatch:i}=t;this.dispatchTransactions=t.dispatchTransactions||i&&(t=>t.forEach(t=>i(t,this)))||(t=>this.update(t)),this.dispatch=this.dispatch.bind(this),this._root=t.root||function(t){for(;t;){if(t&&(9==t.nodeType||11==t.nodeType&&t.host))return t;t=t.assignedSlot||t.parentNode}return null}(t.parent)||document,this.viewState=new Xr(t.state||yt.create(t)),t.scrollTo&&t.scrollTo.is(ji)&&(this.viewState.scrollTarget=t.scrollTo.value.clip(this.viewState.state)),this.plugins=this.state.facet(Ui).map(t=>new Fi(t));for(let t of this.plugins)t.update(this);this.observer=new Gr(this),this.inputState=new _n(this),this.inputState.ensureHandlers(this.plugins),this.docView=new On(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure(),(null===(e=document.fonts)||void 0===e?void 0:e.ready)&&document.fonts.ready.then(()=>this.requestMeasure())}dispatch(...t){let e=1==t.length&&t[0]instanceof Ot?t:1==t.length&&Array.isArray(t[0])?t[0]:[this.state.update(...t)];this.dispatchTransactions(e,this)}update(t){if(0!=this.updateState)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let e,i=!1,n=!1,r=this.state;for(let e of t){if(e.startState!=r)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");r=e.state}if(this.destroyed)return void(this.viewState.state=r);let s=this.hasFocus,o=0,a=null;t.some(t=>t.annotation(sr))?(this.inputState.notifiedFocused=s,o=1):s!=this.inputState.notifiedFocused&&(this.inputState.notifiedFocused=s,a=or(r,s),a||(o=1));let l=this.observer.delayedAndroidKey,h=null;if(l?(this.observer.clearDelayedAndroidKey(),h=this.observer.readChange(),(h&&!this.state.doc.eq(r.doc)||!this.state.selection.eq(r.selection))&&(h=null)):this.observer.clear(),r.facet(yt.phrases)!=this.state.facet(yt.phrases))return this.setState(r);e=cn.create(this,r,t),e.flags|=o;let c=this.viewState.scrollTarget;try{this.updateState=2;for(let e of t){if(c&&(c=c.map(e.changes)),e.scrollIntoView){let{main:t}=e.state.selection;c=new Wi(t.empty?t:_.cursor(t.head,t.head>t.anchor?-1:1))}for(let t of e.effects)t.is(ji)&&(c=t.value.clip(this.state))}this.viewState.update(e,c),this.bidiCache=ts.update(this.bidiCache,e.changes),e.empty||(this.updatePlugins(e),this.inputState.update(e)),i=this.docView.update(e),this.state.facet(ln)!=this.styleModules&&this.mountStyles(),n=this.updateAttrs(),this.showAnnouncements(t),this.docView.updateSelection(i,t.some(t=>t.isUserEvent("select.pointer")))}finally{this.updateState=0}if(e.startState.facet(Yr)!=e.state.facet(Yr)&&(this.viewState.mustMeasureContent=!0),(i||n||c||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),i&&this.docViewUpdate(),!e.empty)for(let t of this.state.facet(_i))try{t(e)}catch(t){Ii(this.state,t,"update listener")}(a||h)&&Promise.resolve().then(()=>{a&&this.state==a.startState&&this.dispatch(a),h&&!Mn(this,h)&&l.force&&pe(this.contentDOM,l.key,l.keyCode)})}setState(t){if(0!=this.updateState)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed)return void(this.viewState.state=t);this.updateState=2;let e=this.hasFocus;try{for(let t of this.plugins)t.destroy(this);this.viewState=new Xr(t),this.plugins=t.facet(Ui).map(t=>new Fi(t)),this.pluginMap.clear();for(let t of this.plugins)t.update(this);this.docView.destroy(),this.docView=new On(this),this.inputState.ensureHandlers(this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}e&&this.focus(),this.requestMeasure()}updatePlugins(t){let e=t.startState.facet(Ui),i=t.state.facet(Ui);if(e!=i){let n=[];for(let r of i){let i=e.indexOf(r);if(i<0)n.push(new Fi(r));else{let e=this.plugins[i];e.mustUpdate=t,n.push(e)}}for(let e of this.plugins)e.mustUpdate!=t&&e.destroy(this);this.plugins=n,this.pluginMap.clear()}else for(let e of this.plugins)e.mustUpdate=t;for(let t=0;t<this.plugins.length;t++)this.plugins[t].update(this);e!=i&&this.inputState.ensureHandlers(this.plugins)}docViewUpdate(){for(let t of this.plugins){let e=t.value;if(e&&e.docViewUpdate)try{e.docViewUpdate(this)}catch(t){Ii(this.state,t,"doc view update listener")}}}measure(t=!0){if(this.destroyed)return;if(this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.observer.delayedAndroidKey)return this.measureScheduled=-1,void this.requestMeasure();this.measureScheduled=0,t&&this.observer.forceFlush();let e=null,i=this.scrollDOM,n=i.scrollTop*this.scaleY,{scrollAnchorPos:r,scrollAnchorHeight:s}=this.viewState;Math.abs(n-this.viewState.scrollTop)>1&&(s=-1),this.viewState.scrollAnchorHeight=-1;try{for(let t=0;;t++){if(s<0)if(me(i))r=-1,s=this.viewState.heightMap.height;else{let t=this.viewState.scrollAnchorAt(n);r=t.from,s=t.top}this.updateState=1;let o=this.viewState.measure(this);if(!o&&!this.measureRequests.length&&null==this.viewState.scrollTarget)break;if(t>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let a=[];4&o||([this.measureRequests,a]=[a,this.measureRequests]);let l=a.map(t=>{try{return t.read(this)}catch(t){return Ii(this.state,t),Jr}}),h=cn.create(this,this.state,[]),c=!1;h.flags|=o,e?e.flags|=o:e=h,this.updateState=2,h.empty||(this.updatePlugins(h),this.inputState.update(h),this.updateAttrs(),c=this.docView.update(h),c&&this.docViewUpdate());for(let t=0;t<a.length;t++)if(l[t]!=Jr)try{let e=a[t];e.write&&e.write(l[t],this)}catch(t){Ii(this.state,t)}if(c&&this.docView.updateSelection(!0),!h.viewportChanged&&0==this.measureRequests.length){if(this.viewState.editorHeight){if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,s=-1;continue}{let t=(r<0?this.viewState.heightMap.height:this.viewState.lineBlockAt(r).top)-s;if(t>1||t<-1){n+=t,i.scrollTop=n/this.scaleY,s=-1;continue}}}break}}}finally{this.updateState=0,this.measureScheduled=-1}if(e&&!e.empty)for(let t of this.state.facet(_i))t(e)}get themeClasses(){return Vr+" "+(this.state.facet(Er)?Lr:qr)+" "+this.state.facet(Yr)}updateAttrs(){let t=es(this,Ki,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),e={spellcheck:"false",autocorrect:"off",autocapitalize:"off",writingsuggestions:"false",translate:"no",contenteditable:this.state.facet(Gi)?"true":"false",class:"cm-content",style:`${Ee.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(e["aria-readonly"]="true"),es(this,Ji,e);let i=this.observer.ignore(()=>{let i=He(this.contentDOM,this.contentAttrs,e),n=He(this.dom,this.editorAttrs,t);return i||n});return this.editorAttrs=t,this.contentAttrs=e,i}showAnnouncements(t){let e=!0;for(let i of t)for(let t of i.effects)t.is(Fr.announce)&&(e&&(this.announceDOM.textContent=""),e=!1,this.announceDOM.appendChild(document.createElement("div")).textContent=t.value)}mountStyles(){this.styleModules=this.state.facet(ln);let t=this.state.facet(Fr.cspNonce);Wt.mount(this.root,this.styleModules.concat(jr).reverse(),t?{nonce:t}:void 0)}readMeasured(){if(2==this.updateState)throw new Error("Reading the editor layout isn't allowed during an update");0==this.updateState&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(t){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame(()=>this.measure())),t){if(this.measureRequests.indexOf(t)>-1)return;if(null!=t.key)for(let e=0;e<this.measureRequests.length;e++)if(this.measureRequests[e].key===t.key)return void(this.measureRequests[e]=t);this.measureRequests.push(t)}}plugin(t){let e=this.pluginMap.get(t);return(void 0===e||e&&e.plugin!=t)&&this.pluginMap.set(t,e=this.plugins.find(e=>e.plugin==t)||null),e&&e.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}get scaleX(){return this.viewState.scaleX}get scaleY(){return this.viewState.scaleY}elementAtHeight(t){return this.readMeasured(),this.viewState.elementAtHeight(t)}lineBlockAtHeight(t){return this.readMeasured(),this.viewState.lineBlockAtHeight(t)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(t){return this.viewState.lineBlockAt(t)}get contentHeight(){return this.viewState.contentHeight}moveByChar(t,e,i){return Pn(this,t,kn(this,t,e,i))}moveByGroup(t,e){return Pn(this,t,kn(this,t,e,e=>function(t,e,i){let n=t.state.charCategorizer(e),r=n(i);return t=>{let e=n(t);return r==Qt.Space&&(r=e),r==e}}(this,t.head,e)))}visualLineSide(t,e){let i=this.bidiSpans(t),n=this.textDirectionAt(t.from),r=i[e?i.length-1:0];return _.cursor(r.side(e,n)+t.from,r.forward(!e,n)?1:-1)}moveToLineBoundary(t,e,i=!0){return function(t,e,i,n){let r=function(t,e,i){let n=t.lineBlockAt(e);if(Array.isArray(n.type)){let t;for(let r of n.type){if(r.from>e)break;if(!(r.to<e)){if(r.from<e&&r.to>e)return r;t&&(r.type!=Je.Text||t.type==r.type&&!(i<0?r.from<e:r.to>e))||(t=r)}}return t||n}return n}(t,e.head,e.assoc||-1),s=n&&r.type==Je.Text&&(t.lineWrapping||r.widgetLineBreaks)?t.coordsAtPos(e.assoc<0&&e.head>r.from?e.head-1:e.head):null;if(s){let e=t.dom.getBoundingClientRect(),n=t.textDirectionAt(r.from),o=t.posAtCoords({x:i==(n==ui.LTR)?e.right-1:e.left+1,y:(s.top+s.bottom)/2});if(null!=o)return _.cursor(o,i?-1:1)}return _.cursor(i?r.to:r.from,i?-1:1)}(this,t,e,i)}moveVertically(t,e,i){return Pn(this,t,function(t,e,i,n){let r=e.head,s=i?1:-1;if(r==(i?t.state.doc.length:0))return _.cursor(r,e.assoc);let o,a=e.goalColumn,l=t.contentDOM.getBoundingClientRect(),h=t.coordsAtPos(r,e.assoc||-1),c=t.documentTop;if(h)null==a&&(a=h.left-l.left),o=s<0?h.top:h.bottom;else{let e=t.viewState.lineBlockAt(r);null==a&&(a=Math.min(l.right-l.left,t.defaultCharacterWidth*(r-e.from))),o=(s<0?e.top:e.bottom)+c}let O=l.left+a,u=null!=n?n:t.viewState.heightOracle.textHeight>>1;for(let e=0;;e+=10){let i=o+(u+e)*s,n=yn(t,{x:O,y:i},!1,s);if(i<l.top||i>l.bottom||(s<0?n<r:n>r)){let e=t.docView.coordsForChar(n),r=!e||i<e.top?-1:1;return _.cursor(n,r,void 0,a)}}}(this,t,e,i))}domAtPos(t){return this.docView.domAtPos(t)}posAtDOM(t,e=0){return this.docView.posFromDOM(t,e)}posAtCoords(t,e=!0){return this.readMeasured(),yn(this,t,e)}coordsAtPos(t,e=1){this.readMeasured();let i=this.docView.coordsAt(t,e);if(!i||i.left==i.right)return i;let n=this.state.doc.lineAt(t),r=this.bidiSpans(n);return ae(i,r[wi.find(r,t-n.from,-1,e)].dir==ui.LTR==e>0)}coordsForChar(t){return this.readMeasured(),this.docView.coordsForChar(t)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(t){return!this.state.facet(qi)||t<this.viewport.from||t>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(t))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(t){if(t.length>Kr)return Pi(t.length);let e,i=this.textDirectionAt(t.from);for(let n of this.bidiCache)if(n.from==t.from&&n.dir==i&&(n.fresh||bi(n.isolates,e=sn(this,t))))return n.order;e||(e=sn(this,t));let n=function(t,e,i){if(!t)return[new wi(0,0,e==fi?1:0)];if(e==di&&!i.length&&!yi.test(t))return Pi(t.length);if(i.length)for(;t.length>ki.length;)ki[ki.length]=256;let n=[],r=e==di?0:1;return $i(t,r,r,i,0,t.length,n),n}(t.text,i,e);return this.bidiCache.push(new ts(t.from,t.to,i,e,!0,n)),n}get hasFocus(){var t;return(this.dom.ownerDocument.hasFocus()||Ee.safari&&(null===(t=this.inputState)||void 0===t?void 0:t.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore(()=>{de(this.contentDOM),this.docView.updateSelection()})}setRoot(t){this._root!=t&&(this._root=t,this.observer.setWindow((9==t.nodeType?t:t.ownerDocument).defaultView||window),this.mountStyles())}destroy(){this.root.activeElement==this.contentDOM&&this.contentDOM.blur();for(let t of this.plugins)t.destroy(this);this.plugins=[],this.inputState.destroy(),this.docView.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(t,e={}){return ji.of(new Wi("number"==typeof t?_.cursor(t):t,e.y,e.x,e.yMargin,e.xMargin))}scrollSnapshot(){let{scrollTop:t,scrollLeft:e}=this.scrollDOM,i=this.viewState.scrollAnchorAt(t);return ji.of(new Wi(_.cursor(i.from),"start","start",i.top-t,e,!0))}setTabFocusMode(t){null==t?this.inputState.tabFocusMode=this.inputState.tabFocusMode<0?0:-1:"boolean"==typeof t?this.inputState.tabFocusMode=t?0:-1:0!=this.inputState.tabFocusMode&&(this.inputState.tabFocusMode=Date.now()+t)}static domEventHandlers(t){return Hi.define(()=>({}),{eventHandlers:t})}static domEventObservers(t){return Hi.define(()=>({}),{eventObservers:t})}static theme(t,e){let i=Wt.newName(),n=[Yr.of(i),ln.of(Wr(`.${i}`,t))];return e&&e.dark&&n.push(Er.of(!0)),n}static baseTheme(t){return G.lowest(ln.of(Wr("."+Vr,t,Dr)))}static findFromDOM(t){var e;let i=t.querySelector(".cm-content"),n=i&&we.get(i)||we.get(t);return(null===(e=null==n?void 0:n.rootView)||void 0===e?void 0:e.view)||null}}Fr.styleModule=ln,Fr.inputHandler=zi,Fr.clipboardInputFilter=Ei,Fr.clipboardOutputFilter=Vi,Fr.scrollHandler=Di,Fr.focusChangeEffect=Yi,Fr.perLineTextDirection=qi,Fr.exceptionSink=Ri,Fr.updateListener=_i,Fr.editable=Gi,Fr.mouseSelectionStyle=Mi,Fr.dragMovesSelection=Ci,Fr.clickAddsSelectionRange=Ai,Fr.decorations=tn,Fr.outerDecorations=en,Fr.atomicRanges=nn,Fr.bidiIsolatedRanges=rn,Fr.scrollMargins=on,Fr.darkTheme=Er,Fr.cspNonce=E.define({combine:t=>t.length?t[0]:""}),Fr.contentAttributes=Ji,Fr.editorAttributes=Ki,Fr.lineWrapping=Fr.contentAttributes.of({class:"cm-lineWrapping"}),Fr.announce=ct.define();const Kr=4096,Jr={};class ts{constructor(t,e,i,n,r,s){this.from=t,this.to=e,this.dir=i,this.isolates=n,this.fresh=r,this.order=s}static update(t,e){if(e.empty&&!t.some(t=>t.fresh))return t;let i=[],n=t.length?t[t.length-1].dir:ui.LTR;for(let r=Math.max(0,t.length-10);r<t.length;r++){let s=t[r];s.dir!=n||e.touchesRange(s.from,s.to)||i.push(new ts(e.mapPos(s.from,1),e.mapPos(s.to,-1),s.dir,s.isolates,!1,s.order))}return i}}function es(t,e,i){for(let n=t.state.facet(e),r=n.length-1;r>=0;r--){let e=n[r],s="function"==typeof e?e(t):e;s&&Ge(s,i)}return i}const is=Ee.mac?"mac":Ee.windows?"win":Ee.linux?"linux":"key";function ns(t,e,i){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),!1!==i&&e.shiftKey&&(t="Shift-"+t),t}const rs=G.default(Fr.domEventHandlers({keydown:(t,e)=>function(t,e,i,n){hs=e;let r=function(t){var e=!(Nt&&t.metaKey&&t.shiftKey&&!t.ctrlKey&&!t.altKey||Ut&&t.shiftKey&&t.key&&1==t.key.length||"Unidentified"==t.key)&&t.key||(t.shiftKey?Gt:It)[t.keyCode]||t.key||"Unidentified";return"Esc"==e&&(e="Escape"),"Del"==e&&(e="Delete"),"Left"==e&&(e="ArrowLeft"),"Up"==e&&(e="ArrowUp"),"Right"==e&&(e="ArrowRight"),"Down"==e&&(e="ArrowDown"),e}(e),s=b(w(r,0))==r.length&&" "!=r,o="",a=!1,l=!1,h=!1;as&&as.view==i&&as.scope==n&&(o=as.prefix+" ",qn.indexOf(e.keyCode)<0&&(l=!0,as=null));let c,O,u=new Set,d=t=>{if(t){for(let e of t.run)if(!u.has(e)&&(u.add(e),e(i)))return t.stopPropagation&&(h=!0),!0;t.preventDefault&&(t.stopPropagation&&(h=!0),l=!0)}return!1},f=t[n];return f&&(d(f[o+ns(r,e,!s)])?a=!0:!s||!(e.altKey||e.metaKey||e.ctrlKey)||Ee.windows&&e.ctrlKey&&e.altKey||Ee.mac&&e.altKey&&!e.ctrlKey&&!e.metaKey||!(c=It[e.keyCode])||c==r?s&&e.shiftKey&&d(f[o+ns(r,e,!0)])&&(a=!0):(d(f[o+ns(c,e,!0)])||e.shiftKey&&(O=Gt[e.keyCode])!=r&&O!=c&&d(f[o+ns(O,e,!1)]))&&(a=!0),!a&&d(f._any)&&(a=!0)),l&&(a=!0),a&&h&&e.stopPropagation(),hs=null,a}(function(t){let e=t.facet(ss),i=os.get(e);return i||os.set(e,i=function(t,e=is){let i=Object.create(null),n=Object.create(null),r=(t,e)=>{let i=n[t];if(null==i)n[t]=e;else if(i!=e)throw new Error("Key binding "+t+" is used both as a regular binding and as a multi-stroke prefix")},s=(t,n,s,o,a)=>{var l,h;let c=i[t]||(i[t]=Object.create(null)),O=n.split(/ (?!$)/).map(t=>function(t,e){const i=t.split(/-(?!$)/);let n,r,s,o,a=i[i.length-1];"Space"==a&&(a=" ");for(let t=0;t<i.length-1;++t){const a=i[t];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))n=!0;else if(/^(c|ctrl|control)$/i.test(a))r=!0;else if(/^s(hift)?$/i.test(a))s=!0;else{if(!/^mod$/i.test(a))throw new Error("Unrecognized modifier name: "+a);"mac"==e?o=!0:r=!0}}return n&&(a="Alt-"+a),r&&(a="Ctrl-"+a),o&&(a="Meta-"+a),s&&(a="Shift-"+a),a}(t,e));for(let e=1;e<O.length;e++){let i=O.slice(0,e).join(" ");r(i,!0),c[i]||(c[i]={preventDefault:!0,stopPropagation:!1,run:[e=>{let n=as={view:e,prefix:i,scope:t};return setTimeout(()=>{as==n&&(as=null)},ls),!0}]})}let u=O.join(" ");r(u,!1);let d=c[u]||(c[u]={preventDefault:!1,stopPropagation:!1,run:(null===(h=null===(l=c._any)||void 0===l?void 0:l.run)||void 0===h?void 0:h.slice())||[]});s&&d.run.push(s),o&&(d.preventDefault=!0),a&&(d.stopPropagation=!0)};for(let n of t){let t=n.scope?n.scope.split(" "):["editor"];if(n.any)for(let e of t){let t=i[e]||(i[e]=Object.create(null));t._any||(t._any={preventDefault:!1,stopPropagation:!1,run:[]});let{any:r}=n;for(let e in t)t[e].run.push(t=>r(t,hs))}let r=n[e]||n.key;if(r)for(let e of t)s(e,r,n.run,n.preventDefault,n.stopPropagation),n.shift&&s(e,"Shift-"+r,n.shift,n.preventDefault,n.stopPropagation)}return i}(e.reduce((t,e)=>t.concat(e),[]))),i}(e.state),t,e,"editor")})),ss=E.define({enables:rs}),os=new WeakMap;let as=null;const ls=4e3;let hs=null;const cs=ti.line({class:"cm-activeLine"}),Os=Hi.fromClass(class{constructor(t){this.decorations=this.getDeco(t)}update(t){(t.docChanged||t.selectionSet)&&(this.decorations=this.getDeco(t.view))}getDeco(t){let e=-1,i=[];for(let n of t.state.selection.ranges){let r=t.lineBlockAt(n.head);r.from>e&&(i.push(cs.range(r.from)),e=r.from)}return ti.set(i)}},{decorations:t=>t.decorations});class us extends bt{compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}eq(t){return!1}destroy(t){}}us.prototype.elementClass="",us.prototype.toDOM=void 0,us.prototype.mapMode=v.TrackBefore,us.prototype.startSide=us.prototype.endSide=-1,us.prototype.point=!0;const ds=E.define(),fs=E.define(),ps=E.define(),gs=E.define({combine:t=>t.some(t=>t)});function ms(t){let e=[Qs];return t&&!1===t.fixed&&e.push(gs.of(!0)),e}const Qs=Hi.fromClass(class{constructor(t){this.view=t,this.domAfter=null,this.prevViewport=t.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters cm-gutters-before",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.gutters=t.state.facet(ps).map(e=>new ws(t,e)),this.fixed=!t.state.facet(gs);for(let t of this.gutters)"after"==t.config.side?this.getDOMAfter().appendChild(t.dom):this.dom.appendChild(t.dom);this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),t.scrollDOM.insertBefore(this.dom,t.contentDOM)}getDOMAfter(){return this.domAfter||(this.domAfter=document.createElement("div"),this.domAfter.className="cm-gutters cm-gutters-after",this.domAfter.setAttribute("aria-hidden","true"),this.domAfter.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.domAfter.style.position=this.fixed?"sticky":"",this.view.scrollDOM.appendChild(this.domAfter)),this.domAfter}update(t){if(this.updateGutters(t)){let e=this.prevViewport,i=t.view.viewport,n=Math.min(e.to,i.to)-Math.max(e.from,i.from);this.syncGutters(n<.8*(i.to-i.from))}if(t.geometryChanged){let t=this.view.contentHeight/this.view.scaleY+"px";this.dom.style.minHeight=t,this.domAfter&&(this.domAfter.style.minHeight=t)}this.view.state.facet(gs)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":"",this.domAfter&&(this.domAfter.style.position=this.fixed?"sticky":"")),this.prevViewport=t.view.viewport}syncGutters(t){let e=this.dom.nextSibling;t&&(this.dom.remove(),this.domAfter&&this.domAfter.remove());let i=Pt.iter(this.view.state.facet(ds),this.view.viewport.from),n=[],r=this.gutters.map(t=>new ys(t,this.view.viewport,-this.view.documentPadding.top));for(let t of this.view.viewportLineBlocks)if(n.length&&(n=[]),Array.isArray(t.type)){let e=!0;for(let s of t.type)if(s.type==Je.Text&&e){xs(i,n,s.from);for(let t of r)t.line(this.view,s,n);e=!1}else if(s.widget)for(let t of r)t.widget(this.view,s)}else if(t.type==Je.Text){xs(i,n,t.from);for(let e of r)e.line(this.view,t,n)}else if(t.widget)for(let e of r)e.widget(this.view,t);for(let t of r)t.finish();t&&(this.view.scrollDOM.insertBefore(this.dom,e),this.domAfter&&this.view.scrollDOM.appendChild(this.domAfter))}updateGutters(t){let e=t.startState.facet(ps),i=t.state.facet(ps),n=t.docChanged||t.heightChanged||t.viewportChanged||!Pt.eq(t.startState.facet(ds),t.state.facet(ds),t.view.viewport.from,t.view.viewport.to);if(e==i)for(let e of this.gutters)e.update(t)&&(n=!0);else{n=!0;let r=[];for(let n of i){let i=e.indexOf(n);i<0?r.push(new ws(this.view,n)):(this.gutters[i].update(t),r.push(this.gutters[i]))}for(let t of this.gutters)t.dom.remove(),r.indexOf(t)<0&&t.destroy();for(let t of r)"after"==t.config.side?this.getDOMAfter().appendChild(t.dom):this.dom.appendChild(t.dom);this.gutters=r}return n}destroy(){for(let t of this.gutters)t.destroy();this.dom.remove(),this.domAfter&&this.domAfter.remove()}},{provide:t=>Fr.scrollMargins.of(e=>{let i=e.plugin(t);if(!i||0==i.gutters.length||!i.fixed)return null;let n=i.dom.offsetWidth*e.scaleX,r=i.domAfter?i.domAfter.offsetWidth*e.scaleX:0;return e.textDirection==ui.LTR?{left:n,right:r}:{right:n,left:r}})});function Ss(t){return Array.isArray(t)?t:[t]}function xs(t,e,i){for(;t.value&&t.from<=i;)t.from==i&&e.push(t.value),t.next()}class ys{constructor(t,e,i){this.gutter=t,this.height=i,this.i=0,this.cursor=Pt.iter(t.markers,e.from)}addElement(t,e,i){let{gutter:n}=this,r=(e.top-this.height)/t.scaleY,s=e.height/t.scaleY;if(this.i==n.elements.length){let e=new bs(t,s,r,i);n.elements.push(e),n.dom.appendChild(e.dom)}else n.elements[this.i].update(t,s,r,i);this.height=e.bottom,this.i++}line(t,e,i){let n=[];xs(this.cursor,n,e.from),i.length&&(n=n.concat(i));let r=this.gutter.config.lineMarker(t,e,n);r&&n.unshift(r);let s=this.gutter;(0!=n.length||s.config.renderEmptyElements)&&this.addElement(t,e,n)}widget(t,e){let i=this.gutter.config.widgetMarker(t,e.widget,e),n=i?[i]:null;for(let i of t.state.facet(fs)){let r=i(t,e.widget,e);r&&(n||(n=[])).push(r)}n&&this.addElement(t,e,n)}finish(){let t=this.gutter;for(;t.elements.length>this.i;){let e=t.elements.pop();t.dom.removeChild(e.dom),e.destroy()}}}class ws{constructor(t,e){this.view=t,this.config=e,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in e.domEventHandlers)this.dom.addEventListener(i,n=>{let r,s=n.target;if(s!=this.dom&&this.dom.contains(s)){for(;s.parentNode!=this.dom;)s=s.parentNode;let t=s.getBoundingClientRect();r=(t.top+t.bottom)/2}else r=n.clientY;let o=t.lineBlockAtHeight(r-t.documentTop);e.domEventHandlers[i](t,o,n)&&n.preventDefault()});this.markers=Ss(e.markers(t)),e.initialSpacer&&(this.spacer=new bs(t,0,0,[e.initialSpacer(t)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(t){let e=this.markers;if(this.markers=Ss(this.config.markers(t.view)),this.spacer&&this.config.updateSpacer){let e=this.config.updateSpacer(this.spacer.markers[0],t);e!=this.spacer.markers[0]&&this.spacer.update(t.view,0,0,[e])}let i=t.view.viewport;return!Pt.eq(this.markers,e,i.from,i.to)||!!this.config.lineMarkerChange&&this.config.lineMarkerChange(t)}destroy(){for(let t of this.elements)t.destroy()}}class bs{constructor(t,e,i,n){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(t,e,i,n)}update(t,e,i,n){this.height!=e&&(this.height=e,this.dom.style.height=e+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),function(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(!t[i].compare(e[i]))return!1;return!0}(this.markers,n)||this.setMarkers(t,n)}setMarkers(t,e){let i="cm-gutterElement",n=this.dom.firstChild;for(let r=0,s=0;;){let o=s,a=r<e.length?e[r++]:null,l=!1;if(a){let t=a.elementClass;t&&(i+=" "+t);for(let t=s;t<this.markers.length;t++)if(this.markers[t].compare(a)){o=t,l=!0;break}}else o=this.markers.length;for(;s<o;){let t=this.markers[s++];if(t.toDOM){t.destroy(n);let e=n.nextSibling;n.remove(),n=e}}if(!a)break;a.toDOM&&(l?n=n.nextSibling:this.dom.insertBefore(a.toDOM(t),n)),l&&s++}this.dom.className=i,this.markers=e}destroy(){this.setMarkers(null,[])}}const ks=E.define(),vs=E.define(),$s=E.define({combine:t=>wt(t,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(t,e){let i=Object.assign({},t);for(let t in e){let n=i[t],r=e[t];i[t]=n?(t,e,i)=>n(t,e,i)||r(t,e,i):r}return i}})});class Ps extends us{constructor(t){super(),this.number=t}eq(t){return this.number==t.number}toDOM(){return document.createTextNode(this.number)}}function Ts(t,e){return t.state.facet($s).formatNumber(e,t.state)}const Zs=ps.compute([$s],t=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers:t=>t.state.facet(ks),lineMarker:(t,e,i)=>i.some(t=>t.toDOM)?null:new Ps(Ts(t,t.state.doc.lineAt(e.from).number)),widgetMarker:(t,e,i)=>{for(let n of t.state.facet(vs)){let r=n(t,e,i);if(r)return r}return null},lineMarkerChange:t=>t.startState.facet($s)!=t.state.facet($s),initialSpacer:t=>new Ps(Ts(t,Xs(t.state.doc.lines))),updateSpacer(t,e){let i=Ts(e.view,Xs(e.view.state.doc.lines));return i==t.number?t:new Ps(i)},domEventHandlers:t.facet($s).domEventHandlers,side:"before"}));function Xs(t){let e=9;for(;e<t;)e=10*e+9;return e}const As=new class extends us{constructor(){super(...arguments),this.elementClass="cm-activeLineGutter"}},Cs=ds.compute(["selection"],t=>{let e=[],i=-1;for(let n of t.selection.ranges){let r=t.doc.lineAt(n.head).from;r>i&&(i=r,e.push(As.range(r)))}return Pt.of(e)}),Ms=1024;let Rs=0;class _s{constructor(t,e){this.from=t,this.to=e}}class zs{constructor(t={}){this.id=Rs++,this.perNode=!!t.perNode,this.deserialize=t.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(t){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return"function"!=typeof t&&(t=Vs.match(t)),e=>{let i=t(e);return void 0===i?null:[this,i]}}}zs.closedBy=new zs({deserialize:t=>t.split(" ")}),zs.openedBy=new zs({deserialize:t=>t.split(" ")}),zs.group=new zs({deserialize:t=>t.split(" ")}),zs.isolate=new zs({deserialize:t=>{if(t&&"rtl"!=t&&"ltr"!=t&&"auto"!=t)throw new RangeError("Invalid value for isolate: "+t);return t||"auto"}}),zs.contextHash=new zs({perNode:!0}),zs.lookAhead=new zs({perNode:!0}),zs.mounted=new zs({perNode:!0});class Ys{constructor(t,e,i){this.tree=t,this.overlay=e,this.parser=i}static get(t){return t&&t.props&&t.props[zs.mounted.id]}}const Es=Object.create(null);class Vs{constructor(t,e,i,n=0){this.name=t,this.props=e,this.id=i,this.flags=n}static define(t){let e=t.props&&t.props.length?Object.create(null):Es,i=(t.top?1:0)|(t.skipped?2:0)|(t.error?4:0)|(null==t.name?8:0),n=new Vs(t.name||"",e,t.id,i);if(t.props)for(let i of t.props)if(Array.isArray(i)||(i=i(n)),i){if(i[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");e[i[0].id]=i[1]}return n}prop(t){return this.props[t.id]}get isTop(){return(1&this.flags)>0}get isSkipped(){return(2&this.flags)>0}get isError(){return(4&this.flags)>0}get isAnonymous(){return(8&this.flags)>0}is(t){if("string"==typeof t){if(this.name==t)return!0;let e=this.prop(zs.group);return!!e&&e.indexOf(t)>-1}return this.id==t}static match(t){let e=Object.create(null);for(let i in t)for(let n of i.split(" "))e[n]=t[i];return t=>{for(let i=t.prop(zs.group),n=-1;n<(i?i.length:0);n++){let r=e[n<0?t.name:i[n]];if(r)return r}}}}Vs.none=new Vs("",Object.create(null),0,8);class qs{constructor(t){this.types=t;for(let e=0;e<t.length;e++)if(t[e].id!=e)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...t){let e=[];for(let i of this.types){let n=null;for(let e of t){let t=e(i);t&&(n||(n=Object.assign({},i.props)),n[t[0].id]=t[1])}e.push(n?new Vs(i.name,n,i.id,i.flags):i)}return new qs(e)}}const Ls=new WeakMap,Ds=new WeakMap;var Ws;!function(t){t[t.ExcludeBuffers=1]="ExcludeBuffers",t[t.IncludeAnonymous=2]="IncludeAnonymous",t[t.IgnoreMounts=4]="IgnoreMounts",t[t.IgnoreOverlays=8]="IgnoreOverlays"}(Ws||(Ws={}));class js{constructor(t,e,i,n,r){if(this.type=t,this.children=e,this.positions=i,this.length=n,this.props=null,r&&r.length){this.props=Object.create(null);for(let[t,e]of r)this.props["number"==typeof t?t:t.id]=e}}toString(){let t=Ys.get(this);if(t&&!t.overlay)return t.tree.toString();let e="";for(let t of this.children){let i=t.toString();i&&(e&&(e+=","),e+=i)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(e.length?"("+e+")":""):e}cursor(t=0){return new no(this.topNode,t)}cursorAt(t,e=0,i=0){let n=Ls.get(this)||this.topNode,r=new no(n);return r.moveTo(t,e),Ls.set(this,r._tree),r}get topNode(){return new Hs(this,0,0,null)}resolve(t,e=0){let i=Ns(Ls.get(this)||this.topNode,t,e,!1);return Ls.set(this,i),i}resolveInner(t,e=0){let i=Ns(Ds.get(this)||this.topNode,t,e,!0);return Ds.set(this,i),i}resolveStack(t,e=0){return function(t,e,i){let n=t.resolveInner(e,i),r=null;for(let t=n instanceof Hs?n:n.context.parent;t;t=t.parent)if(t.index<0){let s=t.parent;(r||(r=[n])).push(s.resolve(e,i)),t=s}else{let s=Ys.get(t.tree);if(s&&s.overlay&&s.overlay[0].from<=e&&s.overlay[s.overlay.length-1].to>=e){let o=new Hs(s.tree,s.overlay[0].from+t.from,-1,t);(r||(r=[n])).push(Ns(o,e,i,!1))}}return r?eo(r):n}(this,t,e)}iterate(t){let{enter:e,leave:i,from:n=0,to:r=this.length}=t,s=t.mode||0,o=(s&Ws.IncludeAnonymous)>0;for(let t=this.cursor(s|Ws.IncludeAnonymous);;){let s=!1;if(t.from<=r&&t.to>=n&&(!o&&t.type.isAnonymous||!1!==e(t))){if(t.firstChild())continue;s=!0}for(;s&&i&&(o||!t.type.isAnonymous)&&i(t),!t.nextSibling();){if(!t.parent())return;s=!0}}}prop(t){return t.perNode?this.props?this.props[t.id]:void 0:this.type.prop(t)}get propValues(){let t=[];if(this.props)for(let e in this.props)t.push([+e,this.props[e]]);return t}balance(t={}){return this.children.length<=8?this:ao(Vs.none,this.children,this.positions,0,this.children.length,0,this.length,(t,e,i)=>new js(this.type,t,e,i,this.propValues),t.makeTree||((t,e,i)=>new js(Vs.none,t,e,i)))}static build(t){return function(t){var e;let{buffer:i,nodeSet:n,maxBufferLength:r=Ms,reused:s=[],minRepeatType:o=n.types.length}=t,a=Array.isArray(i)?new Bs(i,i.length):i,l=n.types,h=0,c=0;function O(t,e,i,g,m,Q){let{id:S,start:x,end:y,size:w}=a,b=c,k=h;for(;w<0;){if(a.next(),-1==w){let e=s[S];return i.push(e),void g.push(x-t)}if(-3==w)return void(h=S);if(-4==w)return void(c=S);throw new RangeError(`Unrecognized record size: ${w}`)}let v,$,P=l[S],T=x-t;if(y-x<=r&&($=function(t,e){let i=a.fork(),n=0,s=0,l=0,h=i.end-r,c={size:0,start:0,skip:0};t:for(let r=i.pos-t;i.pos>r;){let t=i.size;if(i.id==e&&t>=0){c.size=n,c.start=s,c.skip=l,l+=4,n+=4,i.next();continue}let a=i.pos-t;if(t<0||a<r||i.start<h)break;let O=i.id>=o?4:0,u=i.start;for(i.next();i.pos>a;){if(i.size<0){if(-3!=i.size)break t;O+=4}else i.id>=o&&(O+=4);i.next()}s=u,n+=t,l+=O}return(e<0||n==t)&&(c.size=n,c.start=s,c.skip=l),c.size>4?c:void 0}(a.pos-e,m))){let e=new Uint16Array($.size-$.skip),i=a.pos-$.size,r=e.length;for(;a.pos>i;)r=p($.start,e,r);v=new Is(e,y-$.start,n),T=$.start-t}else{let t=a.pos-w;a.next();let e=[],i=[],n=S>=o?S:-1,s=0,l=y;for(;a.pos>t;)n>=0&&a.id==n&&a.size>=0?(a.end<=l-r&&(d(e,i,x,s,a.end,l,n,b,k),s=e.length,l=a.end),a.next()):Q>2500?u(x,t,e,i):O(x,t,e,i,n,Q+1);if(n>=0&&s>0&&s<e.length&&d(e,i,x,s,x,l,n,b,k),e.reverse(),i.reverse(),n>-1&&s>0){let t=function(t,e){return(i,n,r)=>{let s,o,a=0,l=i.length-1;if(l>=0&&(s=i[l])instanceof js){if(!l&&s.type==t&&s.length==r)return s;(o=s.prop(zs.lookAhead))&&(a=n[l]+s.length+o)}return f(t,i,n,r,a,e)}}(P,k);v=ao(P,e,i,0,e.length,0,y-x,t,t)}else v=f(P,e,i,y-x,b-y,k)}i.push(v),g.push(T)}function u(t,e,i,s){let o=[],l=0,h=-1;for(;a.pos>e;){let{id:t,start:e,end:i,size:n}=a;if(n>4)a.next();else{if(h>-1&&e<h)break;h<0&&(h=i-r),o.push(t,e,i),l++,a.next()}}if(l){let e=new Uint16Array(4*l),r=o[o.length-2];for(let t=o.length-3,i=0;t>=0;t-=3)e[i++]=o[t],e[i++]=o[t+1]-r,e[i++]=o[t+2]-r,e[i++]=i;i.push(new Is(e,o[2]-r,n)),s.push(r-t)}}function d(t,e,i,r,s,o,a,l,h){let c=[],O=[];for(;t.length>r;)c.push(t.pop()),O.push(e.pop()+i-s);t.push(f(n.types[a],c,O,o-s,l-o,h)),e.push(s-i)}function f(t,e,i,n,r,s,o){if(s){let t=[zs.contextHash,s];o=o?[t].concat(o):[t]}if(r>25){let t=[zs.lookAhead,r];o=o?[t].concat(o):[t]}return new js(t,e,i,n,o)}function p(t,e,i){let{id:n,start:r,end:s,size:l}=a;if(a.next(),l>=0&&n<o){let o=i;if(l>4){let n=a.pos-(l-4);for(;a.pos>n;)i=p(t,e,i)}e[--i]=o,e[--i]=s-t,e[--i]=r-t,e[--i]=n}else-3==l?h=n:-4==l&&(c=n);return i}let g=[],m=[];for(;a.pos>0;)O(t.start||0,t.bufferStart||0,g,m,-1,0);let Q=null!==(e=t.length)&&void 0!==e?e:g.length?m[0]+g[0].length:0;return new js(l[t.topID],g.reverse(),m.reverse(),Q)}(t)}}js.empty=new js(Vs.none,[],[],0);class Bs{constructor(t,e){this.buffer=t,this.index=e}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new Bs(this.buffer,this.index)}}class Is{constructor(t,e,i){this.buffer=t,this.length=e,this.set=i}get type(){return Vs.none}toString(){let t=[];for(let e=0;e<this.buffer.length;)t.push(this.childString(e)),e=this.buffer[e+3];return t.join(",")}childString(t){let e=this.buffer[t],i=this.buffer[t+3],n=this.set.types[e],r=n.name;if(/\W/.test(r)&&!n.isError&&(r=JSON.stringify(r)),i==(t+=4))return r;let s=[];for(;t<i;)s.push(this.childString(t)),t=this.buffer[t+3];return r+"("+s.join(",")+")"}findChild(t,e,i,n,r){let{buffer:s}=this,o=-1;for(let a=t;a!=e&&!(Gs(r,n,s[a+1],s[a+2])&&(o=a,i>0));a=s[a+3]);return o}slice(t,e,i){let n=this.buffer,r=new Uint16Array(e-t),s=0;for(let o=t,a=0;o<e;){r[a++]=n[o++],r[a++]=n[o++]-i;let e=r[a++]=n[o++]-i;r[a++]=n[o++]-t,s=Math.max(s,e)}return new Is(r,s,this.set)}}function Gs(t,e,i,n){switch(t){case-2:return i<e;case-1:return n>=e&&i<e;case 0:return i<e&&n>e;case 1:return i<=e&&n>e;case 2:return n>e;case 4:return!0}}function Ns(t,e,i,n){for(var r;t.from==t.to||(i<1?t.from>=e:t.from>e)||(i>-1?t.to<=e:t.to<e);){let e=!n&&t instanceof Hs&&t.index<0?null:t.parent;if(!e)return t;t=e}let s=n?0:Ws.IgnoreOverlays;if(n)for(let n=t,o=n.parent;o;n=o,o=n.parent)n instanceof Hs&&n.index<0&&(null===(r=o.enter(e,i,s))||void 0===r?void 0:r.from)!=n.from&&(t=o);for(;;){let n=t.enter(e,i,s);if(!n)return t;t=n}}class Us{cursor(t=0){return new no(this,t)}getChild(t,e=null,i=null){let n=Fs(this,t,e,i);return n.length?n[0]:null}getChildren(t,e=null,i=null){return Fs(this,t,e,i)}resolve(t,e=0){return Ns(this,t,e,!1)}resolveInner(t,e=0){return Ns(this,t,e,!0)}matchContext(t){return Ks(this.parent,t)}enterUnfinishedNodesBefore(t){let e=this.childBefore(t),i=this;for(;e;){let t=e.lastChild;if(!t||t.to!=e.to)break;t.type.isError&&t.from==t.to?(i=e,e=t.prevSibling):e=t}return i}get node(){return this}get next(){return this.parent}}class Hs extends Us{constructor(t,e,i,n){super(),this._tree=t,this.from=e,this.index=i,this._parent=n}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(t,e,i,n,r=0){for(let s=this;;){for(let{children:o,positions:a}=s._tree,l=e>0?o.length:-1;t!=l;t+=e){let l=o[t],h=a[t]+s.from;if(Gs(n,i,h,h+l.length))if(l instanceof Is){if(r&Ws.ExcludeBuffers)continue;let o=l.findChild(0,l.buffer.length,e,i-h,n);if(o>-1)return new to(new Js(s,l,t,h),null,o)}else if(r&Ws.IncludeAnonymous||!l.type.isAnonymous||ro(l)){let o;if(!(r&Ws.IgnoreMounts)&&(o=Ys.get(l))&&!o.overlay)return new Hs(o.tree,h,t,s);let a=new Hs(l,h,t,s);return r&Ws.IncludeAnonymous||!a.type.isAnonymous?a:a.nextChild(e<0?l.children.length-1:0,e,i,n)}}if(r&Ws.IncludeAnonymous||!s.type.isAnonymous)return null;if(t=s.index>=0?s.index+e:e<0?-1:s._parent._tree.children.length,s=s._parent,!s)return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(t){return this.nextChild(0,1,t,2)}childBefore(t){return this.nextChild(this._tree.children.length-1,-1,t,-2)}enter(t,e,i=0){let n;if(!(i&Ws.IgnoreOverlays)&&(n=Ys.get(this._tree))&&n.overlay){let i=t-this.from;for(let{from:t,to:r}of n.overlay)if((e>0?t<=i:t<i)&&(e<0?r>=i:r>i))return new Hs(n.tree,n.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,t,e,i)}nextSignificantParent(){let t=this;for(;t.type.isAnonymous&&t._parent;)t=t._parent;return t}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function Fs(t,e,i,n){let r=t.cursor(),s=[];if(!r.firstChild())return s;if(null!=i)for(let t=!1;!t;)if(t=r.type.is(i),!r.nextSibling())return s;for(;;){if(null!=n&&r.type.is(n))return s;if(r.type.is(e)&&s.push(r.node),!r.nextSibling())return null==n?s:[]}}function Ks(t,e,i=e.length-1){for(let n=t;i>=0;n=n.parent){if(!n)return!1;if(!n.type.isAnonymous){if(e[i]&&e[i]!=n.name)return!1;i--}}return!0}class Js{constructor(t,e,i,n){this.parent=t,this.buffer=e,this.index=i,this.start=n}}class to extends Us{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(t,e,i){super(),this.context=t,this._parent=e,this.index=i,this.type=t.buffer.set.types[t.buffer.buffer[i]]}child(t,e,i){let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.context.start,i);return r<0?null:new to(this.context,this,r)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(t){return this.child(1,t,2)}childBefore(t){return this.child(-1,t,-2)}enter(t,e,i=0){if(i&Ws.ExcludeBuffers)return null;let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],e>0?1:-1,t-this.context.start,e);return r<0?null:new to(this.context,this,r)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(t){return this._parent?null:this.context.parent.nextChild(this.context.index+t,t,0,4)}get nextSibling(){let{buffer:t}=this.context,e=t.buffer[this.index+3];return e<(this._parent?t.buffer[this._parent.index+3]:t.buffer.length)?new to(this.context,this._parent,e):this.externalSibling(1)}get prevSibling(){let{buffer:t}=this.context,e=this._parent?this._parent.index+4:0;return this.index==e?this.externalSibling(-1):new to(this.context,this._parent,t.findChild(e,this.index,-1,0,4))}get tree(){return null}toTree(){let t=[],e=[],{buffer:i}=this.context,n=this.index+4,r=i.buffer[this.index+3];if(r>n){let s=i.buffer[this.index+1];t.push(i.slice(n,r,s)),e.push(0)}return new js(this.type,t,e,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function eo(t){if(!t.length)return null;let e=0,i=t[0];for(let n=1;n<t.length;n++){let r=t[n];(r.from>i.from||r.to<i.to)&&(i=r,e=n)}let n=i instanceof Hs&&i.index<0?null:i.parent,r=t.slice();return n?r[e]=n:r.splice(e,1),new io(r,i)}class io{constructor(t,e){this.heads=t,this.node=e}get next(){return eo(this.heads)}}class no{get name(){return this.type.name}constructor(t,e=0){if(this.mode=e,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,t instanceof Hs)this.yieldNode(t);else{this._tree=t.context.parent,this.buffer=t.context;for(let e=t._parent;e;e=e._parent)this.stack.unshift(e.index);this.bufferNode=t,this.yieldBuf(t.index)}}yieldNode(t){return!!t&&(this._tree=t,this.type=t.type,this.from=t.from,this.to=t.to,!0)}yieldBuf(t,e){this.index=t;let{start:i,buffer:n}=this.buffer;return this.type=e||n.set.types[n.buffer[t]],this.from=i+n.buffer[t+1],this.to=i+n.buffer[t+2],!0}yield(t){return!!t&&(t instanceof Hs?(this.buffer=null,this.yieldNode(t)):(this.buffer=t.context,this.yieldBuf(t.index,t.type)))}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(t,e,i){if(!this.buffer)return this.yield(this._tree.nextChild(t<0?this._tree._tree.children.length-1:0,t,e,i,this.mode));let{buffer:n}=this.buffer,r=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.buffer.start,i);return!(r<0)&&(this.stack.push(this.index),this.yieldBuf(r))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(t){return this.enterChild(1,t,2)}childBefore(t){return this.enterChild(-1,t,-2)}enter(t,e,i=this.mode){return this.buffer?!(i&Ws.ExcludeBuffers)&&this.enterChild(1,t,e):this.yield(this._tree.enter(t,e,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&Ws.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let t=this.mode&Ws.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(t)}sibling(t){if(!this.buffer)return!!this._tree._parent&&this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+t,t,0,4,this.mode));let{buffer:e}=this.buffer,i=this.stack.length-1;if(t<0){let t=i<0?0:this.stack[i]+4;if(this.index!=t)return this.yieldBuf(e.findChild(t,this.index,-1,0,4))}else{let t=e.buffer[this.index+3];if(t<(i<0?e.buffer.length:e.buffer[this.stack[i]+3]))return this.yieldBuf(t)}return i<0&&this.yield(this.buffer.parent.nextChild(this.buffer.index+t,t,0,4,this.mode))}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(t){let e,i,{buffer:n}=this;if(n){if(t>0){if(this.index<n.buffer.buffer.length)return!1}else for(let t=0;t<this.index;t++)if(n.buffer.buffer[t+3]<this.index)return!1;({index:e,parent:i}=n)}else({index:e,_parent:i}=this._tree);for(;i;({index:e,_parent:i}=i))if(e>-1)for(let n=e+t,r=t<0?-1:i._tree.children.length;n!=r;n+=t){let t=i._tree.children[n];if(this.mode&Ws.IncludeAnonymous||t instanceof Is||!t.type.isAnonymous||ro(t))return!1}return!0}move(t,e){if(e&&this.enterChild(t,0,4))return!0;for(;;){if(this.sibling(t))return!0;if(this.atLastNode(t)||!this.parent())return!1}}next(t=!0){return this.move(1,t)}prev(t=!0){return this.move(-1,t)}moveTo(t,e=0){for(;(this.from==this.to||(e<1?this.from>=t:this.from>t)||(e>-1?this.to<=t:this.to<t))&&this.parent(););for(;this.enterChild(1,t,e););return this}get node(){if(!this.buffer)return this._tree;let t=this.bufferNode,e=null,i=0;if(t&&t.context==this.buffer)t:for(let n=this.index,r=this.stack.length;r>=0;){for(let s=t;s;s=s._parent)if(s.index==n){if(n==this.index)return s;e=s,i=r+1;break t}n=this.stack[--r]}for(let t=i;t<this.stack.length;t++)e=new to(this.buffer,e,this.stack[t]);return this.bufferNode=new to(this.buffer,e,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(t,e){for(let i=0;;){let n=!1;if(this.type.isAnonymous||!1!==t(this)){if(this.firstChild()){i++;continue}this.type.isAnonymous||(n=!0)}for(;;){if(n&&e&&e(this),n=this.type.isAnonymous,!i)return;if(this.nextSibling())break;this.parent(),i--,n=!0}}}matchContext(t){if(!this.buffer)return Ks(this.node.parent,t);let{buffer:e}=this.buffer,{types:i}=e.set;for(let n=t.length-1,r=this.stack.length-1;n>=0;r--){if(r<0)return Ks(this._tree,t,n);let s=i[e.buffer[this.stack[r]]];if(!s.isAnonymous){if(t[n]&&t[n]!=s.name)return!1;n--}}return!0}}function ro(t){return t.children.some(t=>t instanceof Is||!t.type.isAnonymous||ro(t))}const so=new WeakMap;function oo(t,e){if(!t.isAnonymous||e instanceof Is||e.type!=t)return 1;let i=so.get(e);if(null==i){i=1;for(let n of e.children){if(n.type!=t||!(n instanceof js)){i=1;break}i+=oo(t,n)}so.set(e,i)}return i}function ao(t,e,i,n,r,s,o,a,l){let h=0;for(let i=n;i<r;i++)h+=oo(t,e[i]);let c=Math.ceil(1.5*h/8),O=[],u=[];return function e(i,n,r,o,a){for(let h=r;h<o;){let r=h,d=n[h],f=oo(t,i[h]);for(h++;h<o;h++){let e=oo(t,i[h]);if(f+e>=c)break;f+=e}if(h==r+1){if(f>c){let t=i[r];e(t.children,t.positions,0,t.children.length,n[r]+a);continue}O.push(i[r])}else{let e=n[h-1]+i[h-1].length-d;O.push(ao(t,i,n,r,h,d,e,null,l))}u.push(d+a-s)}}(e,i,n,r,0),(a||l)(O,u,o)}class lo{constructor(){this.map=new WeakMap}setBuffer(t,e,i){let n=this.map.get(t);n||this.map.set(t,n=new Map),n.set(e,i)}getBuffer(t,e){let i=this.map.get(t);return i&&i.get(e)}set(t,e){t instanceof to?this.setBuffer(t.context.buffer,t.index,e):t instanceof Hs&&this.map.set(t.tree,e)}get(t){return t instanceof to?this.getBuffer(t.context.buffer,t.index):t instanceof Hs?this.map.get(t.tree):void 0}cursorSet(t,e){t.buffer?this.setBuffer(t.buffer.buffer,t.index,e):this.map.set(t.tree,e)}cursorGet(t){return t.buffer?this.getBuffer(t.buffer.buffer,t.index):this.map.get(t.tree)}}class ho{constructor(t,e,i,n,r=!1,s=!1){this.from=t,this.to=e,this.tree=i,this.offset=n,this.open=(r?1:0)|(s?2:0)}get openStart(){return(1&this.open)>0}get openEnd(){return(2&this.open)>0}static addTree(t,e=[],i=!1){let n=[new ho(0,t.length,t,0,!1,i)];for(let i of e)i.to>t.length&&n.push(i);return n}static applyChanges(t,e,i=128){if(!e.length)return t;let n=[],r=1,s=t.length?t[0]:null;for(let o=0,a=0,l=0;;o++){let h=o<e.length?e[o]:null,c=h?h.fromA:1e9;if(c-a>=i)for(;s&&s.from<c;){let e=s;if(a>=e.from||c<=e.to||l){let t=Math.max(e.from,a)-l,i=Math.min(e.to,c)-l;e=t>=i?null:new ho(t,i,e.tree,e.offset+l,o>0,!!h)}if(e&&n.push(e),s.to>c)break;s=r<t.length?t[r++]:null}if(!h)break;a=h.toA,l=h.toA-h.toB}return n}}class co{startParse(t,e,i){return"string"==typeof t&&(t=new Oo(t)),i=i?i.length?i.map(t=>new _s(t.from,t.to)):[new _s(0,0)]:[new _s(0,t.length)],this.createParse(t,e||[],i)}parse(t,e,i){let n=this.startParse(t,e,i);for(;;){let t=n.advance();if(t)return t}}}class Oo{constructor(t){this.string=t}get length(){return this.string.length}chunk(t){return this.string.slice(t)}get lineChunks(){return!1}read(t,e){return this.string.slice(t,e)}}function uo(t){return(e,i,n,r)=>new Qo(e,t,i,n,r)}class fo{constructor(t,e,i,n,r){this.parser=t,this.parse=e,this.overlay=i,this.target=n,this.from=r}}function po(t){if(!t.length||t.some(t=>t.from>=t.to))throw new RangeError("Invalid inner parse ranges given: "+JSON.stringify(t))}class go{constructor(t,e,i,n,r,s,o){this.parser=t,this.predicate=e,this.mounts=i,this.index=n,this.start=r,this.target=s,this.prev=o,this.depth=0,this.ranges=[]}}const mo=new zs({perNode:!0});class Qo{constructor(t,e,i,n,r){this.nest=e,this.input=i,this.fragments=n,this.ranges=r,this.inner=[],this.innerDone=0,this.baseTree=null,this.stoppedAt=null,this.baseParse=t}advance(){if(this.baseParse){let t=this.baseParse.advance();if(!t)return null;if(this.baseParse=null,this.baseTree=t,this.startInner(),null!=this.stoppedAt)for(let t of this.inner)t.parse.stopAt(this.stoppedAt)}if(this.innerDone==this.inner.length){let t=this.baseTree;return null!=this.stoppedAt&&(t=new js(t.type,t.children,t.positions,t.length,t.propValues.concat([[mo,this.stoppedAt]]))),t}let t=this.inner[this.innerDone],e=t.parse.advance();if(e){this.innerDone++;let i=Object.assign(Object.create(null),t.target.props);i[zs.mounted.id]=new Ys(e,t.overlay,t.parser),t.target.props=i}return null}get parsedPos(){if(this.baseParse)return 0;let t=this.input.length;for(let e=this.innerDone;e<this.inner.length;e++)this.inner[e].from<t&&(t=Math.min(t,this.inner[e].parse.parsedPos));return t}stopAt(t){if(this.stoppedAt=t,this.baseParse)this.baseParse.stopAt(t);else for(let e=this.innerDone;e<this.inner.length;e++)this.inner[e].parse.stopAt(t)}startInner(){let t=new bo(this.fragments),e=null,i=null,n=new no(new Hs(this.baseTree,this.ranges[0].from,0,null),Ws.IncludeAnonymous|Ws.IgnoreMounts);t:for(let r,s;;){let o,a=!0;if(null!=this.stoppedAt&&n.from>=this.stoppedAt)a=!1;else if(t.hasNode(n)){if(e){let t=e.mounts.find(t=>t.frag.from<=n.from&&t.frag.to>=n.to&&t.mount.overlay);if(t)for(let i of t.mount.overlay){let r=i.from+t.pos,s=i.to+t.pos;r>=n.from&&s<=n.to&&!e.ranges.some(t=>t.from<s&&t.to>r)&&e.ranges.push({from:r,to:s})}}a=!1}else if(i&&(s=So(i.ranges,n.from,n.to)))a=2!=s;else if(!n.type.isAnonymous&&(r=this.nest(n,this.input))&&(n.from<n.to||!r.overlay)){n.tree||yo(n);let s=t.findMounts(n.from,r.parser);if("function"==typeof r.overlay)e=new go(r.parser,r.overlay,s,this.inner.length,n.from,n.tree,e);else{let t=ko(this.ranges,r.overlay||(n.from<n.to?[new _s(n.from,n.to)]:[]));t.length&&po(t),!t.length&&r.overlay||this.inner.push(new fo(r.parser,t.length?r.parser.startParse(this.input,$o(s,t),t):r.parser.startParse(""),r.overlay?r.overlay.map(t=>new _s(t.from-n.from,t.to-n.from)):null,n.tree,t.length?t[0].from:n.from)),r.overlay?t.length&&(i={ranges:t,depth:0,prev:i}):a=!1}}else if(e&&(o=e.predicate(n))&&(!0===o&&(o=new _s(n.from,n.to)),o.from<o.to)){let t=e.ranges.length-1;t>=0&&e.ranges[t].to==o.from?e.ranges[t]={from:e.ranges[t].from,to:o.to}:e.ranges.push(o)}if(a&&n.firstChild())e&&e.depth++,i&&i.depth++;else for(;!n.nextSibling();){if(!n.parent())break t;if(e&&! --e.depth){let t=ko(this.ranges,e.ranges);t.length&&(po(t),this.inner.splice(e.index,0,new fo(e.parser,e.parser.startParse(this.input,$o(e.mounts,t),t),e.ranges.map(t=>new _s(t.from-e.start,t.to-e.start)),e.target,t[0].from))),e=e.prev}i&&! --i.depth&&(i=i.prev)}}}}function So(t,e,i){for(let n of t){if(n.from>=i)break;if(n.to>e)return n.from<=e&&n.to>=i?2:1}return 0}function xo(t,e,i,n,r,s){if(e<i){let o=t.buffer[e+1];n.push(t.slice(e,i,o)),r.push(o-s)}}function yo(t){let{node:e}=t,i=[],n=e.context.buffer;do{i.push(t.index),t.parent()}while(!t.tree);let r=t.tree,s=r.children.indexOf(n),o=r.children[s],a=o.buffer,l=[s];r.children[s]=function t(n,r,s,h,c,O){let u=i[O],d=[],f=[];xo(o,n,u,d,f,h);let p=a[u+1],g=a[u+2];l.push(d.length);let m=O?t(u+4,a[u+3],o.set.types[a[u]],p,g-p,O-1):e.toTree();return d.push(m),f.push(p-h),xo(o,a[u+3],r,d,f,h),new js(s,d,f,c)}(0,a.length,Vs.none,0,o.length,i.length-1);for(let e of l){let i=t.tree.children[e],n=t.tree.positions[e];t.yield(new Hs(i,n+t.from,e,t._tree))}}class wo{constructor(t,e){this.offset=e,this.done=!1,this.cursor=t.cursor(Ws.IncludeAnonymous|Ws.IgnoreMounts)}moveTo(t){let{cursor:e}=this,i=t-this.offset;for(;!this.done&&e.from<i;)e.to>=t&&e.enter(i,1,Ws.IgnoreOverlays|Ws.ExcludeBuffers)||e.next(!1)||(this.done=!0)}hasNode(t){if(this.moveTo(t.from),!this.done&&this.cursor.from+this.offset==t.from&&this.cursor.tree)for(let e=this.cursor.tree;;){if(e==t.tree)return!0;if(!(e.children.length&&0==e.positions[0]&&e.children[0]instanceof js))break;e=e.children[0]}return!1}}class bo{constructor(t){var e;if(this.fragments=t,this.curTo=0,this.fragI=0,t.length){let i=this.curFrag=t[0];this.curTo=null!==(e=i.tree.prop(mo))&&void 0!==e?e:i.to,this.inner=new wo(i.tree,-i.offset)}else this.curFrag=this.inner=null}hasNode(t){for(;this.curFrag&&t.from>=this.curTo;)this.nextFrag();return this.curFrag&&this.curFrag.from<=t.from&&this.curTo>=t.to&&this.inner.hasNode(t)}nextFrag(){var t;if(this.fragI++,this.fragI==this.fragments.length)this.curFrag=this.inner=null;else{let e=this.curFrag=this.fragments[this.fragI];this.curTo=null!==(t=e.tree.prop(mo))&&void 0!==t?t:e.to,this.inner=new wo(e.tree,-e.offset)}}findMounts(t,e){var i;let n=[];if(this.inner){this.inner.cursor.moveTo(t,1);for(let t=this.inner.cursor.node;t;t=t.parent){let r=null===(i=t.tree)||void 0===i?void 0:i.prop(zs.mounted);if(r&&r.parser==e)for(let e=this.fragI;e<this.fragments.length;e++){let i=this.fragments[e];if(i.from>=t.to)break;i.tree==this.curFrag.tree&&n.push({frag:i,pos:t.from-i.offset,mount:r})}}}return n}}function ko(t,e){let i=null,n=e;for(let r=1,s=0;r<t.length;r++){let o=t[r-1].to,a=t[r].from;for(;s<n.length;s++){let t=n[s];if(t.from>=a)break;t.to<=o||(i||(n=i=e.slice()),t.from<o?(i[s]=new _s(t.from,o),t.to>a&&i.splice(s+1,0,new _s(a,t.to))):t.to>a?i[s--]=new _s(a,t.to):i.splice(s--,1))}}return n}function vo(t,e,i,n){let r=0,s=0,o=!1,a=!1,l=-1e9,h=[];for(;;){let c=r==t.length?1e9:o?t[r].to:t[r].from,O=s==e.length?1e9:a?e[s].to:e[s].from;if(o!=a){let t=Math.max(l,i),e=Math.min(c,O,n);t<e&&h.push(new _s(t,e))}if(l=Math.min(c,O),1e9==l)break;c==l&&(o?(o=!1,r++):o=!0),O==l&&(a?(a=!1,s++):a=!0)}return h}function $o(t,e){let i=[];for(let{pos:n,mount:r,frag:s}of t){let t=n+(r.overlay?r.overlay[0].from:0),o=t+r.tree.length,a=Math.max(s.from,t),l=Math.min(s.to,o);if(r.overlay){let o=vo(e,r.overlay.map(t=>new _s(t.from+n,t.to+n)),a,l);for(let e=0,n=a;;e++){let a=e==o.length,h=a?l:o[e].from;if(h>n&&i.push(new ho(n,h,r.tree,-t,s.from>=n||s.openStart,s.to<=h||s.openEnd)),a)break;n=o[e].to}}else i.push(new ho(a,l,r.tree,-t,s.from>=t||s.openStart,s.to<=o||s.openEnd))}return i}let Po=0;class To{constructor(t,e,i,n){this.name=t,this.set=e,this.base=i,this.modified=n,this.id=Po++}toString(){let{name:t}=this;for(let e of this.modified)e.name&&(t=`${e.name}(${t})`);return t}static define(t,e){let i="string"==typeof t?t:"?";if(t instanceof To&&(e=t),null==e?void 0:e.base)throw new Error("Can not derive from a modified tag");let n=new To(i,[],null,[]);if(n.set.push(n),e)for(let t of e.set)n.set.push(t);return n}static defineModifier(t){let e=new Xo(t);return t=>t.modified.indexOf(e)>-1?t:Xo.get(t.base||t,t.modified.concat(e).sort((t,e)=>t.id-e.id))}}let Zo=0;class Xo{constructor(t){this.name=t,this.instances=[],this.id=Zo++}static get(t,e){if(!e.length)return t;let i=e[0].instances.find(i=>{return i.base==t&&(n=e,r=i.modified,n.length==r.length&&n.every((t,e)=>t==r[e]));var n,r});if(i)return i;let n=[],r=new To(t.name,n,t,e);for(let t of e)t.instances.push(r);let s=function(t){let e=[[]];for(let i=0;i<t.length;i++)for(let n=0,r=e.length;n<r;n++)e.push(e[n].concat(t[i]));return e.sort((t,e)=>e.length-t.length)}(e);for(let e of t.set)if(!e.modified.length)for(let t of s)n.push(Xo.get(e,t));return r}}function Ao(t){let e=Object.create(null);for(let i in t){let n=t[i];Array.isArray(n)||(n=[n]);for(let t of i.split(" "))if(t){let i=[],r=2,s=t;for(let e=0;;){if("..."==s&&e>0&&e+3==t.length){r=1;break}let n=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(s);if(!n)throw new RangeError("Invalid path: "+t);if(i.push("*"==n[0]?"":'"'==n[0][0]?JSON.parse(n[0]):n[0]),e+=n[0].length,e==t.length)break;let o=t[e++];if(e==t.length&&"!"==o){r=0;break}if("/"!=o)throw new RangeError("Invalid path: "+t);s=t.slice(e)}let o=i.length-1,a=i[o];if(!a)throw new RangeError("Invalid path: "+t);let l=new Mo(n,r,o>0?i.slice(0,o):null);e[a]=l.sort(e[a])}}return Co.add(e)}const Co=new zs;class Mo{constructor(t,e,i,n){this.tags=t,this.mode=e,this.context=i,this.next=n}get opaque(){return 0==this.mode}get inherit(){return 1==this.mode}sort(t){return!t||t.depth<this.depth?(this.next=t,this):(t.next=this.sort(t.next),t)}get depth(){return this.context?this.context.length:0}}function Ro(t,e){let i=Object.create(null);for(let e of t)if(Array.isArray(e.tag))for(let t of e.tag)i[t.id]=e.class;else i[e.tag.id]=e.class;let{scope:n,all:r=null}=e||{};return{style:t=>{let e=r;for(let n of t)for(let t of n.set){let n=i[t.id];if(n){e=e?e+" "+n:n;break}}return e},scope:n}}function _o(t,e,i,n=0,r=t.length){let s=new zo(n,Array.isArray(e)?e:[e],i);s.highlightRange(t.cursor(),n,r,"",s.highlighters),s.flush(r)}Mo.empty=new Mo([],2,null);class zo{constructor(t,e,i){this.at=t,this.highlighters=e,this.span=i,this.class=""}startSpan(t,e){e!=this.class&&(this.flush(t),t>this.at&&(this.at=t),this.class=e)}flush(t){t>this.at&&this.class&&this.span(this.at,t,this.class)}highlightRange(t,e,i,n,r){let{type:s,from:o,to:a}=t;if(o>=i||a<=e)return;s.isTop&&(r=this.highlighters.filter(t=>!t.scope||t.scope(s)));let l=n,h=function(t){let e=t.type.prop(Co);for(;e&&e.context&&!t.matchContext(e.context);)e=e.next;return e||null}(t)||Mo.empty,c=function(t,e){let i=null;for(let n of t){let t=n.style(e);t&&(i=i?i+" "+t:t)}return i}(r,h.tags);if(c&&(l&&(l+=" "),l+=c,1==h.mode&&(n+=(n?" ":"")+c)),this.startSpan(Math.max(e,o),l),h.opaque)return;let O=t.tree&&t.tree.prop(zs.mounted);if(O&&O.overlay){let s=t.node.enter(O.overlay[0].from+o,1),h=this.highlighters.filter(t=>!t.scope||t.scope(O.tree.type)),c=t.firstChild();for(let u=0,d=o;;u++){let f=u<O.overlay.length?O.overlay[u]:null,p=f?f.from+o:a,g=Math.max(e,d),m=Math.min(i,p);if(g<m&&c)for(;t.from<m&&(this.highlightRange(t,g,m,n,r),this.startSpan(Math.min(m,t.to),l),!(t.to>=p)&&t.nextSibling()););if(!f||p>i)break;d=f.to+o,d>e&&(this.highlightRange(s.cursor(),Math.max(e,f.from+o),Math.min(i,d),"",h),this.startSpan(Math.min(i,d),l))}c&&t.parent()}else if(t.firstChild()){O&&(n="");do{if(!(t.to<=e)){if(t.from>=i)break;this.highlightRange(t,e,i,n,r),this.startSpan(Math.min(i,t.to),l)}}while(t.nextSibling());t.parent()}}}const Yo=To.define,Eo=Yo(),Vo=Yo(),qo=Yo(Vo),Lo=Yo(Vo),Do=Yo(),Wo=Yo(Do),jo=Yo(Do),Bo=Yo(),Io=Yo(Bo),Go=Yo(),No=Yo(),Uo=Yo(),Ho=Yo(Uo),Fo=Yo(),Ko={comment:Eo,lineComment:Yo(Eo),blockComment:Yo(Eo),docComment:Yo(Eo),name:Vo,variableName:Yo(Vo),typeName:qo,tagName:Yo(qo),propertyName:Lo,attributeName:Yo(Lo),className:Yo(Vo),labelName:Yo(Vo),namespace:Yo(Vo),macroName:Yo(Vo),literal:Do,string:Wo,docString:Yo(Wo),character:Yo(Wo),attributeValue:Yo(Wo),number:jo,integer:Yo(jo),float:Yo(jo),bool:Yo(Do),regexp:Yo(Do),escape:Yo(Do),color:Yo(Do),url:Yo(Do),keyword:Go,self:Yo(Go),null:Yo(Go),atom:Yo(Go),unit:Yo(Go),modifier:Yo(Go),operatorKeyword:Yo(Go),controlKeyword:Yo(Go),definitionKeyword:Yo(Go),moduleKeyword:Yo(Go),operator:No,derefOperator:Yo(No),arithmeticOperator:Yo(No),logicOperator:Yo(No),bitwiseOperator:Yo(No),compareOperator:Yo(No),updateOperator:Yo(No),definitionOperator:Yo(No),typeOperator:Yo(No),controlOperator:Yo(No),punctuation:Uo,separator:Yo(Uo),bracket:Ho,angleBracket:Yo(Ho),squareBracket:Yo(Ho),paren:Yo(Ho),brace:Yo(Ho),content:Bo,heading:Io,heading1:Yo(Io),heading2:Yo(Io),heading3:Yo(Io),heading4:Yo(Io),heading5:Yo(Io),heading6:Yo(Io),contentSeparator:Yo(Bo),list:Yo(Bo),quote:Yo(Bo),emphasis:Yo(Bo),strong:Yo(Bo),link:Yo(Bo),monospace:Yo(Bo),strikethrough:Yo(Bo),inserted:Yo(),deleted:Yo(),changed:Yo(),invalid:Yo(),meta:Fo,documentMeta:Yo(Fo),annotation:Yo(Fo),processingInstruction:Yo(Fo),definition:To.defineModifier("definition"),constant:To.defineModifier("constant"),function:To.defineModifier("function"),standard:To.defineModifier("standard"),local:To.defineModifier("local"),special:To.defineModifier("special")};for(let t in Ko){let e=Ko[t];e instanceof To&&(e.name=t)}var Jo;Ro([{tag:Ko.link,class:"tok-link"},{tag:Ko.heading,class:"tok-heading"},{tag:Ko.emphasis,class:"tok-emphasis"},{tag:Ko.strong,class:"tok-strong"},{tag:Ko.keyword,class:"tok-keyword"},{tag:Ko.atom,class:"tok-atom"},{tag:Ko.bool,class:"tok-bool"},{tag:Ko.url,class:"tok-url"},{tag:Ko.labelName,class:"tok-labelName"},{tag:Ko.inserted,class:"tok-inserted"},{tag:Ko.deleted,class:"tok-deleted"},{tag:Ko.literal,class:"tok-literal"},{tag:Ko.string,class:"tok-string"},{tag:Ko.number,class:"tok-number"},{tag:[Ko.regexp,Ko.escape,Ko.special(Ko.string)],class:"tok-string2"},{tag:Ko.variableName,class:"tok-variableName"},{tag:Ko.local(Ko.variableName),class:"tok-variableName tok-local"},{tag:Ko.definition(Ko.variableName),class:"tok-variableName tok-definition"},{tag:Ko.special(Ko.variableName),class:"tok-variableName2"},{tag:Ko.definition(Ko.propertyName),class:"tok-propertyName tok-definition"},{tag:Ko.typeName,class:"tok-typeName"},{tag:Ko.namespace,class:"tok-namespace"},{tag:Ko.className,class:"tok-className"},{tag:Ko.macroName,class:"tok-macroName"},{tag:Ko.propertyName,class:"tok-propertyName"},{tag:Ko.operator,class:"tok-operator"},{tag:Ko.comment,class:"tok-comment"},{tag:Ko.meta,class:"tok-meta"},{tag:Ko.invalid,class:"tok-invalid"},{tag:Ko.punctuation,class:"tok-punctuation"}]);const ta=new zs;function ea(t){return E.define({combine:t?e=>e.concat(t):void 0})}const ia=new zs;class na{constructor(t,e,i=[],n=""){this.data=t,this.name=n,yt.prototype.hasOwnProperty("tree")||Object.defineProperty(yt.prototype,"tree",{get(){return oa(this)}}),this.parser=e,this.extension=[pa.of(this),yt.languageData.of((t,e,i)=>{let n=ra(t,e,i),r=n.type.prop(ta);if(!r)return[];let s=t.facet(r),o=n.type.prop(ia);if(o){let r=n.resolve(e-n.from,i);for(let e of o)if(e.test(r,t)){let i=t.facet(e.facet);return"replace"==e.type?i:i.concat(s)}}return s})].concat(i)}isActiveAt(t,e,i=-1){return ra(t,e,i).type.prop(ta)==this.data}findRegions(t){let e=t.facet(pa);if((null==e?void 0:e.data)==this.data)return[{from:0,to:t.doc.length}];if(!e||!e.allowsNesting)return[];let i=[],n=(t,e)=>{if(t.prop(ta)==this.data)return void i.push({from:e,to:e+t.length});let r=t.prop(zs.mounted);if(r){if(r.tree.prop(ta)==this.data){if(r.overlay)for(let t of r.overlay)i.push({from:t.from+e,to:t.to+e});else i.push({from:e,to:e+t.length});return}if(r.overlay){let t=i.length;if(n(r.tree,r.overlay[0].from+e),i.length>t)return}}for(let i=0;i<t.children.length;i++){let r=t.children[i];r instanceof js&&n(r,t.positions[i]+e)}};return n(oa(t),0),i}get allowsNesting(){return!0}}function ra(t,e,i){let n=t.facet(pa),r=oa(t).topNode;if(!n||n.allowsNesting)for(let t=r;t;t=t.enter(e,i,Ws.ExcludeBuffers))t.type.isTop&&(r=t);return r}na.setState=ct.define();class sa extends na{constructor(t,e,i){super(t,e,[],i),this.parser=e}static define(t){let e=ea(t.languageData);return new sa(e,t.parser.configure({props:[ta.add(t=>t.isTop?e:void 0)]}),t.name)}configure(t,e){return new sa(this.data,this.parser.configure(t),e||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function oa(t){let e=t.field(na.state,!1);return e?e.tree:js.empty}class aa{constructor(t){this.doc=t,this.cursorPos=0,this.string="",this.cursor=t.iter()}get length(){return this.doc.length}syncTo(t){return this.string=this.cursor.next(t-this.cursorPos).value,this.cursorPos=t+this.string.length,this.cursorPos-this.string.length}chunk(t){return this.syncTo(t),this.string}get lineChunks(){return!0}read(t,e){let i=this.cursorPos-this.string.length;return t<i||e>=this.cursorPos?this.doc.sliceString(t,e):this.string.slice(t-i,e-i)}}let la=null;class ha{constructor(t,e,i=[],n,r,s,o,a){this.parser=t,this.state=e,this.fragments=i,this.tree=n,this.treeLen=r,this.viewport=s,this.skipped=o,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(t,e,i){return new ha(t,e,[],js.empty,0,i,[],null)}startParse(){return this.parser.startParse(new aa(this.state.doc),this.fragments)}work(t,e){return null!=e&&e>=this.state.doc.length&&(e=void 0),this.tree!=js.empty&&this.isDone(null!=e?e:this.state.doc.length)?(this.takeTree(),!0):this.withContext(()=>{var i;if("number"==typeof t){let e=Date.now()+t;t=()=>Date.now()>e}for(this.parse||(this.parse=this.startParse()),null!=e&&(null==this.parse.stoppedAt||this.parse.stoppedAt>e)&&e<this.state.doc.length&&this.parse.stopAt(e);;){let n=this.parse.advance();if(n){if(this.fragments=this.withoutTempSkipped(ho.addTree(n,this.fragments,null!=this.parse.stoppedAt)),this.treeLen=null!==(i=this.parse.stoppedAt)&&void 0!==i?i:this.state.doc.length,this.tree=n,this.parse=null,!(this.treeLen<(null!=e?e:this.state.doc.length)))return!0;this.parse=this.startParse()}if(t())return!1}})}takeTree(){let t,e;this.parse&&(t=this.parse.parsedPos)>=this.treeLen&&((null==this.parse.stoppedAt||this.parse.stoppedAt>t)&&this.parse.stopAt(t),this.withContext(()=>{for(;!(e=this.parse.advance()););}),this.treeLen=t,this.tree=e,this.fragments=this.withoutTempSkipped(ho.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(t){let e=la;la=this;try{return t()}finally{la=e}}withoutTempSkipped(t){for(let e;e=this.tempSkipped.pop();)t=ca(t,e.from,e.to);return t}changes(t,e){let{fragments:i,tree:n,treeLen:r,viewport:s,skipped:o}=this;if(this.takeTree(),!t.empty){let e=[];if(t.iterChangedRanges((t,i,n,r)=>e.push({fromA:t,toA:i,fromB:n,toB:r})),i=ho.applyChanges(i,e),n=js.empty,r=0,s={from:t.mapPos(s.from,-1),to:t.mapPos(s.to,1)},this.skipped.length){o=[];for(let e of this.skipped){let i=t.mapPos(e.from,1),n=t.mapPos(e.to,-1);i<n&&o.push({from:i,to:n})}}}return new ha(this.parser,e,i,n,r,s,o,this.scheduleOn)}updateViewport(t){if(this.viewport.from==t.from&&this.viewport.to==t.to)return!1;this.viewport=t;let e=this.skipped.length;for(let e=0;e<this.skipped.length;e++){let{from:i,to:n}=this.skipped[e];i<t.to&&n>t.from&&(this.fragments=ca(this.fragments,i,n),this.skipped.splice(e--,1))}return!(this.skipped.length>=e||(this.reset(),0))}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(t,e){this.skipped.push({from:t,to:e})}static getSkippingParser(t){return new class extends co{createParse(e,i,n){let r=n[0].from,s=n[n.length-1].to;return{parsedPos:r,advance(){let e=la;if(e){for(let t of n)e.tempSkipped.push(t);t&&(e.scheduleOn=e.scheduleOn?Promise.all([e.scheduleOn,t]):t)}return this.parsedPos=s,new js(Vs.none,[],[],s-r)},stoppedAt:null,stopAt(){}}}}}isDone(t){t=Math.min(t,this.state.doc.length);let e=this.fragments;return this.treeLen>=t&&e.length&&0==e[0].from&&e[0].to>=t}static get(){return la}}function ca(t,e,i){return ho.applyChanges(t,[{fromA:e,toA:i,fromB:e,toB:i}])}class Oa{constructor(t){this.context=t,this.tree=t.tree}apply(t){if(!t.docChanged&&this.tree==this.context.tree)return this;let e=this.context.changes(t.changes,t.state),i=this.context.treeLen==t.startState.doc.length?void 0:Math.max(t.changes.mapPos(this.context.treeLen),e.viewport.to);return e.work(20,i)||e.takeTree(),new Oa(e)}static init(t){let e=Math.min(3e3,t.doc.length),i=ha.create(t.facet(pa).parser,t,{from:0,to:e});return i.work(20,e)||i.takeTree(),new Oa(i)}}na.state=B.define({create:Oa.init,update(t,e){for(let t of e.effects)if(t.is(na.setState))return t.value;return e.startState.facet(pa)!=e.state.facet(pa)?Oa.init(e.state):t.apply(e)}});let ua=t=>{let e=setTimeout(()=>t(),500);return()=>clearTimeout(e)};"undefined"!=typeof requestIdleCallback&&(ua=t=>{let e=-1,i=setTimeout(()=>{e=requestIdleCallback(t,{timeout:400})},100);return()=>e<0?clearTimeout(i):cancelIdleCallback(e)});const da="undefined"!=typeof navigator&&(null===(Jo=navigator.scheduling)||void 0===Jo?void 0:Jo.isInputPending)?()=>navigator.scheduling.isInputPending():null,fa=Hi.fromClass(class{constructor(t){this.view=t,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(t){let e=this.view.state.field(na.state).context;(e.updateViewport(t.view.viewport)||this.view.viewport.to>e.treeLen)&&this.scheduleWork(),(t.docChanged||t.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(e)}scheduleWork(){if(this.working)return;let{state:t}=this.view,e=t.field(na.state);e.tree==e.context.tree&&e.context.isDone(t.doc.length)||(this.working=ua(this.work))}work(t){this.working=null;let e=Date.now();if(this.chunkEnd<e&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=e+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:n}}=this.view,r=i.field(na.state);if(r.tree==r.context.tree&&r.context.isDone(n+1e5))return;let s=Date.now()+Math.min(this.chunkBudget,100,t&&!da?Math.max(25,t.timeRemaining()-5):1e9),o=r.context.treeLen<n&&i.doc.length>n+1e3,a=r.context.work(()=>da&&da()||Date.now()>s,n+(o?0:1e5));this.chunkBudget-=Date.now()-e,(a||this.chunkBudget<=0)&&(r.context.takeTree(),this.view.dispatch({effects:na.setState.of(new Oa(r.context))})),this.chunkBudget>0&&(!a||o)&&this.scheduleWork(),this.checkAsyncSchedule(r.context)}checkAsyncSchedule(t){t.scheduleOn&&(this.workScheduled++,t.scheduleOn.then(()=>this.scheduleWork()).catch(t=>Ii(this.view.state,t)).then(()=>this.workScheduled--),t.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),pa=E.define({combine:t=>t.length?t[0]:null,enables:t=>[na.state,fa,Fr.contentAttributes.compute([t],e=>{let i=e.facet(t);return i&&i.name?{"data-language":i.name}:{}})]});class ga{constructor(t,e=[]){this.language=t,this.support=e,this.extension=[t,e]}}class ma{constructor(t,e,i,n,r,s=void 0){this.name=t,this.alias=e,this.extensions=i,this.filename=n,this.loadFunc=r,this.support=s,this.loading=null}load(){return this.loading||(this.loading=this.loadFunc().then(t=>this.support=t,t=>{throw this.loading=null,t}))}static of(t){let{load:e,support:i}=t;if(!e){if(!i)throw new RangeError("Must pass either 'load' or 'support' to LanguageDescription.of");e=()=>Promise.resolve(i)}return new ma(t.name,(t.alias||[]).concat(t.name).map(t=>t.toLowerCase()),t.extensions||[],t.filename,e,i)}static matchFilename(t,e){for(let i of t)if(i.filename&&i.filename.test(e))return i;let i=/\.([^.]+)$/.exec(e);if(i)for(let e of t)if(e.extensions.indexOf(i[1])>-1)return e;return null}static matchLanguageName(t,e,i=!0){e=e.toLowerCase();for(let i of t)if(i.alias.some(t=>t==e))return i;if(i)for(let i of t)for(let t of i.alias){let n=e.indexOf(t);if(n>-1&&(t.length>2||!/\w/.test(e[n-1])&&!/\w/.test(e[n+t.length])))return i}return null}}const Qa=E.define(),Sa=E.define({combine:t=>{if(!t.length)return"  ";let e=t[0];if(!e||/\S/.test(e)||Array.from(e).some(t=>t!=e[0]))throw new Error("Invalid indent unit: "+JSON.stringify(t[0]));return e}});function xa(t){let e=t.facet(Sa);return 9==e.charCodeAt(0)?t.tabSize*e.length:e.length}function ya(t,e){let i="",n=t.tabSize,r=t.facet(Sa)[0];if("\t"==r){for(;e>=n;)i+="\t",e-=n;r=" "}for(let t=0;t<e;t++)i+=r;return i}function wa(t,e){t instanceof yt&&(t=new ba(t));for(let i of t.state.facet(Qa)){let n=i(t,e);if(void 0!==n)return n}let i=oa(t.state);return i.length>=e?function(t,e,i){let n=e.resolveStack(i),r=e.resolveInner(i,-1).resolve(i,0).enterUnfinishedNodesBefore(i);if(r!=n.node){let t=[];for(let e=r;e&&!(e.from<n.node.from||e.to>n.node.to||e.from==n.node.from&&e.type==n.node.type);e=e.parent)t.push(e);for(let e=t.length-1;e>=0;e--)n={node:t[e],next:n}}return va(n,t,i)}(t,i,e):null}class ba{constructor(t,e={}){this.state=t,this.options=e,this.unit=xa(t)}lineAt(t,e=1){let i=this.state.doc.lineAt(t),{simulateBreak:n,simulateDoubleBreak:r}=this.options;return null!=n&&n>=i.from&&n<=i.to?r&&n==t?{text:"",from:t}:(e<0?n<t:n<=t)?{text:i.text.slice(n-i.from),from:n}:{text:i.text.slice(0,n-i.from),from:i.from}:i}textAfterPos(t,e=1){if(this.options.simulateDoubleBreak&&t==this.options.simulateBreak)return"";let{text:i,from:n}=this.lineAt(t,e);return i.slice(t-n,Math.min(i.length,t+100-n))}column(t,e=1){let{text:i,from:n}=this.lineAt(t,e),r=this.countColumn(i,t-n),s=this.options.overrideIndentation?this.options.overrideIndentation(n):-1;return s>-1&&(r+=s-this.countColumn(i,i.search(/\S|$/))),r}countColumn(t,e=t.length){return Vt(t,this.state.tabSize,e)}lineIndent(t,e=1){let{text:i,from:n}=this.lineAt(t,e),r=this.options.overrideIndentation;if(r){let t=r(n);if(t>-1)return t}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const ka=new zs;function va(t,e,i){for(let n=t;n;n=n.next){let t=$a(n.node);if(t)return t(Ta.create(e,i,n))}return 0}function $a(t){let e=t.type.prop(ka);if(e)return e;let i,n=t.firstChild;if(n&&(i=n.type.prop(zs.closedBy))){let e=t.lastChild,n=e&&i.indexOf(e.name)>-1;return t=>Aa(t,!0,1,void 0,n&&!function(t){return t.pos==t.options.simulateBreak&&t.options.simulateDoubleBreak}(t)?e.from:void 0)}return null==t.parent?Pa:null}function Pa(){return 0}class Ta extends ba{constructor(t,e,i){super(t.state,t.options),this.base=t,this.pos=e,this.context=i}get node(){return this.context.node}static create(t,e,i){return new Ta(t,e,i)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){return this.baseIndentFor(this.node)}baseIndentFor(t){let e=this.state.doc.lineAt(t.from);for(;;){let i=t.resolve(e.from);for(;i.parent&&i.parent.from==i.from;)i=i.parent;if(Za(i,t))break;e=this.state.doc.lineAt(i.from)}return this.lineIndent(e.from)}continue(){return va(this.context.next,this.base,this.pos)}}function Za(t,e){for(let i=e;i;i=i.parent)if(t==i)return!0;return!1}function Xa({closing:t,align:e=!0,units:i=1}){return n=>Aa(n,e,i,t)}function Aa(t,e,i,n,r){let s=t.textAfter,o=s.match(/^\s*/)[0].length,a=n&&s.slice(o,o+n.length)==n||r==t.pos+o,l=e?function(t){let e=t.node,i=e.childAfter(e.from),n=e.lastChild;if(!i)return null;let r=t.options.simulateBreak,s=t.state.doc.lineAt(i.from),o=null==r||r<=s.from?s.to:Math.min(s.to,r);for(let t=i.to;;){let r=e.childAfter(t);if(!r||r==n)return null;if(!r.type.isSkipped){if(r.from>=o)return null;let t=/^ */.exec(s.text.slice(i.to-s.from))[0].length;return{from:i.from,to:i.to+t}}t=r.to}}(t):null;return l?a?t.column(l.from):t.column(l.to):t.baseIndent+(a?0:t.unit*i)}function Ca({except:t,units:e=1}={}){return i=>{let n=t&&t.test(i.textAfter);return i.baseIndent+(n?0:e*i.unit)}}const Ma=E.define(),Ra=new zs;function _a(t){let e=t.firstChild,i=t.lastChild;return e&&e.to<i.from?{from:e.to,to:i.type.isError?t.to:i.from}:null}class za{constructor(t,e){let i;function n(t){let e=Wt.newName();return(i||(i=Object.create(null)))["."+e]=t,e}this.specs=t;const r="string"==typeof e.all?e.all:e.all?n(e.all):void 0,s=e.scope;this.scope=s instanceof na?t=>t.prop(ta)==s.data:s?t=>t==s:void 0,this.style=Ro(t.map(t=>({tag:t.tag,class:t.class||n(Object.assign({},t,{tag:null}))})),{all:r}).style,this.module=i?new Wt(i):null,this.themeType=e.themeType}static define(t,e){return new za(t,e||{})}}const Ya=E.define(),Ea=E.define({combine:t=>t.length?[t[0]]:null});function Va(t){let e=t.facet(Ya);return e.length?e:t.facet(Ea)}class qa{constructor(t){this.markCache=Object.create(null),this.tree=oa(t.state),this.decorations=this.buildDeco(t,Va(t.state)),this.decoratedTo=t.viewport.to}update(t){let e=oa(t.state),i=Va(t.state),n=i!=Va(t.startState),{viewport:r}=t.view,s=t.changes.mapPos(this.decoratedTo,1);e.length<r.to&&!n&&e.type==this.tree.type&&s>=r.to?(this.decorations=this.decorations.map(t.changes),this.decoratedTo=s):(e!=this.tree||t.viewportChanged||n)&&(this.tree=e,this.decorations=this.buildDeco(t.view,i),this.decoratedTo=r.to)}buildDeco(t,e){if(!e||!this.tree.length)return ti.none;let i=new Tt;for(let{from:n,to:r}of t.visibleRanges)_o(this.tree,e,(t,e,n)=>{i.add(t,e,this.markCache[n]||(this.markCache[n]=ti.mark({class:n})))},n,r);return i.finish()}}const La=G.high(Hi.fromClass(qa,{decorations:t=>t.decorations})),Da=za.define([{tag:Ko.meta,color:"#404740"},{tag:Ko.link,textDecoration:"underline"},{tag:Ko.heading,textDecoration:"underline",fontWeight:"bold"},{tag:Ko.emphasis,fontStyle:"italic"},{tag:Ko.strong,fontWeight:"bold"},{tag:Ko.strikethrough,textDecoration:"line-through"},{tag:Ko.keyword,color:"#708"},{tag:[Ko.atom,Ko.bool,Ko.url,Ko.contentSeparator,Ko.labelName],color:"#219"},{tag:[Ko.literal,Ko.inserted],color:"#164"},{tag:[Ko.string,Ko.deleted],color:"#a11"},{tag:[Ko.regexp,Ko.escape,Ko.special(Ko.string)],color:"#e40"},{tag:Ko.definition(Ko.variableName),color:"#00f"},{tag:Ko.local(Ko.variableName),color:"#30a"},{tag:[Ko.typeName,Ko.namespace],color:"#085"},{tag:Ko.className,color:"#167"},{tag:[Ko.special(Ko.variableName),Ko.macroName],color:"#256"},{tag:Ko.definition(Ko.propertyName),color:"#00c"},{tag:Ko.comment,color:"#940"},{tag:Ko.invalid,color:"#f00"}]),Wa=Fr.baseTheme({"&.cm-focused .cm-matchingBracket":{backgroundColor:"#328c8252"},"&.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bb555544"}}),ja="()[]{}",Ba=E.define({combine:t=>wt(t,{afterCursor:!0,brackets:ja,maxScanDistance:1e4,renderMatch:Na})}),Ia=ti.mark({class:"cm-matchingBracket"}),Ga=ti.mark({class:"cm-nonmatchingBracket"});function Na(t){let e=[],i=t.matched?Ia:Ga;return e.push(i.range(t.start.from,t.start.to)),t.end&&e.push(i.range(t.end.from,t.end.to)),e}const Ua=B.define({create:()=>ti.none,update(t,e){if(!e.docChanged&&!e.selection)return t;let i=[],n=e.state.facet(Ba);for(let t of e.state.selection.ranges){if(!t.empty)continue;let r=tl(e.state,t.head,-1,n)||t.head>0&&tl(e.state,t.head-1,1,n)||n.afterCursor&&(tl(e.state,t.head,1,n)||t.head<e.state.doc.length&&tl(e.state,t.head+1,-1,n));r&&(i=i.concat(n.renderMatch(r,e.state)))}return ti.set(i,!0)},provide:t=>Fr.decorations.from(t)}),Ha=[Ua,Wa],Fa=new zs;function Ka(t,e,i){let n=t.prop(e<0?zs.openedBy:zs.closedBy);if(n)return n;if(1==t.name.length){let n=i.indexOf(t.name);if(n>-1&&n%2==(e<0?1:0))return[i[n+e]]}return null}function Ja(t){let e=t.type.prop(Fa);return e?e(t.node):t}function tl(t,e,i,n={}){let r=n.maxScanDistance||1e4,s=n.brackets||ja,o=oa(t),a=o.resolveInner(e,i);for(let t=a;t;t=t.parent){let n=Ka(t.type,i,s);if(n&&t.from<t.to){let r=Ja(t);if(r&&(i>0?e>=r.from&&e<r.to:e>r.from&&e<=r.to))return el(0,0,i,t,r,n,s)}}return function(t,e,i,n,r,s,o){let a=i<0?t.sliceDoc(e-1,e):t.sliceDoc(e,e+1),l=o.indexOf(a);if(l<0||l%2==0!=i>0)return null;let h={from:i<0?e-1:e,to:i>0?e+1:e},c=t.doc.iterRange(e,i>0?t.doc.length:0),O=0;for(let t=0;!c.next().done&&t<=s;){let s=c.value;i<0&&(t+=s.length);let a=e+t*i;for(let t=i>0?0:s.length-1,e=i>0?s.length:-1;t!=e;t+=i){let e=o.indexOf(s[t]);if(!(e<0||n.resolveInner(a+t,1).type!=r))if(e%2==0==i>0)O++;else{if(1==O)return{start:h,end:{from:a+t,to:a+t+1},matched:e>>1==l>>1};O--}}i>0&&(t+=s.length)}return c.done?{start:h,matched:!1}:null}(t,e,i,o,a.type,r,s)}function el(t,e,i,n,r,s,o){let a=n.parent,l={from:r.from,to:r.to},h=0,c=null==a?void 0:a.cursor();if(c&&(i<0?c.childBefore(n.from):c.childAfter(n.to)))do{if(i<0?c.to<=n.from:c.from>=n.to){if(0==h&&s.indexOf(c.type.name)>-1&&c.from<c.to){let t=Ja(c);return{start:l,end:t?{from:t.from,to:t.to}:void 0,matched:!0}}if(Ka(c.type,i,o))h++;else if(Ka(c.type,-i,o)){if(0==h){let t=Ja(c);return{start:l,end:t&&t.from<t.to?{from:t.from,to:t.to}:void 0,matched:!1}}h--}}}while(i<0?c.prevSibling():c.nextSibling());return{start:l,matched:!1}}const il=Object.create(null),nl=[Vs.none],rl=[],sl=Object.create(null),ol=Object.create(null);for(let[t,e]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])ol[t]=ll(il,e);function al(t,e){rl.indexOf(t)>-1||(rl.push(t),console.warn(e))}function ll(t,e){let i=[];for(let n of e.split(" ")){let e=[];for(let i of n.split(".")){let n=t[i]||Ko[i];n?"function"==typeof n?e.length?e=e.map(n):al(i,`Modifier ${i} used at start of tag`):e.length?al(i,`Tag ${i} used as modifier`):e=Array.isArray(n)?n:[n]:al(i,`Unknown highlighting tag ${i}`)}for(let t of e)i.push(t)}if(!i.length)return 0;let n=e.replace(/ /g,"_"),r=n+" "+i.map(t=>t.id),s=sl[r];if(s)return s.id;let o=sl[r]=Vs.define({id:nl.length,name:n,props:[Ao({[n]:i})]});return nl.push(o),o.id}function hl(t,e){return({state:i,dispatch:n})=>{if(i.readOnly)return!1;let r=t(e,i);return!!r&&(n(i.update(r)),!0)}}ui.RTL,ui.LTR;const cl=hl(gl,0),Ol=hl(pl,0),ul=hl((t,e)=>pl(t,e,function(t){let e=[];for(let i of t.selection.ranges){let n=t.doc.lineAt(i.from),r=i.to<=n.to?n:t.doc.lineAt(i.to);r.from>n.from&&r.from==i.to&&(r=i.to==n.to+1?n:t.doc.lineAt(i.to-1));let s=e.length-1;s>=0&&e[s].to>n.from?e[s].to=r.to:e.push({from:n.from+/^\s*/.exec(n.text)[0].length,to:r.to})}return e}(e)),0);function dl(t,e){let i=t.languageDataAt("commentTokens",e,1);return i.length?i[0]:{}}const fl=50;function pl(t,e,i=e.selection.ranges){let n=i.map(t=>dl(e,t.from).block);if(!n.every(t=>t))return null;let r=i.map((t,i)=>function(t,{open:e,close:i},n,r){let s,o,a=t.sliceDoc(n-fl,n),l=t.sliceDoc(r,r+fl),h=/\s*$/.exec(a)[0].length,c=/^\s*/.exec(l)[0].length,O=a.length-h;if(a.slice(O-e.length,O)==e&&l.slice(c,c+i.length)==i)return{open:{pos:n-h,margin:h&&1},close:{pos:r+c,margin:c&&1}};r-n<=2*fl?s=o=t.sliceDoc(n,r):(s=t.sliceDoc(n,n+fl),o=t.sliceDoc(r-fl,r));let u=/^\s*/.exec(s)[0].length,d=/\s*$/.exec(o)[0].length,f=o.length-d-i.length;return s.slice(u,u+e.length)==e&&o.slice(f,f+i.length)==i?{open:{pos:n+u+e.length,margin:/\s/.test(s.charAt(u+e.length))?1:0},close:{pos:r-d-i.length,margin:/\s/.test(o.charAt(f-1))?1:0}}:null}(e,n[i],t.from,t.to));if(2!=t&&!r.every(t=>t))return{changes:e.changes(i.map((t,e)=>r[e]?[]:[{from:t.from,insert:n[e].open+" "},{from:t.to,insert:" "+n[e].close}]))};if(1!=t&&r.some(t=>t)){let t=[];for(let e,i=0;i<r.length;i++)if(e=r[i]){let r=n[i],{open:s,close:o}=e;t.push({from:s.pos-r.open.length,to:s.pos+s.margin},{from:o.pos-o.margin,to:o.pos+r.close.length})}return{changes:t}}return null}function gl(t,e,i=e.selection.ranges){let n=[],r=-1;for(let{from:t,to:s}of i){let i=n.length,o=1e9,a=dl(e,t).line;if(a){for(let i=t;i<=s;){let l=e.doc.lineAt(i);if(l.from>r&&(t==s||s>l.from)){r=l.from;let t=/^\s*/.exec(l.text)[0].length,e=t==l.length,i=l.text.slice(t,t+a.length)==a?t:-1;t<l.text.length&&t<o&&(o=t),n.push({line:l,comment:i,token:a,indent:t,empty:e,single:!1})}i=l.to+1}if(o<1e9)for(let t=i;t<n.length;t++)n[t].indent<n[t].line.text.length&&(n[t].indent=o);n.length==i+1&&(n[i].single=!0)}}if(2!=t&&n.some(t=>t.comment<0&&(!t.empty||t.single))){let t=[];for(let{line:e,token:i,indent:r,empty:s,single:o}of n)!o&&s||t.push({from:e.from+r,insert:i+" "});let i=e.changes(t);return{changes:i,selection:e.selection.map(i,1)}}if(1!=t&&n.some(t=>t.comment>=0)){let t=[];for(let{line:e,comment:i,token:r}of n)if(i>=0){let n=e.from+i,s=n+r.length;" "==e.text[s-e.from]&&s++,t.push({from:n,to:s})}return{changes:t}}return null}const ml=at.define(),Ql=at.define(),Sl=E.define(),xl=E.define({combine:t=>wt(t,{minDepth:100,newGroupDelay:500,joinToEvent:(t,e)=>e},{minDepth:Math.max,newGroupDelay:Math.min,joinToEvent:(t,e)=>(i,n)=>t(i,n)||e(i,n)})}),yl=B.define({create:()=>Yl.empty,update(t,e){let i=e.state.facet(xl),n=e.annotation(ml);if(n){let r=Pl.fromTransaction(e,n.selection),s=n.side,o=0==s?t.undone:t.done;return o=r?Tl(o,o.length,i.minDepth,r):Cl(o,e.startState.selection),new Yl(0==s?n.rest:o,0==s?o:n.rest)}let r=e.annotation(Ql);if("full"!=r&&"before"!=r||(t=t.isolate()),!1===e.annotation(Ot.addToHistory))return e.changes.empty?t:t.addMapping(e.changes.desc);let s=Pl.fromTransaction(e),o=e.annotation(Ot.time),a=e.annotation(Ot.userEvent);return s?t=t.addChanges(s,o,a,i,e):e.selection&&(t=t.addSelection(e.startState.selection,o,a,i.newGroupDelay)),"full"!=r&&"after"!=r||(t=t.isolate()),t},toJSON:t=>({done:t.done.map(t=>t.toJSON()),undone:t.undone.map(t=>t.toJSON())}),fromJSON:t=>new Yl(t.done.map(Pl.fromJSON),t.undone.map(Pl.fromJSON))});function wl(t,e){return function({state:i,dispatch:n}){if(!e&&i.readOnly)return!1;let r=i.field(yl,!1);if(!r)return!1;let s=r.pop(t,i,e);return!!s&&(n(s),!0)}}const bl=wl(0,!1),kl=wl(1,!1),vl=wl(0,!0),$l=wl(1,!0);class Pl{constructor(t,e,i,n,r){this.changes=t,this.effects=e,this.mapped=i,this.startSelection=n,this.selectionsAfter=r}setSelAfter(t){return new Pl(this.changes,this.effects,this.mapped,this.startSelection,t)}toJSON(){var t,e,i;return{changes:null===(t=this.changes)||void 0===t?void 0:t.toJSON(),mapped:null===(e=this.mapped)||void 0===e?void 0:e.toJSON(),startSelection:null===(i=this.startSelection)||void 0===i?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map(t=>t.toJSON())}}static fromJSON(t){return new Pl(t.changes&&P.fromJSON(t.changes),[],t.mapped&&$.fromJSON(t.mapped),t.startSelection&&_.fromJSON(t.startSelection),t.selectionsAfter.map(_.fromJSON))}static fromTransaction(t,e){let i=Xl;for(let e of t.startState.facet(Sl)){let n=e(t);n.length&&(i=i.concat(n))}return!i.length&&t.changes.empty?null:new Pl(t.changes.invert(t.startState.doc),i,void 0,e||t.startState.selection,Xl)}static selection(t){return new Pl(void 0,Xl,void 0,void 0,t)}}function Tl(t,e,i,n){let r=e+1>i+20?e-i-1:0,s=t.slice(r,e);return s.push(n),s}function Zl(t,e){return t.length?e.length?t.concat(e):t:e}const Xl=[],Al=200;function Cl(t,e){if(t.length){let i=t[t.length-1],n=i.selectionsAfter.slice(Math.max(0,i.selectionsAfter.length-Al));return n.length&&n[n.length-1].eq(e)?t:(n.push(e),Tl(t,t.length-1,1e9,i.setSelAfter(n)))}return[Pl.selection([e])]}function Ml(t){let e=t[t.length-1],i=t.slice();return i[t.length-1]=e.setSelAfter(e.selectionsAfter.slice(0,e.selectionsAfter.length-1)),i}function Rl(t,e){if(!t.length)return t;let i=t.length,n=Xl;for(;i;){let r=_l(t[i-1],e,n);if(r.changes&&!r.changes.empty||r.effects.length){let e=t.slice(0,i);return e[i-1]=r,e}e=r.mapped,i--,n=r.selectionsAfter}return n.length?[Pl.selection(n)]:Xl}function _l(t,e,i){let n=Zl(t.selectionsAfter.length?t.selectionsAfter.map(t=>t.map(e)):Xl,i);if(!t.changes)return Pl.selection(n);let r=t.changes.map(e),s=e.mapDesc(t.changes,!0),o=t.mapped?t.mapped.composeDesc(s):s;return new Pl(r,ct.mapEffects(t.effects,e),o,t.startSelection.map(s),n)}const zl=/^(input\.type|delete)($|\.)/;class Yl{constructor(t,e,i=0,n=void 0){this.done=t,this.undone=e,this.prevTime=i,this.prevUserEvent=n}isolate(){return this.prevTime?new Yl(this.done,this.undone):this}addChanges(t,e,i,n,r){let s=this.done,o=s[s.length-1];return s=o&&o.changes&&!o.changes.empty&&t.changes&&(!i||zl.test(i))&&(!o.selectionsAfter.length&&e-this.prevTime<n.newGroupDelay&&n.joinToEvent(r,function(t,e){let i=[],n=!1;return t.iterChangedRanges((t,e)=>i.push(t,e)),e.iterChangedRanges((t,e,r,s)=>{for(let t=0;t<i.length;){let e=i[t++],o=i[t++];s>=e&&r<=o&&(n=!0)}}),n}(o.changes,t.changes))||"input.type.compose"==i)?Tl(s,s.length-1,n.minDepth,new Pl(t.changes.compose(o.changes),Zl(ct.mapEffects(t.effects,o.changes),o.effects),o.mapped,o.startSelection,Xl)):Tl(s,s.length,n.minDepth,t),new Yl(s,Xl,e,i)}addSelection(t,e,i,n){let r=this.done.length?this.done[this.done.length-1].selectionsAfter:Xl;return r.length>0&&e-this.prevTime<n&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&(s=r[r.length-1],o=t,s.ranges.length==o.ranges.length&&0===s.ranges.filter((t,e)=>t.empty!=o.ranges[e].empty).length)?this:new Yl(Cl(this.done,t),this.undone,e,i);var s,o}addMapping(t){return new Yl(Rl(this.done,t),Rl(this.undone,t),this.prevTime,this.prevUserEvent)}pop(t,e,i){let n=0==t?this.done:this.undone;if(0==n.length)return null;let r=n[n.length-1],s=r.selectionsAfter[0]||e.selection;if(i&&r.selectionsAfter.length)return e.update({selection:r.selectionsAfter[r.selectionsAfter.length-1],annotations:ml.of({side:t,rest:Ml(n),selection:s}),userEvent:0==t?"select.undo":"select.redo",scrollIntoView:!0});if(r.changes){let i=1==n.length?Xl:n.slice(0,n.length-1);return r.mapped&&(i=Rl(i,r.mapped)),e.update({changes:r.changes,selection:r.startSelection,effects:r.effects,annotations:ml.of({side:t,rest:i,selection:s}),filter:!1,userEvent:0==t?"undo":"redo",scrollIntoView:!0})}return null}}Yl.empty=new Yl(Xl,Xl);const El=[{key:"Mod-z",run:bl,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:kl,preventDefault:!0},{linux:"Ctrl-Shift-z",run:kl,preventDefault:!0},{key:"Mod-u",run:vl,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:$l,preventDefault:!0}];function Vl(t,e){return _.create(t.ranges.map(e),t.mainIndex)}function ql(t,e){return t.update({selection:e,scrollIntoView:!0,userEvent:"select"})}function Ll({state:t,dispatch:e},i){let n=Vl(t.selection,i);return!n.eq(t.selection,!0)&&(e(ql(t,n)),!0)}function Dl(t,e){return _.cursor(e?t.to:t.from)}function Wl(t,e){return Ll(t,i=>i.empty?t.moveByChar(i,e):Dl(i,e))}function jl(t){return t.textDirectionAt(t.state.selection.main.head)==ui.LTR}const Bl=t=>Wl(t,!jl(t)),Il=t=>Wl(t,jl(t));function Gl(t,e){return Ll(t,i=>i.empty?t.moveByGroup(i,e):Dl(i,e))}function Nl(t,e,i){if(e.type.prop(i))return!0;let n=e.to-e.from;return n&&(n>2||/[^\s,.;:]/.test(t.sliceDoc(e.from,e.to)))||e.firstChild}function Ul(t,e,i){let n,r,s=oa(t).resolveInner(e.head),o=i?zs.closedBy:zs.openedBy;for(let n=e.head;;){let e=i?s.childAfter(n):s.childBefore(n);if(!e)break;Nl(t,e,o)?s=e:n=i?e.to:e.from}return r=s.type.prop(o)&&(n=i?tl(t,s.from,1):tl(t,s.to,-1))&&n.matched?i?n.end.to:n.end.from:i?s.to:s.from,_.cursor(r,i?-1:1)}function Hl(t,e){return Ll(t,i=>{if(!i.empty)return Dl(i,e);let n=t.moveVertically(i,e);return n.head!=i.head?n:t.moveToLineBoundary(i,e)})}"undefined"!=typeof Intl&&Intl.Segmenter;const Fl=t=>Hl(t,!1),Kl=t=>Hl(t,!0);function Jl(t){let e,i=t.scrollDOM.clientHeight<t.scrollDOM.scrollHeight-2,n=0,r=0;if(i){for(let e of t.state.facet(Fr.scrollMargins)){let i=e(t);(null==i?void 0:i.top)&&(n=Math.max(null==i?void 0:i.top,n)),(null==i?void 0:i.bottom)&&(r=Math.max(null==i?void 0:i.bottom,r))}e=t.scrollDOM.clientHeight-n-r}else e=(t.dom.ownerDocument.defaultView||window).innerHeight;return{marginTop:n,marginBottom:r,selfScroll:i,height:Math.max(t.defaultLineHeight,e-5)}}function th(t,e){let i,n=Jl(t),{state:r}=t,s=Vl(r.selection,i=>i.empty?t.moveVertically(i,e,n.height):Dl(i,e));if(s.eq(r.selection))return!1;if(n.selfScroll){let e=t.coordsAtPos(r.selection.main.head),o=t.scrollDOM.getBoundingClientRect(),a=o.top+n.marginTop,l=o.bottom-n.marginBottom;e&&e.top>a&&e.bottom<l&&(i=Fr.scrollIntoView(s.main.head,{y:"start",yMargin:e.top-a}))}return t.dispatch(ql(r,s),{effects:i}),!0}const eh=t=>th(t,!1),ih=t=>th(t,!0);function nh(t,e,i){let n=t.lineBlockAt(e.head),r=t.moveToLineBoundary(e,i);if(r.head==e.head&&r.head!=(i?n.to:n.from)&&(r=t.moveToLineBoundary(e,i,!1)),!i&&r.head==n.from&&n.length){let i=/^\s*/.exec(t.state.sliceDoc(n.from,Math.min(n.from+100,n.to)))[0].length;i&&e.head!=n.from+i&&(r=_.cursor(n.from+i))}return r}function rh(t,e){let i=Vl(t.state.selection,t=>{let i=e(t);return _.range(t.anchor,i.head,i.goalColumn,i.bidiLevel||void 0)});return!i.eq(t.state.selection)&&(t.dispatch(ql(t.state,i)),!0)}function sh(t,e){return rh(t,i=>t.moveByChar(i,e))}const oh=t=>sh(t,!jl(t)),ah=t=>sh(t,jl(t));function lh(t,e){return rh(t,i=>t.moveByGroup(i,e))}function hh(t,e){return rh(t,i=>t.moveVertically(i,e))}const ch=t=>hh(t,!1),Oh=t=>hh(t,!0);function uh(t,e){return rh(t,i=>t.moveVertically(i,e,Jl(t).height))}const dh=t=>uh(t,!1),fh=t=>uh(t,!0),ph=({state:t,dispatch:e})=>(e(ql(t,{anchor:0})),!0),gh=({state:t,dispatch:e})=>(e(ql(t,{anchor:t.doc.length})),!0),mh=({state:t,dispatch:e})=>(e(ql(t,{anchor:t.selection.main.anchor,head:0})),!0),Qh=({state:t,dispatch:e})=>(e(ql(t,{anchor:t.selection.main.anchor,head:t.doc.length})),!0);function Sh(t,e){if(t.state.readOnly)return!1;let i="delete.selection",{state:n}=t,r=n.changeByRange(n=>{let{from:r,to:s}=n;if(r==s){let o=e(n);o<r?(i="delete.backward",o=xh(t,o,!1)):o>r&&(i="delete.forward",o=xh(t,o,!0)),r=Math.min(r,o),s=Math.max(s,o)}else r=xh(t,r,!1),s=xh(t,s,!0);return r==s?{range:n}:{changes:{from:r,to:s},range:_.cursor(r,r<n.head?-1:1)}});return!r.changes.empty&&(t.dispatch(n.update(r,{scrollIntoView:!0,userEvent:i,effects:"delete.selection"==i?Fr.announce.of(n.phrase("Selection deleted")):void 0})),!0)}function xh(t,e,i){if(t instanceof Fr)for(let n of t.state.facet(Fr.atomicRanges).map(e=>e(t)))n.between(e,e,(t,n)=>{t<e&&n>e&&(e=i?n:t)});return e}const yh=(t,e,i)=>Sh(t,n=>{let r,s,o=n.from,{state:a}=t,l=a.doc.lineAt(o);if(i&&!e&&o>l.from&&o<l.from+200&&!/[^ \t]/.test(r=l.text.slice(0,o-l.from))){if("\t"==r[r.length-1])return o-1;let t=Vt(r,a.tabSize)%xa(a)||xa(a);for(let e=0;e<t&&" "==r[r.length-1-e];e++)o--;s=o}else s=y(l.text,o-l.from,e,e)+l.from,s==o&&l.number!=(e?a.doc.lines:1)?s+=e?1:-1:!e&&/[\ufe00-\ufe0f]/.test(l.text.slice(s-l.from,o-l.from))&&(s=y(l.text,s-l.from,!1,!1)+l.from);return s}),wh=t=>yh(t,!1,!0),bh=t=>yh(t,!0,!1),kh=(t,e)=>Sh(t,i=>{let n=i.head,{state:r}=t,s=r.doc.lineAt(n),o=r.charCategorizer(n);for(let t=null;;){if(n==(e?s.to:s.from)){n==i.head&&s.number!=(e?r.doc.lines:1)&&(n+=e?1:-1);break}let a=y(s.text,n-s.from,e)+s.from,l=s.text.slice(Math.min(n,a)-s.from,Math.max(n,a)-s.from),h=o(l);if(null!=t&&h!=t)break;" "==l&&n==i.head||(t=h),n=a}return n}),vh=t=>kh(t,!1);function $h(t){let e=[],i=-1;for(let n of t.selection.ranges){let r=t.doc.lineAt(n.from),s=t.doc.lineAt(n.to);if(n.empty||n.to!=s.from||(s=t.doc.lineAt(n.to-1)),i>=r.number){let t=e[e.length-1];t.to=s.to,t.ranges.push(n)}else e.push({from:r.from,to:s.to,ranges:[n]});i=s.number+1}return e}function Ph(t,e,i){if(t.readOnly)return!1;let n=[],r=[];for(let e of $h(t)){if(i?e.to==t.doc.length:0==e.from)continue;let s=t.doc.lineAt(i?e.to+1:e.from-1),o=s.length+1;if(i){n.push({from:e.to,to:s.to},{from:e.from,insert:s.text+t.lineBreak});for(let i of e.ranges)r.push(_.range(Math.min(t.doc.length,i.anchor+o),Math.min(t.doc.length,i.head+o)))}else{n.push({from:s.from,to:e.from},{from:e.to,insert:t.lineBreak+s.text});for(let t of e.ranges)r.push(_.range(t.anchor-o,t.head-o))}}return!!n.length&&(e(t.update({changes:n,scrollIntoView:!0,selection:_.create(r,t.selection.mainIndex),userEvent:"move.line"})),!0)}function Th(t,e,i){if(t.readOnly)return!1;let n=[];for(let e of $h(t))i?n.push({from:e.from,insert:t.doc.slice(e.from,e.to)+t.lineBreak}):n.push({from:e.to,insert:t.lineBreak+t.doc.slice(e.from,e.to)});return e(t.update({changes:n,scrollIntoView:!0,userEvent:"input.copyline"})),!0}const Zh=Xh(!1);function Xh(t){return({state:e,dispatch:i})=>{if(e.readOnly)return!1;let n=e.changeByRange(i=>{let{from:n,to:r}=i,s=e.doc.lineAt(n),o=!t&&n==r&&function(t,e){if(/\(\)|\[\]|\{\}/.test(t.sliceDoc(e-1,e+1)))return{from:e,to:e};let i,n=oa(t).resolveInner(e),r=n.childBefore(e),s=n.childAfter(e);return r&&s&&r.to<=e&&s.from>=e&&(i=r.type.prop(zs.closedBy))&&i.indexOf(s.name)>-1&&t.doc.lineAt(r.to).from==t.doc.lineAt(s.from).from&&!/\S/.test(t.sliceDoc(r.to,s.from))?{from:r.to,to:s.from}:null}(e,n);t&&(n=r=(r<=s.to?s:e.doc.lineAt(r)).to);let a=new ba(e,{simulateBreak:n,simulateDoubleBreak:!!o}),l=wa(a,n);for(null==l&&(l=Vt(/^\s*/.exec(e.doc.lineAt(n).text)[0],e.tabSize));r<s.to&&/\s/.test(s.text[r-s.from]);)r++;o?({from:n,to:r}=o):n>s.from&&n<s.from+100&&!/\S/.test(s.text.slice(0,n))&&(n=s.from);let h=["",ya(e,l)];return o&&h.push(ya(e,a.lineIndent(s.from,-1))),{changes:{from:n,to:r,insert:O.of(h)},range:_.cursor(n+1+h[1].length)}});return i(e.update(n,{scrollIntoView:!0,userEvent:"input"})),!0}}function Ah(t,e){let i=-1;return t.changeByRange(n=>{let r=[];for(let s=n.from;s<=n.to;){let o=t.doc.lineAt(s);o.number>i&&(n.empty||n.to>o.from)&&(e(o,r,n),i=o.number),s=o.to+1}let s=t.changes(r);return{changes:r,range:_.range(s.mapPos(n.anchor,1),s.mapPos(n.head,1))}})}const Ch=[{key:"Ctrl-b",run:Bl,shift:oh,preventDefault:!0},{key:"Ctrl-f",run:Il,shift:ah},{key:"Ctrl-p",run:Fl,shift:ch},{key:"Ctrl-n",run:Kl,shift:Oh},{key:"Ctrl-a",run:t=>Ll(t,e=>_.cursor(t.lineBlockAt(e.head).from,1)),shift:t=>rh(t,e=>_.cursor(t.lineBlockAt(e.head).from))},{key:"Ctrl-e",run:t=>Ll(t,e=>_.cursor(t.lineBlockAt(e.head).to,-1)),shift:t=>rh(t,e=>_.cursor(t.lineBlockAt(e.head).to))},{key:"Ctrl-d",run:bh},{key:"Ctrl-h",run:wh},{key:"Ctrl-k",run:t=>Sh(t,e=>{let i=t.lineBlockAt(e.head).to;return e.head<i?i:Math.min(t.state.doc.length,e.head+1)})},{key:"Ctrl-Alt-h",run:vh},{key:"Ctrl-o",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=t.changeByRange(t=>({changes:{from:t.from,to:t.to,insert:O.of(["",""])},range:_.cursor(t.from)}));return e(t.update(i,{scrollIntoView:!0,userEvent:"input"})),!0}},{key:"Ctrl-t",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=t.changeByRange(e=>{if(!e.empty||0==e.from||e.from==t.doc.length)return{range:e};let i=e.from,n=t.doc.lineAt(i),r=i==n.from?i-1:y(n.text,i-n.from,!1)+n.from,s=i==n.to?i+1:y(n.text,i-n.from,!0)+n.from;return{changes:{from:r,to:s,insert:t.doc.slice(i,s).append(t.doc.slice(r,i))},range:_.cursor(s)}});return!i.changes.empty&&(e(t.update(i,{scrollIntoView:!0,userEvent:"move.character"})),!0)}},{key:"Ctrl-v",run:ih}],Mh=[{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:t=>Ll(t,e=>Ul(t.state,e,!jl(t))),shift:t=>rh(t,e=>Ul(t.state,e,!jl(t)))},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:t=>Ll(t,e=>Ul(t.state,e,jl(t))),shift:t=>rh(t,e=>Ul(t.state,e,jl(t)))},{key:"Alt-ArrowUp",run:({state:t,dispatch:e})=>Ph(t,e,!1)},{key:"Shift-Alt-ArrowUp",run:({state:t,dispatch:e})=>Th(t,e,!1)},{key:"Alt-ArrowDown",run:({state:t,dispatch:e})=>Ph(t,e,!0)},{key:"Shift-Alt-ArrowDown",run:({state:t,dispatch:e})=>Th(t,e,!0)},{key:"Escape",run:({state:t,dispatch:e})=>{let i=t.selection,n=null;return i.ranges.length>1?n=_.create([i.main]):i.main.empty||(n=_.create([_.cursor(i.main.head)])),!!n&&(e(ql(t,n)),!0)}},{key:"Mod-Enter",run:Xh(!0)},{key:"Alt-l",mac:"Ctrl-l",run:({state:t,dispatch:e})=>{let i=$h(t).map(({from:e,to:i})=>_.range(e,Math.min(i+1,t.doc.length)));return e(t.update({selection:_.create(i),userEvent:"select"})),!0}},{key:"Mod-i",run:({state:t,dispatch:e})=>{let i=Vl(t.selection,e=>{let i=oa(t),n=i.resolveStack(e.from,1);if(e.empty){let t=i.resolveStack(e.from,-1);t.node.from>=n.node.from&&t.node.to<=n.node.to&&(n=t)}for(let t=n;t;t=t.next){let{node:i}=t;if((i.from<e.from&&i.to>=e.to||i.to>e.to&&i.from<=e.from)&&t.next)return _.range(i.to,i.from)}return e});return!i.eq(t.selection)&&(e(ql(t,i)),!0)},preventDefault:!0},{key:"Mod-[",run:({state:t,dispatch:e})=>!t.readOnly&&(e(t.update(Ah(t,(e,i)=>{let n=/^\s*/.exec(e.text)[0];if(!n)return;let r=Vt(n,t.tabSize),s=0,o=ya(t,Math.max(0,r-xa(t)));for(;s<n.length&&s<o.length&&n.charCodeAt(s)==o.charCodeAt(s);)s++;i.push({from:e.from+s,to:e.from+n.length,insert:o.slice(s)})}),{userEvent:"delete.dedent"})),!0)},{key:"Mod-]",run:({state:t,dispatch:e})=>!t.readOnly&&(e(t.update(Ah(t,(e,i)=>{i.push({from:e.from,insert:t.facet(Sa)})}),{userEvent:"input.indent"})),!0)},{key:"Mod-Alt-\\",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=Object.create(null),n=new ba(t,{overrideIndentation:t=>{let e=i[t];return null==e?-1:e}}),r=Ah(t,(e,r,s)=>{let o=wa(n,e.from);if(null==o)return;/\S/.test(e.text)||(o=0);let a=/^\s*/.exec(e.text)[0],l=ya(t,o);(a!=l||s.from<e.from+a.length)&&(i[e.from]=o,r.push({from:e.from,to:e.from+a.length,insert:l}))});return r.changes.empty||e(t.update(r,{userEvent:"indent"})),!0}},{key:"Shift-Mod-k",run:t=>{if(t.state.readOnly)return!1;let{state:e}=t,i=e.changes($h(e).map(({from:t,to:i})=>(t>0?t--:i<e.doc.length&&i++,{from:t,to:i}))),n=Vl(e.selection,e=>{let i;if(t.lineWrapping){let n=t.lineBlockAt(e.head),r=t.coordsAtPos(e.head,e.assoc||1);r&&(i=n.bottom+t.documentTop-r.bottom+t.defaultLineHeight/2)}return t.moveVertically(e,!0,i)}).map(i);return t.dispatch({changes:i,selection:n,scrollIntoView:!0,userEvent:"delete.line"}),!0}},{key:"Shift-Mod-\\",run:({state:t,dispatch:e})=>function(t,e,i){let n=!1,r=Vl(t.selection,e=>{let r=tl(t,e.head,-1)||tl(t,e.head,1)||e.head>0&&tl(t,e.head-1,1)||e.head<t.doc.length&&tl(t,e.head+1,-1);if(!r||!r.end)return e;n=!0;let s=r.start.from==e.head?r.end.to:r.end.from;return i?_.range(e.anchor,s):_.cursor(s)});return!!n&&(e(ql(t,r)),!0)}(t,e,!1)},{key:"Mod-/",run:t=>{let{state:e}=t,i=e.doc.lineAt(e.selection.main.from),n=dl(t.state,i.from);return n.line?cl(t):!!n.block&&ul(t)}},{key:"Alt-A",run:Ol},{key:"Ctrl-m",mac:"Shift-Alt-m",run:t=>(t.setTabFocusMode(),!0)}].concat([{key:"ArrowLeft",run:Bl,shift:oh,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:t=>Gl(t,!jl(t)),shift:t=>lh(t,!jl(t)),preventDefault:!0},{mac:"Cmd-ArrowLeft",run:t=>Ll(t,e=>nh(t,e,!jl(t))),shift:t=>rh(t,e=>nh(t,e,!jl(t))),preventDefault:!0},{key:"ArrowRight",run:Il,shift:ah,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:t=>Gl(t,jl(t)),shift:t=>lh(t,jl(t)),preventDefault:!0},{mac:"Cmd-ArrowRight",run:t=>Ll(t,e=>nh(t,e,jl(t))),shift:t=>rh(t,e=>nh(t,e,jl(t))),preventDefault:!0},{key:"ArrowUp",run:Fl,shift:ch,preventDefault:!0},{mac:"Cmd-ArrowUp",run:ph,shift:mh},{mac:"Ctrl-ArrowUp",run:eh,shift:dh},{key:"ArrowDown",run:Kl,shift:Oh,preventDefault:!0},{mac:"Cmd-ArrowDown",run:gh,shift:Qh},{mac:"Ctrl-ArrowDown",run:ih,shift:fh},{key:"PageUp",run:eh,shift:dh},{key:"PageDown",run:ih,shift:fh},{key:"Home",run:t=>Ll(t,e=>nh(t,e,!1)),shift:t=>rh(t,e=>nh(t,e,!1)),preventDefault:!0},{key:"Mod-Home",run:ph,shift:mh},{key:"End",run:t=>Ll(t,e=>nh(t,e,!0)),shift:t=>rh(t,e=>nh(t,e,!0)),preventDefault:!0},{key:"Mod-End",run:gh,shift:Qh},{key:"Enter",run:Zh,shift:Zh},{key:"Mod-a",run:({state:t,dispatch:e})=>(e(t.update({selection:{anchor:0,head:t.doc.length},userEvent:"select"})),!0)},{key:"Backspace",run:wh,shift:wh},{key:"Delete",run:bh},{key:"Mod-Backspace",mac:"Alt-Backspace",run:vh},{key:"Mod-Delete",mac:"Alt-Delete",run:t=>kh(t,!0)},{mac:"Mod-Backspace",run:t=>Sh(t,e=>{let i=t.moveToLineBoundary(e,!1).head;return e.head>i?i:Math.max(0,e.head-1)})},{mac:"Mod-Delete",run:t=>Sh(t,e=>{let i=t.moveToLineBoundary(e,!0).head;return e.head<i?i:Math.min(t.state.doc.length,e.head+1)})}].concat(Ch.map(t=>({mac:t.key,run:t.run,shift:t.shift}))));class Rh{constructor(t,e,i,n){this.state=t,this.pos=e,this.explicit=i,this.view=n,this.abortListeners=[],this.abortOnDocChange=!1}tokenBefore(t){let e=oa(this.state).resolveInner(this.pos,-1);for(;e&&t.indexOf(e.name)<0;)e=e.parent;return e?{from:e.from,to:this.pos,text:this.state.sliceDoc(e.from,this.pos),type:e.type}:null}matchBefore(t){let e=this.state.doc.lineAt(this.pos),i=Math.max(e.from,this.pos-250),n=e.text.slice(i-e.from,this.pos-e.from),r=n.search(function(t,e){var i;let{source:n}=t,r=e&&"^"!=n[0],s="$"!=n[n.length-1];return r||s?new RegExp(`${r?"^":""}(?:${n})${s?"$":""}`,null!==(i=t.flags)&&void 0!==i?i:t.ignoreCase?"i":""):t}(t,!1));return r<0?null:{from:i+r,to:this.pos,text:n.slice(r)}}get aborted(){return null==this.abortListeners}addEventListener(t,e,i){"abort"==t&&this.abortListeners&&(this.abortListeners.push(e),i&&i.onDocChange&&(this.abortOnDocChange=!0))}}function _h(t){let e=Object.keys(t).join(""),i=/\w/.test(e);return i&&(e=e.replace(/\w/g,"")),`[${i?"\\w":""}${e.replace(/[^\w\s]/g,"\\$&")}]`}function zh(t){let e=t.map(t=>"string"==typeof t?{label:t}:t),[i,n]=e.every(t=>/^\w+$/.test(t.label))?[/\w*$/,/\w+$/]:function(t){let e=Object.create(null),i=Object.create(null);for(let{label:n}of t){e[n[0]]=!0;for(let t=1;t<n.length;t++)i[n[t]]=!0}let n=_h(e)+_h(i)+"*$";return[new RegExp("^"+n),new RegExp(n)]}(e);return t=>{let r=t.matchBefore(n);return r||t.explicit?{from:r?r.from:t.pos,options:e,validFor:i}:null}}const Yh=at.define();"object"==typeof navigator&&navigator.platform;const Eh=Fr.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",height:"100%",listStyle:"none",margin:0,padding:0,"& > li, & > completion-section":{padding:"1px 3px",lineHeight:1.2},"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer"},"& > completion-section":{display:"list-item",borderBottom:"1px solid silver",paddingLeft:"0.5em",opacity:.7}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#777"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#444"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box",whiteSpace:"pre-line"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6",boxSizing:"content-box"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});class Vh{constructor(t,e,i,n){this.field=t,this.line=e,this.from=i,this.to=n}}class qh{constructor(t,e,i){this.field=t,this.from=e,this.to=i}map(t){let e=t.mapPos(this.from,-1,v.TrackDel),i=t.mapPos(this.to,1,v.TrackDel);return null==e||null==i?null:new qh(this.field,e,i)}}class Lh{constructor(t,e){this.lines=t,this.fieldPositions=e}instantiate(t,e){let i=[],n=[e],r=t.doc.lineAt(e),s=/^\s*/.exec(r.text)[0];for(let r of this.lines){if(i.length){let i=s,o=/^\t*/.exec(r)[0].length;for(let e=0;e<o;e++)i+=t.facet(Sa);n.push(e+i.length-o),r=i+r.slice(o)}i.push(r),e+=r.length+1}let o=this.fieldPositions.map(t=>new qh(t.field,n[t.line]+t.from,n[t.line]+t.to));return{text:i,ranges:o}}static parse(t){let e,i=[],n=[],r=[];for(let s of t.split(/\r\n?|\n/)){for(;e=/[#$]\{(?:(\d+)(?::([^{}]*))?|((?:\\[{}]|[^{}])*))\}/.exec(s);){let t=e[1]?+e[1]:null,o=e[2]||e[3]||"",a=-1,l=o.replace(/\\[{}]/g,t=>t[1]);for(let e=0;e<i.length;e++)(null!=t?i[e].seq==t:l&&i[e].name==l)&&(a=e);if(a<0){let e=0;for(;e<i.length&&(null==t||null!=i[e].seq&&i[e].seq<t);)e++;i.splice(e,0,{seq:t,name:l}),a=e;for(let t of r)t.field>=a&&t.field++}for(let t of r)if(t.line==n.length&&t.from>e.index){let i=e[2]?3+(e[1]||"").length:2;t.from-=i,t.to-=i}r.push(new Vh(a,n.length,e.index,e.index+l.length)),s=s.slice(0,e.index)+o+s.slice(e.index+e[0].length)}s=s.replace(/\\([{}])/g,(t,e,i)=>{for(let t of r)t.line==n.length&&t.from>i&&(t.from--,t.to--);return e}),n.push(s)}return new Lh(n,r)}}let Dh=ti.widget({widget:new class extends Ke{toDOM(){let t=document.createElement("span");return t.className="cm-snippetFieldPosition",t}ignoreEvent(){return!1}}}),Wh=ti.mark({class:"cm-snippetField"});class jh{constructor(t,e){this.ranges=t,this.active=e,this.deco=ti.set(t.map(t=>(t.from==t.to?Dh:Wh).range(t.from,t.to)),!0)}map(t){let e=[];for(let i of this.ranges){let n=i.map(t);if(!n)return null;e.push(n)}return new jh(e,this.active)}selectionInsideField(t){return t.ranges.every(t=>this.ranges.some(e=>e.field==this.active&&e.from<=t.from&&e.to>=t.to))}}const Bh=ct.define({map:(t,e)=>t&&t.map(e)}),Ih=ct.define(),Gh=B.define({create:()=>null,update(t,e){for(let i of e.effects){if(i.is(Bh))return i.value;if(i.is(Ih)&&t)return new jh(t.ranges,i.value)}return t&&e.docChanged&&(t=t.map(e.changes)),t&&e.selection&&!t.selectionInsideField(e.selection)&&(t=null),t},provide:t=>Fr.decorations.from(t,t=>t?t.deco:ti.none)});function Nh(t,e){return _.create(t.filter(t=>t.field==e).map(t=>_.range(t.from,t.to)))}function Uh(t){let e=Lh.parse(t);return(t,i,n,r)=>{let{text:s,ranges:o}=e.instantiate(t.state,n),{main:a}=t.state.selection,l={changes:{from:n,to:r==a.from?a.to:r,insert:O.of(s)},scrollIntoView:!0,annotations:i?[Yh.of(i),Ot.userEvent.of("input.complete")]:void 0};if(o.length&&(l.selection=Nh(o,0)),o.some(t=>t.field>0)){let e=new jh(o,0),i=l.effects=[Bh.of(e)];void 0===t.state.field(Gh,!1)&&i.push(ct.appendConfig.of([Gh,Jh,ec,Eh]))}t.dispatch(t.state.update(l))}}function Hh(t){return({state:e,dispatch:i})=>{let n=e.field(Gh,!1);if(!n||t<0&&0==n.active)return!1;let r=n.active+t,s=t>0&&!n.ranges.some(e=>e.field==r+t);return i(e.update({selection:Nh(n.ranges,r),effects:Bh.of(s?null:new jh(n.ranges,r)),scrollIntoView:!0})),!0}}const Fh=[{key:"Tab",run:Hh(1),shift:Hh(-1)},{key:"Escape",run:({state:t,dispatch:e})=>!!t.field(Gh,!1)&&(e(t.update({effects:Bh.of(null)})),!0)}],Kh=E.define({combine:t=>t.length?t[0]:Fh}),Jh=G.highest(ss.compute([Kh],t=>t.facet(Kh)));function tc(t,e){return{...e,apply:Uh(t)}}const ec=Fr.domEventHandlers({mousedown(t,e){let i,n=e.state.field(Gh,!1);if(!n||null==(i=e.posAtCoords({x:t.clientX,y:t.clientY})))return!1;let r=n.ranges.find(t=>t.from<=i&&t.to>=i);return!(!r||r.field==n.active||(e.dispatch({selection:Nh(n.ranges,r.field),effects:Bh.of(n.ranges.some(t=>t.field>r.field)?new jh(n.ranges,r.field):null),scrollIntoView:!0}),0))}}),ic={brackets:["(","[","{","'",'"'],before:")]}:;>",stringPrefixes:[]},nc=ct.define({map(t,e){let i=e.mapPos(t,-1,v.TrackAfter);return null==i?void 0:i}}),rc=new class extends bt{};rc.startSide=1,rc.endSide=-1;const sc=B.define({create:()=>Pt.empty,update(t,e){if(t=t.map(e.changes),e.selection){let i=e.state.doc.lineAt(e.selection.main.head);t=t.update({filter:t=>t>=i.from&&t<=i.to})}for(let i of e.effects)i.is(nc)&&(t=t.update({add:[rc.range(i.value,i.value+1)]}));return t}}),oc="()[]{}<>«»»«［］｛｝";function ac(t){for(let e=0;e<16;e+=2)if(oc.charCodeAt(e)==t)return oc.charAt(e+1);return function(t){return t<=65535?String.fromCharCode(t):(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t)))}(t<128?t:t+1)}function lc(t,e){return t.languageDataAt("closeBrackets",e)[0]||ic}const hc="object"==typeof navigator&&/Android\b/.test(navigator.userAgent),cc=Fr.inputHandler.of((t,e,i,n)=>{if((hc?t.composing:t.compositionStarted)||t.state.readOnly)return!1;let r=t.state.selection.main;if(n.length>2||2==n.length&&1==b(w(n,0))||e!=r.from||i!=r.to)return!1;let s=function(t,e){let i=lc(t,t.selection.main.head),n=i.brackets||ic.brackets;for(let r of n){let s=ac(w(r,0));if(e==r)return s==r?gc(t,r,n.indexOf(r+r+r)>-1,i):fc(t,r,s,i.before||ic.before);if(e==s&&uc(t,t.selection.main.from))return pc(t,0,s)}return null}(t.state,n);return!!s&&(t.dispatch(s),!0)}),Oc=[{key:"Backspace",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=lc(t,t.selection.main.head).brackets||ic.brackets,n=null,r=t.changeByRange(e=>{if(e.empty){let n=function(t,e){let i=t.sliceString(e-2,e);return b(w(i,0))==i.length?i:i.slice(1)}(t.doc,e.head);for(let r of i)if(r==n&&dc(t.doc,e.head)==ac(w(r,0)))return{changes:{from:e.head-r.length,to:e.head+r.length},range:_.cursor(e.head-r.length)}}return{range:n=e}});return n||e(t.update(r,{scrollIntoView:!0,userEvent:"delete.backward"})),!n}}];function uc(t,e){let i=!1;return t.field(sc).between(0,t.doc.length,t=>{t==e&&(i=!0)}),i}function dc(t,e){let i=t.sliceString(e,e+2);return i.slice(0,b(w(i,0)))}function fc(t,e,i,n){let r=null,s=t.changeByRange(s=>{if(!s.empty)return{changes:[{insert:e,from:s.from},{insert:i,from:s.to}],effects:nc.of(s.to+e.length),range:_.range(s.anchor+e.length,s.head+e.length)};let o=dc(t.doc,s.head);return!o||/\s/.test(o)||n.indexOf(o)>-1?{changes:{insert:e+i,from:s.head},effects:nc.of(s.head+e.length),range:_.cursor(s.head+e.length)}:{range:r=s}});return r?null:t.update(s,{scrollIntoView:!0,userEvent:"input.type"})}function pc(t,e,i){let n=null,r=t.changeByRange(e=>e.empty&&dc(t.doc,e.head)==i?{changes:{from:e.head,to:e.head+i.length,insert:i},range:_.cursor(e.head+i.length)}:n={range:e});return n?null:t.update(r,{scrollIntoView:!0,userEvent:"input.type"})}function gc(t,e,i,n){let r=n.stringPrefixes||ic.stringPrefixes,s=null,o=t.changeByRange(n=>{if(!n.empty)return{changes:[{insert:e,from:n.from},{insert:e,from:n.to}],effects:nc.of(n.to+e.length),range:_.range(n.anchor+e.length,n.head+e.length)};let o,a=n.head,l=dc(t.doc,a);if(l==e){if(mc(t,a))return{changes:{insert:e+e,from:a},effects:nc.of(a+e.length),range:_.cursor(a+e.length)};if(uc(t,a)){let n=i&&t.sliceDoc(a,a+3*e.length)==e+e+e?e+e+e:e;return{changes:{from:a,to:a+n.length,insert:n},range:_.cursor(a+n.length)}}}else{if(i&&t.sliceDoc(a-2*e.length,a)==e+e&&(o=Qc(t,a-2*e.length,r))>-1&&mc(t,o))return{changes:{insert:e+e+e+e,from:a},effects:nc.of(a+e.length),range:_.cursor(a+e.length)};if(t.charCategorizer(a)(l)!=Qt.Word&&Qc(t,a,r)>-1&&!function(t,e,i,n){let r=oa(t).resolveInner(e,-1),s=n.reduce((t,e)=>Math.max(t,e.length),0);for(let o=0;o<5;o++){let o=t.sliceDoc(r.from,Math.min(r.to,r.from+i.length+s)),a=o.indexOf(i);if(!a||a>-1&&n.indexOf(o.slice(0,a))>-1){let e=r.firstChild;for(;e&&e.from==r.from&&e.to-e.from>i.length+a;){if(t.sliceDoc(e.to-i.length,e.to)==i)return!1;e=e.firstChild}return!0}let l=r.to==e&&r.parent;if(!l)break;r=l}return!1}(t,a,e,r))return{changes:{insert:e+e,from:a},effects:nc.of(a+e.length),range:_.cursor(a+e.length)}}return{range:s=n}});return s?null:t.update(o,{scrollIntoView:!0,userEvent:"input.type"})}function mc(t,e){let i=oa(t).resolveInner(e+1);return i.parent&&i.from==e}function Qc(t,e,i){let n=t.charCategorizer(e);if(n(t.sliceDoc(e-1,e))!=Qt.Word)return e;for(let r of i){let i=e-r.length;if(t.sliceDoc(i,e)==r&&n(t.sliceDoc(i-1,i))!=Qt.Word)return i}return-1}class Sc{static create(t,e,i,n,r){return new Sc(t,e,i,n+(n<<8)+t+(e<<4)|0,r,[],[])}constructor(t,e,i,n,r,s,o){this.type=t,this.value=e,this.from=i,this.hash=n,this.end=r,this.children=s,this.positions=o,this.hashProp=[[zs.contextHash,n]]}addChild(t,e){t.prop(zs.contextHash)!=this.hash&&(t=new js(t.type,t.children,t.positions,t.length,this.hashProp)),this.children.push(t),this.positions.push(e)}toTree(t,e=this.end){let i=this.children.length-1;return i>=0&&(e=Math.max(e,this.positions[i]+this.children[i].length+this.from)),new js(t.types[this.type],this.children,this.positions,e-this.from).balance({makeTree:(t,e,i)=>new js(Vs.none,t,e,i,this.hashProp)})}}var xc;!function(t){t[t.Document=1]="Document",t[t.CodeBlock=2]="CodeBlock",t[t.FencedCode=3]="FencedCode",t[t.Blockquote=4]="Blockquote",t[t.HorizontalRule=5]="HorizontalRule",t[t.BulletList=6]="BulletList",t[t.OrderedList=7]="OrderedList",t[t.ListItem=8]="ListItem",t[t.ATXHeading1=9]="ATXHeading1",t[t.ATXHeading2=10]="ATXHeading2",t[t.ATXHeading3=11]="ATXHeading3",t[t.ATXHeading4=12]="ATXHeading4",t[t.ATXHeading5=13]="ATXHeading5",t[t.ATXHeading6=14]="ATXHeading6",t[t.SetextHeading1=15]="SetextHeading1",t[t.SetextHeading2=16]="SetextHeading2",t[t.HTMLBlock=17]="HTMLBlock",t[t.LinkReference=18]="LinkReference",t[t.Paragraph=19]="Paragraph",t[t.CommentBlock=20]="CommentBlock",t[t.ProcessingInstructionBlock=21]="ProcessingInstructionBlock",t[t.Escape=22]="Escape",t[t.Entity=23]="Entity",t[t.HardBreak=24]="HardBreak",t[t.Emphasis=25]="Emphasis",t[t.StrongEmphasis=26]="StrongEmphasis",t[t.Link=27]="Link",t[t.Image=28]="Image",t[t.InlineCode=29]="InlineCode",t[t.HTMLTag=30]="HTMLTag",t[t.Comment=31]="Comment",t[t.ProcessingInstruction=32]="ProcessingInstruction",t[t.Autolink=33]="Autolink",t[t.HeaderMark=34]="HeaderMark",t[t.QuoteMark=35]="QuoteMark",t[t.ListMark=36]="ListMark",t[t.LinkMark=37]="LinkMark",t[t.EmphasisMark=38]="EmphasisMark",t[t.CodeMark=39]="CodeMark",t[t.CodeText=40]="CodeText",t[t.CodeInfo=41]="CodeInfo",t[t.LinkTitle=42]="LinkTitle",t[t.LinkLabel=43]="LinkLabel",t[t.URL=44]="URL"}(xc||(xc={}));class yc{constructor(t,e){this.start=t,this.content=e,this.marks=[],this.parsers=[]}}class wc{constructor(){this.text="",this.baseIndent=0,this.basePos=0,this.depth=0,this.markers=[],this.pos=0,this.indent=0,this.next=-1}forward(){this.basePos>this.pos&&this.forwardInner()}forwardInner(){let t=this.skipSpace(this.basePos);this.indent=this.countIndent(t,this.pos,this.indent),this.pos=t,this.next=t==this.text.length?-1:this.text.charCodeAt(t)}skipSpace(t){return $c(this.text,t)}reset(t){for(this.text=t,this.baseIndent=this.basePos=this.pos=this.indent=0,this.forwardInner(),this.depth=1;this.markers.length;)this.markers.pop()}moveBase(t){this.basePos=t,this.baseIndent=this.countIndent(t,this.pos,this.indent)}moveBaseColumn(t){this.baseIndent=t,this.basePos=this.findColumn(t)}addMarker(t){this.markers.push(t)}countIndent(t,e=0,i=0){for(let n=e;n<t;n++)i+=9==this.text.charCodeAt(n)?4-i%4:1;return i}findColumn(t){let e=0;for(let i=0;e<this.text.length&&i<t;e++)i+=9==this.text.charCodeAt(e)?4-i%4:1;return e}scrub(){if(!this.baseIndent)return this.text;let t="";for(let e=0;e<this.basePos;e++)t+=" ";return t+this.text.slice(this.basePos)}}function bc(t,e,i){if(i.pos==i.text.length||t!=e.block&&i.indent>=e.stack[i.depth+1].value+i.baseIndent)return!0;if(i.indent>=i.baseIndent+4)return!1;let n=(t.type==xc.OrderedList?Mc:Cc)(i,e,!1);return n>0&&(t.type!=xc.BulletList||Xc(i,e,!1)<0)&&i.text.charCodeAt(i.pos+n-1)==t.value}const kc={[xc.Blockquote]:(t,e,i)=>62==i.next&&(i.markers.push(aO(xc.QuoteMark,e.lineStart+i.pos,e.lineStart+i.pos+1)),i.moveBase(i.pos+(vc(i.text.charCodeAt(i.pos+1))?2:1)),t.end=e.lineStart+i.text.length,!0),[xc.ListItem]:(t,e,i)=>!(i.indent<i.baseIndent+t.value&&i.next>-1||(i.moveBaseColumn(i.baseIndent+t.value),0)),[xc.OrderedList]:bc,[xc.BulletList]:bc,[xc.Document]:()=>!0};function vc(t){return 32==t||9==t||10==t||13==t}function $c(t,e=0){for(;e<t.length&&vc(t.charCodeAt(e));)e++;return e}function Pc(t,e,i){for(;e>i&&vc(t.charCodeAt(e-1));)e--;return e}function Tc(t){if(96!=t.next&&126!=t.next)return-1;let e=t.pos+1;for(;e<t.text.length&&t.text.charCodeAt(e)==t.next;)e++;if(e<t.pos+3)return-1;if(96==t.next)for(let i=e;i<t.text.length;i++)if(96==t.text.charCodeAt(i))return-1;return e}function Zc(t){return 62!=t.next?-1:32==t.text.charCodeAt(t.pos+1)?2:1}function Xc(t,e,i){if(42!=t.next&&45!=t.next&&95!=t.next)return-1;let n=1;for(let e=t.pos+1;e<t.text.length;e++){let i=t.text.charCodeAt(e);if(i==t.next)n++;else if(!vc(i))return-1}return i&&45==t.next&&_c(t)>-1&&t.depth==e.stack.length&&e.parser.leafBlockParsers.indexOf(Gc.SetextHeading)>-1||n<3?-1:1}function Ac(t,e){for(let i=t.stack.length-1;i>=0;i--)if(t.stack[i].type==e)return!0;return!1}function Cc(t,e,i){return 45!=t.next&&43!=t.next&&42!=t.next||t.pos!=t.text.length-1&&!vc(t.text.charCodeAt(t.pos+1))||!(!i||Ac(e,xc.BulletList)||t.skipSpace(t.pos+2)<t.text.length)?-1:1}function Mc(t,e,i){let n=t.pos,r=t.next;for(;r>=48&&r<=57;){if(n++,n==t.text.length)return-1;r=t.text.charCodeAt(n)}return n==t.pos||n>t.pos+9||46!=r&&41!=r||n<t.text.length-1&&!vc(t.text.charCodeAt(n+1))||i&&!Ac(e,xc.OrderedList)&&(t.skipSpace(n+1)==t.text.length||n>t.pos+1||49!=t.next)?-1:n+1-t.pos}function Rc(t){if(35!=t.next)return-1;let e=t.pos+1;for(;e<t.text.length&&35==t.text.charCodeAt(e);)e++;if(e<t.text.length&&32!=t.text.charCodeAt(e))return-1;let i=e-t.pos;return i>6?-1:i}function _c(t){if(45!=t.next&&61!=t.next||t.indent>=t.baseIndent+4)return-1;let e=t.pos+1;for(;e<t.text.length&&t.text.charCodeAt(e)==t.next;)e++;let i=e;for(;e<t.text.length&&vc(t.text.charCodeAt(e));)e++;return e==t.text.length?i:-1}const zc=/^[ \t]*$/,Yc=/-->/,Ec=/\?>/,Vc=[[/^<(?:script|pre|style)(?:\s|>|$)/i,/<\/(?:script|pre|style)>/i],[/^\s*<!--/,Yc],[/^\s*<\?/,Ec],[/^\s*<![A-Z]/,/>/],[/^\s*<!\[CDATA\[/,/\]\]>/],[/^\s*<\/?(?:address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul)(?:\s|\/?>|$)/i,zc],[/^\s*(?:<\/[a-z][\w-]*\s*>|<[a-z][\w-]*(\s+[a-z:_][\w-.]*(?:\s*=\s*(?:[^\s"'=<>`]+|'[^']*'|"[^"]*"))?)*\s*>)\s*$/i,zc]];function qc(t,e,i){if(60!=t.next)return-1;let n=t.text.slice(t.pos);for(let t=0,e=Vc.length-(i?1:0);t<e;t++)if(Vc[t][0].test(n))return t;return-1}function Lc(t,e){let i=t.countIndent(e,t.pos,t.indent),n=t.countIndent(t.skipSpace(e),e,i);return n>=i+5?i+1:n}function Dc(t,e,i){let n=t.length-1;n>=0&&t[n].to==e&&t[n].type==xc.CodeText?t[n].to=i:t.push(aO(xc.CodeText,e,i))}const Wc={LinkReference:void 0,IndentedCode(t,e){let i=e.baseIndent+4;if(e.indent<i)return!1;let n=e.findColumn(i),r=t.lineStart+n,s=t.lineStart+e.text.length,o=[],a=[];for(Dc(o,r,s);t.nextLine()&&e.depth>=t.stack.length;)if(e.pos==e.text.length){Dc(a,t.lineStart-1,t.lineStart);for(let t of e.markers)a.push(t)}else{if(e.indent<i)break;{if(a.length){for(let t of a)t.type==xc.CodeText?Dc(o,t.from,t.to):o.push(t);a=[]}Dc(o,t.lineStart-1,t.lineStart);for(let t of e.markers)o.push(t);s=t.lineStart+e.text.length;let i=t.lineStart+e.findColumn(e.baseIndent+4);i<s&&Dc(o,i,s)}}return a.length&&(a=a.filter(t=>t.type!=xc.CodeText),a.length&&(e.markers=a.concat(e.markers))),t.addNode(t.buffer.writeElements(o,-r).finish(xc.CodeBlock,s-r),r),!0},FencedCode(t,e){let i=Tc(e);if(i<0)return!1;let n=t.lineStart+e.pos,r=e.next,s=i-e.pos,o=e.skipSpace(i),a=Pc(e.text,e.text.length,o),l=[aO(xc.CodeMark,n,n+s)];o<a&&l.push(aO(xc.CodeInfo,t.lineStart+o,t.lineStart+a));for(let i=!0;t.nextLine()&&e.depth>=t.stack.length;i=!1){let n=e.pos;if(e.indent-e.baseIndent<4)for(;n<e.text.length&&e.text.charCodeAt(n)==r;)n++;if(n-e.pos>=s&&e.skipSpace(n)==e.text.length){for(let t of e.markers)l.push(t);l.push(aO(xc.CodeMark,t.lineStart+e.pos,t.lineStart+n)),t.nextLine();break}{i||Dc(l,t.lineStart-1,t.lineStart);for(let t of e.markers)l.push(t);let n=t.lineStart+e.basePos,r=t.lineStart+e.text.length;n<r&&Dc(l,n,r)}}return t.addNode(t.buffer.writeElements(l,-n).finish(xc.FencedCode,t.prevLineEnd()-n),n),!0},Blockquote(t,e){let i=Zc(e);return!(i<0)&&(t.startContext(xc.Blockquote,e.pos),t.addNode(xc.QuoteMark,t.lineStart+e.pos,t.lineStart+e.pos+1),e.moveBase(e.pos+i),null)},HorizontalRule(t,e){if(Xc(e,t,!1)<0)return!1;let i=t.lineStart+e.pos;return t.nextLine(),t.addNode(xc.HorizontalRule,i),!0},BulletList(t,e){let i=Cc(e,t,!1);if(i<0)return!1;t.block.type!=xc.BulletList&&t.startContext(xc.BulletList,e.basePos,e.next);let n=Lc(e,e.pos+1);return t.startContext(xc.ListItem,e.basePos,n-e.baseIndent),t.addNode(xc.ListMark,t.lineStart+e.pos,t.lineStart+e.pos+i),e.moveBaseColumn(n),null},OrderedList(t,e){let i=Mc(e,t,!1);if(i<0)return!1;t.block.type!=xc.OrderedList&&t.startContext(xc.OrderedList,e.basePos,e.text.charCodeAt(e.pos+i-1));let n=Lc(e,e.pos+i);return t.startContext(xc.ListItem,e.basePos,n-e.baseIndent),t.addNode(xc.ListMark,t.lineStart+e.pos,t.lineStart+e.pos+i),e.moveBaseColumn(n),null},ATXHeading(t,e){let i=Rc(e);if(i<0)return!1;let n=e.pos,r=t.lineStart+n,s=Pc(e.text,e.text.length,n),o=s;for(;o>n&&e.text.charCodeAt(o-1)==e.next;)o--;o!=s&&o!=n&&vc(e.text.charCodeAt(o-1))||(o=e.text.length);let a=t.buffer.write(xc.HeaderMark,0,i).writeElements(t.parser.parseInline(e.text.slice(n+i+1,o),r+i+1),-r);o<e.text.length&&a.write(xc.HeaderMark,o-n,s-n);let l=a.finish(xc.ATXHeading1-1+i,e.text.length-n);return t.nextLine(),t.addNode(l,r),!0},HTMLBlock(t,e){let i=qc(e,0,!1);if(i<0)return!1;let n=t.lineStart+e.pos,r=Vc[i][1],s=[],o=r!=zc;for(;!r.test(e.text)&&t.nextLine();){if(e.depth<t.stack.length){o=!1;break}for(let t of e.markers)s.push(t)}o&&t.nextLine();let a=r==Yc?xc.CommentBlock:r==Ec?xc.ProcessingInstructionBlock:xc.HTMLBlock,l=t.prevLineEnd();return t.addNode(t.buffer.writeElements(s,-n).finish(a,l-n),n),!0},SetextHeading:void 0};class jc{constructor(t){this.stage=0,this.elts=[],this.pos=0,this.start=t.start,this.advance(t.content)}nextLine(t,e,i){if(-1==this.stage)return!1;let n=i.content+"\n"+e.scrub(),r=this.advance(n);return r>-1&&r<n.length&&this.complete(t,i,r)}finish(t,e){return(2==this.stage||3==this.stage)&&$c(e.content,this.pos)==e.content.length&&this.complete(t,e,e.content.length)}complete(t,e,i){return t.addLeafElement(e,aO(xc.LinkReference,this.start,this.start+i,this.elts)),!0}nextStage(t){return t?(this.pos=t.to-this.start,this.elts.push(t),this.stage++,!0):(!1===t&&(this.stage=-1),!1)}advance(t){for(;;){if(-1==this.stage)return-1;if(0==this.stage){if(!this.nextStage(QO(t,this.pos,this.start,!0)))return-1;if(58!=t.charCodeAt(this.pos))return this.stage=-1;this.elts.push(aO(xc.LinkMark,this.pos+this.start,this.pos+this.start+1)),this.pos++}else{if(1!=this.stage){if(2==this.stage){let e=$c(t,this.pos),i=0;if(e>this.pos){let n=mO(t,e,this.start);if(n){let e=Bc(t,n.to-this.start);e>0&&(this.nextStage(n),i=e)}}return i||(i=Bc(t,this.pos)),i>0&&i<t.length?i:-1}return Bc(t,this.pos)}if(!this.nextStage(gO(t,$c(t,this.pos),this.start)))return-1}}}}function Bc(t,e){for(;e<t.length;e++){let i=t.charCodeAt(e);if(10==i)break;if(!vc(i))return-1}return e}class Ic{nextLine(t,e,i){let n=e.depth<t.stack.length?-1:_c(e),r=e.next;if(n<0)return!1;let s=aO(xc.HeaderMark,t.lineStart+e.pos,t.lineStart+n);return t.nextLine(),t.addLeafElement(i,aO(61==r?xc.SetextHeading1:xc.SetextHeading2,i.start,t.prevLineEnd(),[...t.parser.parseInline(i.content,i.start),s])),!0}finish(){return!1}}const Gc={LinkReference:(t,e)=>91==e.content.charCodeAt(0)?new jc(e):null,SetextHeading:()=>new Ic},Nc=[(t,e)=>Rc(e)>=0,(t,e)=>Tc(e)>=0,(t,e)=>Zc(e)>=0,(t,e)=>Cc(e,t,!0)>=0,(t,e)=>Mc(e,t,!0)>=0,(t,e)=>Xc(e,t,!0)>=0,(t,e)=>qc(e,0,!0)>=0],Uc={text:"",end:0};class Hc{constructor(t,e,i,n){this.parser=t,this.input=e,this.ranges=n,this.line=new wc,this.atEnd=!1,this.reusePlaceholders=new Map,this.stoppedAt=null,this.rangeI=0,this.to=n[n.length-1].to,this.lineStart=this.absoluteLineStart=this.absoluteLineEnd=n[0].from,this.block=Sc.create(xc.Document,0,this.lineStart,0,0),this.stack=[this.block],this.fragments=i.length?new wO(i,e):null,this.readLine()}get parsedPos(){return this.absoluteLineStart}advance(){if(null!=this.stoppedAt&&this.absoluteLineStart>this.stoppedAt)return this.finish();let{line:t}=this;for(;;){for(let e=0;;){let i=t.depth<this.stack.length?this.stack[this.stack.length-1]:null;for(;e<t.markers.length&&(!i||t.markers[e].from<i.end);){let i=t.markers[e++];this.addNode(i.type,i.from,i.to)}if(!i)break;this.finishContext()}if(t.pos<t.text.length)break;if(!this.nextLine())return this.finish()}if(this.fragments&&this.reuseFragment(t.basePos))return null;t:for(;;){for(let e of this.parser.blockParsers)if(e){let i=e(this,t);if(0!=i){if(1==i)return null;t.forward();continue t}}break}let e=new yc(this.lineStart+t.pos,t.text.slice(t.pos));for(let t of this.parser.leafBlockParsers)if(t){let i=t(this,e);i&&e.parsers.push(i)}t:for(;this.nextLine()&&t.pos!=t.text.length;){if(t.indent<t.baseIndent+4)for(let i of this.parser.endLeafBlock)if(i(this,t,e))break t;for(let i of e.parsers)if(i.nextLine(this,t,e))return null;e.content+="\n"+t.scrub();for(let i of t.markers)e.marks.push(i)}return this.finishLeaf(e),null}stopAt(t){if(null!=this.stoppedAt&&this.stoppedAt<t)throw new RangeError("Can't move stoppedAt forward");this.stoppedAt=t}reuseFragment(t){if(!this.fragments.moveTo(this.absoluteLineStart+t,this.absoluteLineStart)||!this.fragments.matches(this.block.hash))return!1;let e=this.fragments.takeNodes(this);return!!e&&(this.absoluteLineStart+=e,this.lineStart=bO(this.absoluteLineStart,this.ranges),this.moveRangeI(),this.absoluteLineStart<this.to?(this.lineStart++,this.absoluteLineStart++,this.readLine()):(this.atEnd=!0,this.readLine()),!0)}get depth(){return this.stack.length}parentType(t=this.depth-1){return this.parser.nodeSet.types[this.stack[t].type]}nextLine(){return this.lineStart+=this.line.text.length,this.absoluteLineEnd>=this.to?(this.absoluteLineStart=this.absoluteLineEnd,this.atEnd=!0,this.readLine(),!1):(this.lineStart++,this.absoluteLineStart=this.absoluteLineEnd+1,this.moveRangeI(),this.readLine(),!0)}peekLine(){return this.scanLine(this.absoluteLineEnd+1).text}moveRangeI(){for(;this.rangeI<this.ranges.length-1&&this.absoluteLineStart>=this.ranges[this.rangeI].to;)this.rangeI++,this.absoluteLineStart=Math.max(this.absoluteLineStart,this.ranges[this.rangeI].from)}scanLine(t){let e=Uc;if(e.end=t,t>=this.to)e.text="";else if(e.text=this.lineChunkAt(t),e.end+=e.text.length,this.ranges.length>1){let t=this.absoluteLineStart,i=this.rangeI;for(;this.ranges[i].to<e.end;){i++;let n=this.ranges[i].from,r=this.lineChunkAt(n);e.end=n+r.length,e.text=e.text.slice(0,this.ranges[i-1].to-t)+r,t=e.end-e.text.length}}return e}readLine(){let{line:t}=this,{text:e,end:i}=this.scanLine(this.absoluteLineStart);for(this.absoluteLineEnd=i,t.reset(e);t.depth<this.stack.length;t.depth++){let e=this.stack[t.depth],i=this.parser.skipContextMarkup[e.type];if(!i)throw new Error("Unhandled block context "+xc[e.type]);if(!i(e,this,t))break;t.forward()}}lineChunkAt(t){let e,i=this.input.chunk(t);if(this.input.lineChunks)e="\n"==i?"":i;else{let t=i.indexOf("\n");e=t<0?i:i.slice(0,t)}return t+e.length>this.to?e.slice(0,this.to-t):e}prevLineEnd(){return this.atEnd?this.lineStart:this.lineStart-1}startContext(t,e,i=0){this.block=Sc.create(t,i,this.lineStart+e,this.block.hash,this.lineStart+this.line.text.length),this.stack.push(this.block)}startComposite(t,e,i=0){this.startContext(this.parser.getNodeType(t),e,i)}addNode(t,e,i){"number"==typeof t&&(t=new js(this.parser.nodeSet.types[t],nO,nO,(null!=i?i:this.prevLineEnd())-e)),this.block.addChild(t,e-this.block.from)}addElement(t){this.block.addChild(t.toTree(this.parser.nodeSet),t.from-this.block.from)}addLeafElement(t,e){this.addNode(this.buffer.writeElements(xO(e.children,t.marks),-e.from).finish(e.type,e.to-e.from),e.from)}finishContext(){let t=this.stack.pop(),e=this.stack[this.stack.length-1];e.addChild(t.toTree(this.parser.nodeSet),t.from-e.from),this.block=e}finish(){for(;this.stack.length>1;)this.finishContext();return this.addGaps(this.block.toTree(this.parser.nodeSet,this.lineStart))}addGaps(t){return this.ranges.length>1?Fc(this.ranges,0,t.topNode,this.ranges[0].from,this.reusePlaceholders):t}finishLeaf(t){for(let e of t.parsers)if(e.finish(this,t))return;let e=xO(this.parser.parseInline(t.content,t.start),t.marks);this.addNode(this.buffer.writeElements(e,-t.start).finish(xc.Paragraph,t.content.length),t.start)}elt(t,e,i,n){return"string"==typeof t?aO(this.parser.getNodeType(t),e,i,n):new oO(t,e)}get buffer(){return new rO(this.parser.nodeSet)}}function Fc(t,e,i,n,r){let s=t[e].to,o=[],a=[],l=i.from+n;function h(i,r){for(;r?i>=s:i>s;){let r=t[e+1].from-s;n+=r,i+=r,e++,s=t[e].to}}for(let c=i.firstChild;c;c=c.nextSibling){h(c.from+n,!0);let i,O=c.from+n,u=r.get(c.tree);u?i=u:c.to+n>s?(i=Fc(t,e,c,n,r),h(c.to+n,!1)):i=c.toTree(),o.push(i),a.push(O-l)}return h(i.to+n,!1),new js(i.type,o,a,i.to+n-l,i.tree?i.tree.propValues:void 0)}class Kc extends co{constructor(t,e,i,n,r,s,o,a,l){super(),this.nodeSet=t,this.blockParsers=e,this.leafBlockParsers=i,this.blockNames=n,this.endLeafBlock=r,this.skipContextMarkup=s,this.inlineParsers=o,this.inlineNames=a,this.wrappers=l,this.nodeTypes=Object.create(null);for(let e of t.types)this.nodeTypes[e.name]=e.id}createParse(t,e,i){let n=new Hc(this,t,e,i);for(let r of this.wrappers)n=r(n,t,e,i);return n}configure(t){let e=tO(t);if(!e)return this;let{nodeSet:i,skipContextMarkup:n}=this,r=this.blockParsers.slice(),s=this.leafBlockParsers.slice(),o=this.blockNames.slice(),a=this.inlineParsers.slice(),l=this.inlineNames.slice(),h=this.endLeafBlock.slice(),c=this.wrappers;if(Jc(e.defineNodes)){n=Object.assign({},n);let t,r=i.types.slice();for(let i of e.defineNodes){let{name:e,block:s,composite:o,style:a}="string"==typeof i?{name:i}:i;if(r.some(t=>t.name==e))continue;o&&(n[r.length]=(t,e,i)=>o(e,i,t.value));let l=r.length,h=o?["Block","BlockContext"]:s?l>=xc.ATXHeading1&&l<=xc.SetextHeading2?["Block","LeafBlock","Heading"]:["Block","LeafBlock"]:void 0;r.push(Vs.define({id:l,name:e,props:h&&[[zs.group,h]]})),a&&(t||(t={}),Array.isArray(a)||a instanceof To?t[e]=a:Object.assign(t,a))}i=new qs(r),t&&(i=i.extend(Ao(t)))}if(Jc(e.props)&&(i=i.extend(...e.props)),Jc(e.remove))for(let t of e.remove){let e=this.blockNames.indexOf(t),i=this.inlineNames.indexOf(t);e>-1&&(r[e]=s[e]=void 0),i>-1&&(a[i]=void 0)}if(Jc(e.parseBlock))for(let t of e.parseBlock){let e=o.indexOf(t.name);if(e>-1)r[e]=t.parse,s[e]=t.leaf;else{let e=t.before?eO(o,t.before):t.after?eO(o,t.after)+1:o.length-1;r.splice(e,0,t.parse),s.splice(e,0,t.leaf),o.splice(e,0,t.name)}t.endLeaf&&h.push(t.endLeaf)}if(Jc(e.parseInline))for(let t of e.parseInline){let e=l.indexOf(t.name);if(e>-1)a[e]=t.parse;else{let e=t.before?eO(l,t.before):t.after?eO(l,t.after)+1:l.length-1;a.splice(e,0,t.parse),l.splice(e,0,t.name)}}return e.wrap&&(c=c.concat(e.wrap)),new Kc(i,r,s,o,h,n,a,l,c)}getNodeType(t){let e=this.nodeTypes[t];if(null==e)throw new RangeError(`Unknown node type '${t}'`);return e}parseInline(t,e){let i=new SO(this,t,e);t:for(let t=e;t<i.end;){let e=i.char(t);for(let n of this.inlineParsers)if(n){let r=n(i,e,t);if(r>=0){t=r;continue t}}t++}return i.resolveMarkers(0)}}function Jc(t){return null!=t&&t.length>0}function tO(t){if(!Array.isArray(t))return t;if(0==t.length)return null;let e=tO(t[0]);if(1==t.length)return e;let i=tO(t.slice(1));if(!i||!e)return e||i;let n=(t,e)=>(t||nO).concat(e||nO),r=e.wrap,s=i.wrap;return{props:n(e.props,i.props),defineNodes:n(e.defineNodes,i.defineNodes),parseBlock:n(e.parseBlock,i.parseBlock),parseInline:n(e.parseInline,i.parseInline),remove:n(e.remove,i.remove),wrap:r?s?(t,e,i,n)=>r(s(t,e,i,n),e,i,n):r:s}}function eO(t,e){let i=t.indexOf(e);if(i<0)throw new RangeError(`Position specified relative to unknown parser ${e}`);return i}let iO=[Vs.none];for(let t,e=1;t=xc[e];e++)iO[e]=Vs.define({id:e,name:t,props:e>=xc.Escape?[]:[[zs.group,e in kc?["Block","BlockContext"]:["Block","LeafBlock"]]],top:"Document"==t});const nO=[];class rO{constructor(t){this.nodeSet=t,this.content=[],this.nodes=[]}write(t,e,i,n=0){return this.content.push(t,e,i,4+4*n),this}writeElements(t,e=0){for(let i of t)i.writeTo(this,e);return this}finish(t,e){return js.build({buffer:this.content,nodeSet:this.nodeSet,reused:this.nodes,topID:t,length:e})}}class sO{constructor(t,e,i,n=nO){this.type=t,this.from=e,this.to=i,this.children=n}writeTo(t,e){let i=t.content.length;t.writeElements(this.children,e),t.content.push(this.type,this.from+e,this.to+e,t.content.length+4-i)}toTree(t){return new rO(t).writeElements(this.children,-this.from).finish(this.type,this.to-this.from)}}class oO{constructor(t,e){this.tree=t,this.from=e}get to(){return this.from+this.tree.length}get type(){return this.tree.type.id}get children(){return nO}writeTo(t,e){t.nodes.push(this.tree),t.content.push(t.nodes.length-1,this.from+e,this.to+e,-1)}toTree(){return this.tree}}function aO(t,e,i,n){return new sO(t,e,i,n)}const lO={resolve:"Emphasis",mark:"EmphasisMark"},hO={resolve:"Emphasis",mark:"EmphasisMark"},cO={},OO={};class uO{constructor(t,e,i,n){this.type=t,this.from=e,this.to=i,this.side=n}}let dO=/[!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~\xA1\u2010-\u2027]/;try{dO=new RegExp("[\\p{S}|\\p{P}]","u")}catch(t){}const fO={Escape(t,e,i){if(92!=e||i==t.end-1)return-1;let n=t.char(i+1);for(let e=0;e<32;e++)if("!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~".charCodeAt(e)==n)return t.append(aO(xc.Escape,i,i+2));return-1},Entity(t,e,i){if(38!=e)return-1;let n=/^(?:#\d+|#x[a-f\d]+|\w+);/i.exec(t.slice(i+1,i+31));return n?t.append(aO(xc.Entity,i,i+1+n[0].length)):-1},InlineCode(t,e,i){if(96!=e||i&&96==t.char(i-1))return-1;let n=i+1;for(;n<t.end&&96==t.char(n);)n++;let r=n-i,s=0;for(;n<t.end;n++)if(96==t.char(n)){if(s++,s==r&&96!=t.char(n+1))return t.append(aO(xc.InlineCode,i,n+1,[aO(xc.CodeMark,i,i+r),aO(xc.CodeMark,n+1-r,n+1)]))}else s=0;return-1},HTMLTag(t,e,i){if(60!=e||i==t.end-1)return-1;let n=t.slice(i+1,t.end),r=/^(?:[a-z][-\w+.]+:[^\s>]+|[a-z\d.!#$%&'*+/=?^_`{|}~-]+@[a-z\d](?:[a-z\d-]{0,61}[a-z\d])?(?:\.[a-z\d](?:[a-z\d-]{0,61}[a-z\d])?)*)>/i.exec(n);if(r)return t.append(aO(xc.Autolink,i,i+1+r[0].length,[aO(xc.LinkMark,i,i+1),aO(xc.URL,i+1,i+r[0].length),aO(xc.LinkMark,i+r[0].length,i+1+r[0].length)]));let s=/^!--[^>](?:-[^-]|[^-])*?-->/i.exec(n);if(s)return t.append(aO(xc.Comment,i,i+1+s[0].length));let o=/^\?[^]*?\?>/.exec(n);if(o)return t.append(aO(xc.ProcessingInstruction,i,i+1+o[0].length));let a=/^(?:![A-Z][^]*?>|!\[CDATA\[[^]*?\]\]>|\/\s*[a-zA-Z][\w-]*\s*>|\s*[a-zA-Z][\w-]*(\s+[a-zA-Z:_][\w-.:]*(?:\s*=\s*(?:[^\s"'=<>`]+|'[^']*'|"[^"]*"))?)*\s*(\/\s*)?>)/.exec(n);return a?t.append(aO(xc.HTMLTag,i,i+1+a[0].length)):-1},Emphasis(t,e,i){if(95!=e&&42!=e)return-1;let n=i+1;for(;t.char(n)==e;)n++;let r=t.slice(i-1,i),s=t.slice(n,n+1),o=dO.test(r),a=dO.test(s),l=/\s|^$/.test(r),h=/\s|^$/.test(s),c=!h&&(!a||l||o),O=!l&&(!o||h||a),u=c&&(42==e||!O||o),d=O&&(42==e||!c||a);return t.append(new uO(95==e?lO:hO,i,n,(u?1:0)|(d?2:0)))},HardBreak(t,e,i){if(92==e&&10==t.char(i+1))return t.append(aO(xc.HardBreak,i,i+2));if(32==e){let e=i+1;for(;32==t.char(e);)e++;if(10==t.char(e)&&e>=i+2)return t.append(aO(xc.HardBreak,i,e+1))}return-1},Link:(t,e,i)=>91==e?t.append(new uO(cO,i,i+1,1)):-1,Image:(t,e,i)=>33==e&&91==t.char(i+1)?t.append(new uO(OO,i,i+2,1)):-1,LinkEnd(t,e,i){if(93!=e)return-1;for(let e=t.parts.length-1;e>=0;e--){let n=t.parts[e];if(n instanceof uO&&(n.type==cO||n.type==OO)){if(!n.side||t.skipSpace(n.to)==i&&!/[(\[]/.test(t.slice(i+1,i+2)))return t.parts[e]=null,-1;let r=t.takeContent(e),s=t.parts[e]=pO(t,r,n.type==cO?xc.Link:xc.Image,n.from,i+1);if(n.type==cO)for(let i=0;i<e;i++){let e=t.parts[i];e instanceof uO&&e.type==cO&&(e.side=0)}return s.to}}return-1}};function pO(t,e,i,n,r){let{text:s}=t,o=t.char(r),a=r;if(e.unshift(aO(xc.LinkMark,n,n+(i==xc.Image?2:1))),e.push(aO(xc.LinkMark,r-1,r)),40==o){let i,n=t.skipSpace(r+1),o=gO(s,n-t.offset,t.offset);o&&(n=t.skipSpace(o.to),n!=o.to&&(i=mO(s,n-t.offset,t.offset),i&&(n=t.skipSpace(i.to)))),41==t.char(n)&&(e.push(aO(xc.LinkMark,r,r+1)),a=n+1,o&&e.push(o),i&&e.push(i),e.push(aO(xc.LinkMark,n,a)))}else if(91==o){let i=QO(s,r-t.offset,t.offset,!1);i&&(e.push(i),a=i.to)}return aO(i,n,a,e)}function gO(t,e,i){if(60==t.charCodeAt(e)){for(let n=e+1;n<t.length;n++){let r=t.charCodeAt(n);if(62==r)return aO(xc.URL,e+i,n+1+i);if(60==r||10==r)return!1}return null}{let n=0,r=e;for(let e=!1;r<t.length;r++){let i=t.charCodeAt(r);if(vc(i))break;if(e)e=!1;else if(40==i)n++;else if(41==i){if(!n)break;n--}else 92==i&&(e=!0)}return r>e?aO(xc.URL,e+i,r+i):r==t.length&&null}}function mO(t,e,i){let n=t.charCodeAt(e);if(39!=n&&34!=n&&40!=n)return!1;let r=40==n?41:n;for(let n=e+1,s=!1;n<t.length;n++){let o=t.charCodeAt(n);if(s)s=!1;else{if(o==r)return aO(xc.LinkTitle,e+i,n+1+i);92==o&&(s=!0)}}return null}function QO(t,e,i,n){for(let r=!1,s=e+1,o=Math.min(t.length,s+999);s<o;s++){let o=t.charCodeAt(s);if(r)r=!1;else{if(93==o)return!n&&aO(xc.LinkLabel,e+i,s+1+i);if(n&&!vc(o)&&(n=!1),91==o)return!1;92==o&&(r=!0)}}return null}class SO{constructor(t,e,i){this.parser=t,this.text=e,this.offset=i,this.parts=[]}char(t){return t>=this.end?-1:this.text.charCodeAt(t-this.offset)}get end(){return this.offset+this.text.length}slice(t,e){return this.text.slice(t-this.offset,e-this.offset)}append(t){return this.parts.push(t),t.to}addDelimiter(t,e,i,n,r){return this.append(new uO(t,e,i,(n?1:0)|(r?2:0)))}get hasOpenLink(){for(let t=this.parts.length-1;t>=0;t--){let e=this.parts[t];if(e instanceof uO&&(e.type==cO||e.type==OO))return!0}return!1}addElement(t){return this.append(t)}resolveMarkers(t){for(let e=t;e<this.parts.length;e++){let i=this.parts[e];if(!(i instanceof uO&&i.type.resolve&&2&i.side))continue;let n,r=i.type==lO||i.type==hO,s=i.to-i.from,o=e-1;for(;o>=t;o--){let t=this.parts[o];if(t instanceof uO&&1&t.side&&t.type==i.type&&!(r&&(1&i.side||2&t.side)&&(t.to-t.from+s)%3==0&&((t.to-t.from)%3||s%3))){n=t;break}}if(!n)continue;let a=i.type.resolve,l=[],h=n.from,c=i.to;if(r){let t=Math.min(2,n.to-n.from,s);h=n.to-t,c=i.from+t,a=1==t?"Emphasis":"StrongEmphasis"}n.type.mark&&l.push(this.elt(n.type.mark,h,n.to));for(let t=o+1;t<e;t++)this.parts[t]instanceof sO&&l.push(this.parts[t]),this.parts[t]=null;i.type.mark&&l.push(this.elt(i.type.mark,i.from,c));let O=this.elt(a,h,c,l);this.parts[o]=r&&n.from!=h?new uO(n.type,n.from,h,n.side):null,(this.parts[e]=r&&i.to!=c?new uO(i.type,c,i.to,i.side):null)?this.parts.splice(e,0,O):this.parts[e]=O}let e=[];for(let i=t;i<this.parts.length;i++){let t=this.parts[i];t instanceof sO&&e.push(t)}return e}findOpeningDelimiter(t){for(let e=this.parts.length-1;e>=0;e--){let i=this.parts[e];if(i instanceof uO&&i.type==t)return e}return null}takeContent(t){let e=this.resolveMarkers(t);return this.parts.length=t,e}skipSpace(t){return $c(this.text,t-this.offset)+this.offset}elt(t,e,i,n){return"string"==typeof t?aO(this.parser.getNodeType(t),e,i,n):new oO(t,e)}}function xO(t,e){if(!e.length)return t;if(!t.length)return e;let i=t.slice(),n=0;for(let t of e){for(;n<i.length&&i[n].to<t.to;)n++;if(n<i.length&&i[n].from<t.from){let e=i[n];e instanceof sO&&(i[n]=new sO(e.type,e.from,e.to,xO(e.children,[t])))}else i.splice(n++,0,t)}return i}const yO=[xc.CodeBlock,xc.ListItem,xc.OrderedList,xc.BulletList];class wO{constructor(t,e){this.fragments=t,this.input=e,this.i=0,this.fragment=null,this.fragmentEnd=-1,this.cursor=null,t.length&&(this.fragment=t[this.i++])}nextFragment(){this.fragment=this.i<this.fragments.length?this.fragments[this.i++]:null,this.cursor=null,this.fragmentEnd=-1}moveTo(t,e){for(;this.fragment&&this.fragment.to<=t;)this.nextFragment();if(!this.fragment||this.fragment.from>(t?t-1:0))return!1;if(this.fragmentEnd<0){let t=this.fragment.to;for(;t>0&&"\n"!=this.input.read(t-1,t);)t--;this.fragmentEnd=t?t-1:0}let i=this.cursor;i||(i=this.cursor=this.fragment.tree.cursor(),i.firstChild());let n=t+this.fragment.offset;for(;i.to<=n;)if(!i.parent())return!1;for(;;){if(i.from>=n)return this.fragment.from<=e;if(!i.childAfter(n))return!1}}matches(t){let e=this.cursor.tree;return e&&e.prop(zs.contextHash)==t}takeNodes(t){let e=this.cursor,i=this.fragment.offset,n=this.fragmentEnd-(this.fragment.openEnd?1:0),r=t.absoluteLineStart,s=r,o=t.block.children.length,a=s,l=o;for(;;){if(e.to-i>n){if(e.type.isAnonymous&&e.firstChild())continue;break}let r=bO(e.from-i,t.ranges);if(e.to-i<=t.ranges[t.rangeI].to)t.addNode(e.tree,r);else{let i=new js(t.parser.nodeSet.types[xc.Paragraph],[],[],0,t.block.hashProp);t.reusePlaceholders.set(i,e.tree),t.addNode(i,r)}if(e.type.is("Block")&&(yO.indexOf(e.type.id)<0?(s=e.to-i,o=t.block.children.length):(s=a,o=l,a=e.to-i,l=t.block.children.length)),!e.nextSibling())break}for(;t.block.children.length>o;)t.block.children.pop(),t.block.positions.pop();return s-r}}function bO(t,e){let i=t;for(let n=1;n<e.length;n++){let r=e[n-1].to,s=e[n].from;r<t&&(i-=s-r)}return i}const kO=Ao({"Blockquote/...":Ko.quote,HorizontalRule:Ko.contentSeparator,"ATXHeading1/... SetextHeading1/...":Ko.heading1,"ATXHeading2/... SetextHeading2/...":Ko.heading2,"ATXHeading3/...":Ko.heading3,"ATXHeading4/...":Ko.heading4,"ATXHeading5/...":Ko.heading5,"ATXHeading6/...":Ko.heading6,"Comment CommentBlock":Ko.comment,Escape:Ko.escape,Entity:Ko.character,"Emphasis/...":Ko.emphasis,"StrongEmphasis/...":Ko.strong,"Link/... Image/...":Ko.link,"OrderedList/... BulletList/...":Ko.list,"BlockQuote/...":Ko.quote,"InlineCode CodeText":Ko.monospace,"URL Autolink":Ko.url,"HeaderMark HardBreak QuoteMark ListMark LinkMark EmphasisMark CodeMark":Ko.processingInstruction,"CodeInfo LinkLabel":Ko.labelName,LinkTitle:Ko.string,Paragraph:Ko.content}),vO=new Kc(new qs(iO).extend(kO),Object.keys(Wc).map(t=>Wc[t]),Object.keys(Wc).map(t=>Gc[t]),Object.keys(Wc),Nc,kc,Object.keys(fO).map(t=>fO[t]),Object.keys(fO),[]);function $O(t,e,i){let n=[];for(let r=t.firstChild,s=e;;r=r.nextSibling){let t=r?r.from:i;if(t>s&&n.push({from:s,to:t}),!r)break;s=r.to}return n}const PO={resolve:"Strikethrough",mark:"StrikethroughMark"},TO={defineNodes:[{name:"Strikethrough",style:{"Strikethrough/...":Ko.strikethrough}},{name:"StrikethroughMark",style:Ko.processingInstruction}],parseInline:[{name:"Strikethrough",parse(t,e,i){if(126!=e||126!=t.char(i+1)||126==t.char(i+2))return-1;let n=t.slice(i-1,i),r=t.slice(i+2,i+3),s=/\s|^$/.test(n),o=/\s|^$/.test(r),a=dO.test(n),l=dO.test(r);return t.addDelimiter(PO,i,i+2,!o&&(!l||s||a),!s&&(!a||o||l))},after:"Emphasis"}]};function ZO(t,e,i=0,n,r=0){let s=0,o=!0,a=-1,l=-1,h=!1,c=()=>{n.push(t.elt("TableCell",r+a,r+l,t.parser.parseInline(e.slice(a,l),r+a)))};for(let O=i;O<e.length;O++){let i=e.charCodeAt(O);124!=i||h?(h||32!=i&&9!=i)&&(a<0&&(a=O),l=O+1):((!o||a>-1)&&s++,o=!1,n&&(a>-1&&c(),n.push(t.elt("TableDelimiter",O+r,O+r+1))),a=l=-1),h=!h&&92==i}return a>-1&&(s++,n&&c()),s}function XO(t,e){for(let i=e;i<t.length;i++){let e=t.charCodeAt(i);if(124==e)return!0;92==e&&i++}return!1}const AO=/^\|?(\s*:?-+:?\s*\|)+(\s*:?-+:?\s*)?$/;class CO{constructor(){this.rows=null}nextLine(t,e,i){if(null==this.rows){let n;if(this.rows=!1,(45==e.next||58==e.next||124==e.next)&&AO.test(n=e.text.slice(e.pos))){let r=[];ZO(t,i.content,0,r,i.start)==ZO(t,n,e.pos)&&(this.rows=[t.elt("TableHeader",i.start,i.start+i.content.length,r),t.elt("TableDelimiter",t.lineStart+e.pos,t.lineStart+e.text.length)])}}else if(this.rows){let i=[];ZO(t,e.text,e.pos,i,t.lineStart),this.rows.push(t.elt("TableRow",t.lineStart+e.pos,t.lineStart+e.text.length,i))}return!1}finish(t,e){return!!this.rows&&(t.addLeafElement(e,t.elt("Table",e.start,e.start+e.content.length,this.rows)),!0)}}const MO={defineNodes:[{name:"Table",block:!0},{name:"TableHeader",style:{"TableHeader/...":Ko.heading}},"TableRow",{name:"TableCell",style:Ko.content},{name:"TableDelimiter",style:Ko.processingInstruction}],parseBlock:[{name:"Table",leaf:(t,e)=>XO(e.content,0)?new CO:null,endLeaf(t,e,i){if(i.parsers.some(t=>t instanceof CO)||!XO(e.text,e.basePos))return!1;let n=t.peekLine();return AO.test(n)&&ZO(t,e.text,e.basePos)==ZO(t,n,e.basePos)},before:"SetextHeading"}]};class RO{nextLine(){return!1}finish(t,e){return t.addLeafElement(e,t.elt("Task",e.start,e.start+e.content.length,[t.elt("TaskMarker",e.start,e.start+3),...t.parser.parseInline(e.content.slice(3),e.start+3)])),!0}}const _O={defineNodes:[{name:"Task",block:!0,style:Ko.list},{name:"TaskMarker",style:Ko.atom}],parseBlock:[{name:"TaskList",leaf:(t,e)=>/^\[[ xX]\][ \t]/.test(e.content)&&"ListItem"==t.parentType().name?new RO:null,after:"SetextHeading"}]},zO=/(www\.)|(https?:\/\/)|([\w.+-]{1,100}@)|(mailto:|xmpp:)/gy,YO=/[\w-]+(\.[\w-]+)+(\/[^\s<]*)?/gy,EO=/[\w-]+\.[\w-]+($|\/)/,VO=/[\w.+-]+@[\w-]+(\.[\w.-]+)+/gy,qO=/\/[a-zA-Z\d@.]+/gy;function LO(t,e,i,n){let r=0;for(let s=e;s<i;s++)t[s]==n&&r++;return r}function DO(t,e){VO.lastIndex=e;let i=VO.exec(t);if(!i)return-1;let n=i[0][i[0].length-1];return"_"==n||"-"==n?-1:e+i[0].length-("."==n?1:0)}const WO=[MO,_O,TO,{parseInline:[{name:"Autolink",parse(t,e,i){let n=i-t.offset;if(n&&/\w/.test(t.text[n-1]))return-1;zO.lastIndex=n;let r=zO.exec(t.text),s=-1;return r?(r[1]||r[2]?(s=function(t,e){YO.lastIndex=e;let i=YO.exec(t);if(!i||EO.exec(i[0])[0].indexOf("_")>-1)return-1;let n=e+i[0].length;for(;;){let i,r=t[n-1];if(/[?!.,:*_~]/.test(r)||")"==r&&LO(t,e,n,")")>LO(t,e,n,"("))n--;else{if(";"!=r||!(i=/&(?:#\d+|#x[a-f\d]+|\w+);$/.exec(t.slice(e,n))))break;n=e+i.index}}return n}(t.text,n+r[0].length),s>-1&&t.hasOpenLink&&(s=n+/([^\[\]]|\[[^\]]*\])*/.exec(t.text.slice(n,s))[0].length)):r[3]?s=DO(t.text,n):(s=DO(t.text,n+r[0].length),s>-1&&"xmpp:"==r[0]&&(qO.lastIndex=s,r=qO.exec(t.text),r&&(s=r.index+r[0].length))),s<0?-1:(t.addElement(t.elt("URL",i,s+t.offset)),s+t.offset)):-1}}]}];function jO(t,e,i){return(n,r,s)=>{if(r!=t||n.char(s+1)==t)return-1;let o=[n.elt(i,s,s+1)];for(let r=s+1;r<n.end;r++){let a=n.char(r);if(a==t)return n.addElement(n.elt(e,s,r+1,o.concat(n.elt(i,r,r+1))));if(92==a&&o.push(n.elt("Escape",r,2+r++)),vc(a))break}return-1}}const BO={defineNodes:[{name:"Superscript",style:Ko.special(Ko.content)},{name:"SuperscriptMark",style:Ko.processingInstruction}],parseInline:[{name:"Superscript",parse:jO(94,"Superscript","SuperscriptMark")}]},IO={defineNodes:[{name:"Subscript",style:Ko.special(Ko.content)},{name:"SubscriptMark",style:Ko.processingInstruction}],parseInline:[{name:"Subscript",parse:jO(126,"Subscript","SubscriptMark")}]},GO={defineNodes:[{name:"Emoji",style:Ko.character}],parseInline:[{name:"Emoji",parse(t,e,i){let n;return 58==e&&(n=/^[a-zA-Z_0-9]+:/.exec(t.slice(i+1,t.end)))?t.addElement(t.elt("Emoji",i,i+1+n[0].length)):-1}}]};class NO{constructor(t,e,i,n,r,s,o,a,l,h=0,c){this.p=t,this.stack=e,this.state=i,this.reducePos=n,this.pos=r,this.score=s,this.buffer=o,this.bufferBase=a,this.curContext=l,this.lookAhead=h,this.parent=c}toString(){return`[${this.stack.filter((t,e)=>e%3==0).concat(this.state)}]@${this.pos}${this.score?"!"+this.score:""}`}static start(t,e,i=0){let n=t.parser.context;return new NO(t,[],e,i,i,0,[],0,n?new UO(n,n.start):null,0,null)}get context(){return this.curContext?this.curContext.context:null}pushState(t,e){this.stack.push(this.state,e,this.bufferBase+this.buffer.length),this.state=t}reduce(t){var e;let i=t>>19,n=65535&t,{parser:r}=this.p,s=this.reducePos<this.pos-25;s&&this.setLookAhead(this.pos);let o=r.dynamicPrecedence(n);if(o&&(this.score+=o),0==i)return this.pushState(r.getGoto(this.state,n,!0),this.reducePos),n<r.minRepeatTerm&&this.storeNode(n,this.reducePos,this.reducePos,s?8:4,!0),void this.reduceContext(n,this.reducePos);let a=this.stack.length-3*(i-1)-(262144&t?6:0),l=a?this.stack[a-2]:this.p.ranges[0].from,h=this.reducePos-l;h>=2e3&&!(null===(e=this.p.parser.nodeSet.types[n])||void 0===e?void 0:e.isAnonymous)&&(l==this.p.lastBigReductionStart?(this.p.bigReductionCount++,this.p.lastBigReductionSize=h):this.p.lastBigReductionSize<h&&(this.p.bigReductionCount=1,this.p.lastBigReductionStart=l,this.p.lastBigReductionSize=h));let c=a?this.stack[a-1]:0,O=this.bufferBase+this.buffer.length-c;if(n<r.minRepeatTerm||131072&t){let t=r.stateFlag(this.state,1)?this.pos:this.reducePos;this.storeNode(n,l,t,O+4,!0)}if(262144&t)this.state=this.stack[a];else{let t=this.stack[a-3];this.state=r.getGoto(t,n,!0)}for(;this.stack.length>a;)this.stack.pop();this.reduceContext(n,l)}storeNode(t,e,i,n=4,r=!1){if(0==t&&(!this.stack.length||this.stack[this.stack.length-1]<this.buffer.length+this.bufferBase)){let t=this,n=this.buffer.length;if(0==n&&t.parent&&(n=t.bufferBase-t.parent.bufferBase,t=t.parent),n>0&&0==t.buffer[n-4]&&t.buffer[n-1]>-1){if(e==i)return;if(t.buffer[n-2]>=e)return void(t.buffer[n-2]=i)}}if(r&&this.pos!=i){let r=this.buffer.length;if(r>0&&0!=this.buffer[r-4]){let t=!1;for(let e=r;e>0&&this.buffer[e-2]>i;e-=4)if(this.buffer[e-1]>=0){t=!0;break}if(t)for(;r>0&&this.buffer[r-2]>i;)this.buffer[r]=this.buffer[r-4],this.buffer[r+1]=this.buffer[r-3],this.buffer[r+2]=this.buffer[r-2],this.buffer[r+3]=this.buffer[r-1],r-=4,n>4&&(n-=4)}this.buffer[r]=t,this.buffer[r+1]=e,this.buffer[r+2]=i,this.buffer[r+3]=n}else this.buffer.push(t,e,i,n)}shift(t,e,i,n){if(131072&t)this.pushState(65535&t,this.pos);else if(262144&t)this.pos=n,this.shiftContext(e,i),e<=this.p.parser.maxNode&&this.buffer.push(e,i,n,4);else{let r=t,{parser:s}=this.p;(n>this.pos||e<=s.maxNode)&&(this.pos=n,s.stateFlag(r,1)||(this.reducePos=n)),this.pushState(r,i),this.shiftContext(e,i),e<=s.maxNode&&this.buffer.push(e,i,n,4)}}apply(t,e,i,n){65536&t?this.reduce(t):this.shift(t,e,i,n)}useNode(t,e){let i=this.p.reused.length-1;(i<0||this.p.reused[i]!=t)&&(this.p.reused.push(t),i++);let n=this.pos;this.reducePos=this.pos=n+t.length,this.pushState(e,n),this.buffer.push(i,n,this.reducePos,-1),this.curContext&&this.updateContext(this.curContext.tracker.reuse(this.curContext.context,t,this,this.p.stream.reset(this.pos-t.length)))}split(){let t=this,e=t.buffer.length;for(;e>0&&t.buffer[e-2]>t.reducePos;)e-=4;let i=t.buffer.slice(e),n=t.bufferBase+e;for(;t&&n==t.bufferBase;)t=t.parent;return new NO(this.p,this.stack.slice(),this.state,this.reducePos,this.pos,this.score,i,n,this.curContext,this.lookAhead,t)}recoverByDelete(t,e){let i=t<=this.p.parser.maxNode;i&&this.storeNode(t,this.pos,e,4),this.storeNode(0,this.pos,e,i?8:4),this.pos=this.reducePos=e,this.score-=190}canShift(t){for(let e=new HO(this);;){let i=this.p.parser.stateSlot(e.state,4)||this.p.parser.hasAction(e.state,t);if(0==i)return!1;if(!(65536&i))return!0;e.reduce(i)}}recoverByInsert(t){if(this.stack.length>=300)return[];let e=this.p.parser.nextStates(this.state);if(e.length>8||this.stack.length>=120){let i=[];for(let n,r=0;r<e.length;r+=2)(n=e[r+1])!=this.state&&this.p.parser.hasAction(n,t)&&i.push(e[r],n);if(this.stack.length<120)for(let t=0;i.length<8&&t<e.length;t+=2){let n=e[t+1];i.some((t,e)=>1&e&&t==n)||i.push(e[t],n)}e=i}let i=[];for(let t=0;t<e.length&&i.length<4;t+=2){let n=e[t+1];if(n==this.state)continue;let r=this.split();r.pushState(n,this.pos),r.storeNode(0,r.pos,r.pos,4,!0),r.shiftContext(e[t],this.pos),r.reducePos=this.pos,r.score-=200,i.push(r)}return i}forceReduce(){let{parser:t}=this.p,e=t.stateSlot(this.state,5);if(!(65536&e))return!1;if(!t.validAction(this.state,e)){let i=e>>19,n=65535&e,r=this.stack.length-3*i;if(r<0||t.getGoto(this.stack[r],n,!1)<0){let t=this.findForcedReduction();if(null==t)return!1;e=t}this.storeNode(0,this.pos,this.pos,4,!0),this.score-=100}return this.reducePos=this.pos,this.reduce(e),!0}findForcedReduction(){let{parser:t}=this.p,e=[],i=(n,r)=>{if(!e.includes(n))return e.push(n),t.allActions(n,e=>{if(393216&e);else if(65536&e){let i=(e>>19)-r;if(i>1){let n=65535&e,r=this.stack.length-3*i;if(r>=0&&t.getGoto(this.stack[r],n,!1)>=0)return i<<19|65536|n}}else{let t=i(e,r+1);if(null!=t)return t}})};return i(this.state,0)}forceAll(){for(;!this.p.parser.stateFlag(this.state,2);)if(!this.forceReduce()){this.storeNode(0,this.pos,this.pos,4,!0);break}return this}get deadEnd(){if(3!=this.stack.length)return!1;let{parser:t}=this.p;return 65535==t.data[t.stateSlot(this.state,1)]&&!t.stateSlot(this.state,4)}restart(){this.storeNode(0,this.pos,this.pos,4,!0),this.state=this.stack[0],this.stack.length=0}sameState(t){if(this.state!=t.state||this.stack.length!=t.stack.length)return!1;for(let e=0;e<this.stack.length;e+=3)if(this.stack[e]!=t.stack[e])return!1;return!0}get parser(){return this.p.parser}dialectEnabled(t){return this.p.parser.dialect.flags[t]}shiftContext(t,e){this.curContext&&this.updateContext(this.curContext.tracker.shift(this.curContext.context,t,this,this.p.stream.reset(e)))}reduceContext(t,e){this.curContext&&this.updateContext(this.curContext.tracker.reduce(this.curContext.context,t,this,this.p.stream.reset(e)))}emitContext(){let t=this.buffer.length-1;(t<0||-3!=this.buffer[t])&&this.buffer.push(this.curContext.hash,this.pos,this.pos,-3)}emitLookAhead(){let t=this.buffer.length-1;(t<0||-4!=this.buffer[t])&&this.buffer.push(this.lookAhead,this.pos,this.pos,-4)}updateContext(t){if(t!=this.curContext.context){let e=new UO(this.curContext.tracker,t);e.hash!=this.curContext.hash&&this.emitContext(),this.curContext=e}}setLookAhead(t){t>this.lookAhead&&(this.emitLookAhead(),this.lookAhead=t)}close(){this.curContext&&this.curContext.tracker.strict&&this.emitContext(),this.lookAhead>0&&this.emitLookAhead()}}class UO{constructor(t,e){this.tracker=t,this.context=e,this.hash=t.strict?t.hash(e):0}}class HO{constructor(t){this.start=t,this.state=t.state,this.stack=t.stack,this.base=this.stack.length}reduce(t){let e=65535&t,i=t>>19;0==i?(this.stack==this.start.stack&&(this.stack=this.stack.slice()),this.stack.push(this.state,0,0),this.base+=3):this.base-=3*(i-1);let n=this.start.p.parser.getGoto(this.stack[this.base-3],e,!0);this.state=n}}class FO{constructor(t,e,i){this.stack=t,this.pos=e,this.index=i,this.buffer=t.buffer,0==this.index&&this.maybeNext()}static create(t,e=t.bufferBase+t.buffer.length){return new FO(t,e,e-t.bufferBase)}maybeNext(){let t=this.stack.parent;null!=t&&(this.index=this.stack.bufferBase-t.bufferBase,this.stack=t,this.buffer=t.buffer)}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}next(){this.index-=4,this.pos-=4,0==this.index&&this.maybeNext()}fork(){return new FO(this.stack,this.pos,this.index)}}function KO(t,e=Uint16Array){if("string"!=typeof t)return t;let i=null;for(let n=0,r=0;n<t.length;){let s=0;for(;;){let e=t.charCodeAt(n++),i=!1;if(126==e){s=65535;break}e>=92&&e--,e>=34&&e--;let r=e-32;if(r>=46&&(r-=46,i=!0),s+=r,i)break;s*=46}i?i[r++]=s:i=new e(s)}return i}class JO{constructor(){this.start=-1,this.value=-1,this.end=-1,this.extended=-1,this.lookAhead=0,this.mask=0,this.context=0}}const tu=new JO;class eu{constructor(t,e){this.input=t,this.ranges=e,this.chunk="",this.chunkOff=0,this.chunk2="",this.chunk2Pos=0,this.next=-1,this.token=tu,this.rangeIndex=0,this.pos=this.chunkPos=e[0].from,this.range=e[0],this.end=e[e.length-1].to,this.readNext()}resolveOffset(t,e){let i=this.range,n=this.rangeIndex,r=this.pos+t;for(;r<i.from;){if(!n)return null;let t=this.ranges[--n];r-=i.from-t.to,i=t}for(;e<0?r>i.to:r>=i.to;){if(n==this.ranges.length-1)return null;let t=this.ranges[++n];r+=t.from-i.to,i=t}return r}clipPos(t){if(t>=this.range.from&&t<this.range.to)return t;for(let e of this.ranges)if(e.to>t)return Math.max(t,e.from);return this.end}peek(t){let e,i,n=this.chunkOff+t;if(n>=0&&n<this.chunk.length)e=this.pos+t,i=this.chunk.charCodeAt(n);else{let n=this.resolveOffset(t,1);if(null==n)return-1;if(e=n,e>=this.chunk2Pos&&e<this.chunk2Pos+this.chunk2.length)i=this.chunk2.charCodeAt(e-this.chunk2Pos);else{let t=this.rangeIndex,n=this.range;for(;n.to<=e;)n=this.ranges[++t];this.chunk2=this.input.chunk(this.chunk2Pos=e),e+this.chunk2.length>n.to&&(this.chunk2=this.chunk2.slice(0,n.to-e)),i=this.chunk2.charCodeAt(0)}}return e>=this.token.lookAhead&&(this.token.lookAhead=e+1),i}acceptToken(t,e=0){let i=e?this.resolveOffset(e,-1):this.pos;if(null==i||i<this.token.start)throw new RangeError("Token end out of bounds");this.token.value=t,this.token.end=i}acceptTokenTo(t,e){this.token.value=t,this.token.end=e}getChunk(){if(this.pos>=this.chunk2Pos&&this.pos<this.chunk2Pos+this.chunk2.length){let{chunk:t,chunkPos:e}=this;this.chunk=this.chunk2,this.chunkPos=this.chunk2Pos,this.chunk2=t,this.chunk2Pos=e,this.chunkOff=this.pos-this.chunkPos}else{this.chunk2=this.chunk,this.chunk2Pos=this.chunkPos;let t=this.input.chunk(this.pos),e=this.pos+t.length;this.chunk=e>this.range.to?t.slice(0,this.range.to-this.pos):t,this.chunkPos=this.pos,this.chunkOff=0}}readNext(){return this.chunkOff>=this.chunk.length&&(this.getChunk(),this.chunkOff==this.chunk.length)?this.next=-1:this.next=this.chunk.charCodeAt(this.chunkOff)}advance(t=1){for(this.chunkOff+=t;this.pos+t>=this.range.to;){if(this.rangeIndex==this.ranges.length-1)return this.setDone();t-=this.range.to-this.pos,this.range=this.ranges[++this.rangeIndex],this.pos=this.range.from}return this.pos+=t,this.pos>=this.token.lookAhead&&(this.token.lookAhead=this.pos+1),this.readNext()}setDone(){return this.pos=this.chunkPos=this.end,this.range=this.ranges[this.rangeIndex=this.ranges.length-1],this.chunk="",this.next=-1}reset(t,e){if(e?(this.token=e,e.start=t,e.lookAhead=t+1,e.value=e.extended=-1):this.token=tu,this.pos!=t){if(this.pos=t,t==this.end)return this.setDone(),this;for(;t<this.range.from;)this.range=this.ranges[--this.rangeIndex];for(;t>=this.range.to;)this.range=this.ranges[++this.rangeIndex];t>=this.chunkPos&&t<this.chunkPos+this.chunk.length?this.chunkOff=t-this.chunkPos:(this.chunk="",this.chunkOff=0),this.readNext()}return this}read(t,e){if(t>=this.chunkPos&&e<=this.chunkPos+this.chunk.length)return this.chunk.slice(t-this.chunkPos,e-this.chunkPos);if(t>=this.chunk2Pos&&e<=this.chunk2Pos+this.chunk2.length)return this.chunk2.slice(t-this.chunk2Pos,e-this.chunk2Pos);if(t>=this.range.from&&e<=this.range.to)return this.input.read(t,e);let i="";for(let n of this.ranges){if(n.from>=e)break;n.to>t&&(i+=this.input.read(Math.max(n.from,t),Math.min(n.to,e)))}return i}}class iu{constructor(t,e){this.data=t,this.id=e}token(t,e){let{parser:i}=e.p;su(this.data,t,e,this.id,i.data,i.tokenPrecTable)}}iu.prototype.contextual=iu.prototype.fallback=iu.prototype.extend=!1;class nu{constructor(t,e,i){this.precTable=e,this.elseToken=i,this.data="string"==typeof t?KO(t):t}token(t,e){let i=t.pos,n=0;for(;;){let i=t.next<0,r=t.resolveOffset(1,1);if(su(this.data,t,e,0,this.data,this.precTable),t.token.value>-1)break;if(null==this.elseToken)return;if(i||n++,null==r)break;t.reset(r,t.token)}n&&(t.reset(i,t.token),t.acceptToken(this.elseToken,n))}}nu.prototype.contextual=iu.prototype.fallback=iu.prototype.extend=!1;class ru{constructor(t,e={}){this.token=t,this.contextual=!!e.contextual,this.fallback=!!e.fallback,this.extend=!!e.extend}}function su(t,e,i,n,r,s){let o=0,a=1<<n,{dialect:l}=i.p.parser;t:for(;0!=(a&t[o]);){let i=t[o+1];for(let n=o+3;n<i;n+=2)if((t[n+1]&a)>0){let i=t[n];if(l.allows(i)&&(-1==e.token.value||e.token.value==i||au(i,e.token.value,r,s))){e.acceptToken(i);break}}let n=e.next,h=0,c=t[o+2];if(!(e.next<0&&c>h&&65535==t[i+3*c-3])){for(;h<c;){let r=h+c>>1,s=i+r+(r<<1),a=t[s],l=t[s+1]||65536;if(n<a)c=r;else{if(!(n>=l)){o=t[s+2],e.advance();continue t}h=r+1}}break}o=t[i+3*c-1]}}function ou(t,e,i){for(let n,r=e;65535!=(n=t[r]);r++)if(n==i)return r-e;return-1}function au(t,e,i,n){let r=ou(i,n,e);return r<0||ou(i,n,t)<r}const lu="undefined"!=typeof process&&process.env&&/\bparse\b/.test(process.env.LOG);let hu=null;function cu(t,e,i){let n=t.cursor(Ws.IncludeAnonymous);for(n.moveTo(e);;)if(!(i<0?n.childBefore(e):n.childAfter(e)))for(;;){if((i<0?n.to<e:n.from>e)&&!n.type.isError)return i<0?Math.max(0,Math.min(n.to-1,e-25)):Math.min(t.length,Math.max(n.from+1,e+25));if(i<0?n.prevSibling():n.nextSibling())break;if(!n.parent())return i<0?0:t.length}}class Ou{constructor(t,e){this.fragments=t,this.nodeSet=e,this.i=0,this.fragment=null,this.safeFrom=-1,this.safeTo=-1,this.trees=[],this.start=[],this.index=[],this.nextFragment()}nextFragment(){let t=this.fragment=this.i==this.fragments.length?null:this.fragments[this.i++];if(t){for(this.safeFrom=t.openStart?cu(t.tree,t.from+t.offset,1)-t.offset:t.from,this.safeTo=t.openEnd?cu(t.tree,t.to+t.offset,-1)-t.offset:t.to;this.trees.length;)this.trees.pop(),this.start.pop(),this.index.pop();this.trees.push(t.tree),this.start.push(-t.offset),this.index.push(0),this.nextStart=this.safeFrom}else this.nextStart=1e9}nodeAt(t){if(t<this.nextStart)return null;for(;this.fragment&&this.safeTo<=t;)this.nextFragment();if(!this.fragment)return null;for(;;){let e=this.trees.length-1;if(e<0)return this.nextFragment(),null;let i=this.trees[e],n=this.index[e];if(n==i.children.length){this.trees.pop(),this.start.pop(),this.index.pop();continue}let r=i.children[n],s=this.start[e]+i.positions[n];if(s>t)return this.nextStart=s,null;if(r instanceof js){if(s==t){if(s<this.safeFrom)return null;let t=s+r.length;if(t<=this.safeTo){let e=r.prop(zs.lookAhead);if(!e||t+e<this.fragment.to)return r}}this.index[e]++,s+r.length>=Math.max(this.safeFrom,t)&&(this.trees.push(r),this.start.push(s),this.index.push(0))}else this.index[e]++,this.nextStart=s+r.length}}}class uu{constructor(t,e){this.stream=e,this.tokens=[],this.mainToken=null,this.actions=[],this.tokens=t.tokenizers.map(t=>new JO)}getActions(t){let e=0,i=null,{parser:n}=t.p,{tokenizers:r}=n,s=n.stateSlot(t.state,3),o=t.curContext?t.curContext.hash:0,a=0;for(let n=0;n<r.length;n++){if(!(1<<n&s))continue;let l=r[n],h=this.tokens[n];if((!i||l.fallback)&&((l.contextual||h.start!=t.pos||h.mask!=s||h.context!=o)&&(this.updateCachedToken(h,l,t),h.mask=s,h.context=o),h.lookAhead>h.end+25&&(a=Math.max(h.lookAhead,a)),0!=h.value)){let n=e;if(h.extended>-1&&(e=this.addActions(t,h.extended,h.end,e)),e=this.addActions(t,h.value,h.end,e),!l.extend&&(i=h,e>n))break}}for(;this.actions.length>e;)this.actions.pop();return a&&t.setLookAhead(a),i||t.pos!=this.stream.end||(i=new JO,i.value=t.p.parser.eofTerm,i.start=i.end=t.pos,e=this.addActions(t,i.value,i.end,e)),this.mainToken=i,this.actions}getMainToken(t){if(this.mainToken)return this.mainToken;let e=new JO,{pos:i,p:n}=t;return e.start=i,e.end=Math.min(i+1,n.stream.end),e.value=i==n.stream.end?n.parser.eofTerm:0,e}updateCachedToken(t,e,i){let n=this.stream.clipPos(i.pos);if(e.token(this.stream.reset(n,t),i),t.value>-1){let{parser:e}=i.p;for(let n=0;n<e.specialized.length;n++)if(e.specialized[n]==t.value){let r=e.specializers[n](this.stream.read(t.start,t.end),i);if(r>=0&&i.p.parser.dialect.allows(r>>1)){1&r?t.extended=r>>1:t.value=r>>1;break}}}else t.value=0,t.end=this.stream.clipPos(n+1)}putAction(t,e,i,n){for(let e=0;e<n;e+=3)if(this.actions[e]==t)return n;return this.actions[n++]=t,this.actions[n++]=e,this.actions[n++]=i,n}addActions(t,e,i,n){let{state:r}=t,{parser:s}=t.p,{data:o}=s;for(let t=0;t<2;t++)for(let a=s.stateSlot(r,t?2:1);;a+=3){if(65535==o[a]){if(1!=o[a+1]){0==n&&2==o[a+1]&&(n=this.putAction(Su(o,a+2),e,i,n));break}a=Su(o,a+2)}o[a]==e&&(n=this.putAction(Su(o,a+1),e,i,n))}return n}}class du{constructor(t,e,i,n){this.parser=t,this.input=e,this.ranges=n,this.recovering=0,this.nextStackID=9812,this.minStackPos=0,this.reused=[],this.stoppedAt=null,this.lastBigReductionStart=-1,this.lastBigReductionSize=0,this.bigReductionCount=0,this.stream=new eu(e,n),this.tokens=new uu(t,this.stream),this.topTerm=t.top[1];let{from:r}=n[0];this.stacks=[NO.start(this,t.top[0],r)],this.fragments=i.length&&this.stream.end-r>4*t.bufferLength?new Ou(i,t.nodeSet):null}get parsedPos(){return this.minStackPos}advance(){let t,e,i=this.stacks,n=this.minStackPos,r=this.stacks=[];if(this.bigReductionCount>300&&1==i.length){let[t]=i;for(;t.forceReduce()&&t.stack.length&&t.stack[t.stack.length-2]>=this.lastBigReductionStart;);this.bigReductionCount=this.lastBigReductionSize=0}for(let s=0;s<i.length;s++){let o=i[s];for(;;){if(this.tokens.mainToken=null,o.pos>n)r.push(o);else{if(this.advanceStack(o,r,i))continue;{t||(t=[],e=[]),t.push(o);let i=this.tokens.getMainToken(o);e.push(i.value,i.end)}}break}}if(!r.length){let e=t&&function(t){let e=null;for(let i of t){let t=i.p.stoppedAt;(i.pos==i.p.stream.end||null!=t&&i.pos>t)&&i.p.parser.stateFlag(i.state,2)&&(!e||e.score<i.score)&&(e=i)}return e}(t);if(e)return lu&&console.log("Finish with "+this.stackID(e)),this.stackToTree(e);if(this.parser.strict)throw lu&&t&&console.log("Stuck with token "+(this.tokens.mainToken?this.parser.getName(this.tokens.mainToken.value):"none")),new SyntaxError("No parse at "+n);this.recovering||(this.recovering=5)}if(this.recovering&&t){let i=null!=this.stoppedAt&&t[0].pos>this.stoppedAt?t[0]:this.runRecovery(t,e,r);if(i)return lu&&console.log("Force-finish "+this.stackID(i)),this.stackToTree(i.forceAll())}if(this.recovering){let t=1==this.recovering?1:3*this.recovering;if(r.length>t)for(r.sort((t,e)=>e.score-t.score);r.length>t;)r.pop();r.some(t=>t.reducePos>n)&&this.recovering--}else if(r.length>1){t:for(let t=0;t<r.length-1;t++){let e=r[t];for(let i=t+1;i<r.length;i++){let n=r[i];if(e.sameState(n)||e.buffer.length>500&&n.buffer.length>500){if(!((e.score-n.score||e.buffer.length-n.buffer.length)>0)){r.splice(t--,1);continue t}r.splice(i--,1)}}}r.length>12&&r.splice(12,r.length-12)}this.minStackPos=r[0].pos;for(let t=1;t<r.length;t++)r[t].pos<this.minStackPos&&(this.minStackPos=r[t].pos);return null}stopAt(t){if(null!=this.stoppedAt&&this.stoppedAt<t)throw new RangeError("Can't move stoppedAt forward");this.stoppedAt=t}advanceStack(t,e,i){let n=t.pos,{parser:r}=this,s=lu?this.stackID(t)+" -> ":"";if(null!=this.stoppedAt&&n>this.stoppedAt)return t.forceReduce()?t:null;if(this.fragments){let e=t.curContext&&t.curContext.tracker.strict,i=e?t.curContext.hash:0;for(let o=this.fragments.nodeAt(n);o;){let n=this.parser.nodeSet.types[o.type.id]==o.type?r.getGoto(t.state,o.type.id):-1;if(n>-1&&o.length&&(!e||(o.prop(zs.contextHash)||0)==i))return t.useNode(o,n),lu&&console.log(s+this.stackID(t)+` (via reuse of ${r.getName(o.type.id)})`),!0;if(!(o instanceof js)||0==o.children.length||o.positions[0]>0)break;let a=o.children[0];if(!(a instanceof js&&0==o.positions[0]))break;o=a}}let o=r.stateSlot(t.state,4);if(o>0)return t.reduce(o),lu&&console.log(s+this.stackID(t)+` (via always-reduce ${r.getName(65535&o)})`),!0;if(t.stack.length>=8400)for(;t.stack.length>6e3&&t.forceReduce(););let a=this.tokens.getActions(t);for(let o=0;o<a.length;){let l=a[o++],h=a[o++],c=a[o++],O=o==a.length||!i,u=O?t:t.split(),d=this.tokens.mainToken;if(u.apply(l,h,d?d.start:u.pos,c),lu&&console.log(s+this.stackID(u)+` (via ${65536&l?`reduce of ${r.getName(65535&l)}`:"shift"} for ${r.getName(h)} @ ${n}${u==t?"":", split"})`),O)return!0;u.pos>n?e.push(u):i.push(u)}return!1}advanceFully(t,e){let i=t.pos;for(;;){if(!this.advanceStack(t,null,null))return!1;if(t.pos>i)return fu(t,e),!0}}runRecovery(t,e,i){let n=null,r=!1;for(let s=0;s<t.length;s++){let o=t[s],a=e[s<<1],l=e[1+(s<<1)],h=lu?this.stackID(o)+" -> ":"";if(o.deadEnd){if(r)continue;if(r=!0,o.restart(),lu&&console.log(h+this.stackID(o)+" (restarted)"),this.advanceFully(o,i))continue}let c=o.split(),O=h;for(let t=0;c.forceReduce()&&t<10&&(lu&&console.log(O+this.stackID(c)+" (via force-reduce)"),!this.advanceFully(c,i));t++)lu&&(O=this.stackID(c)+" -> ");for(let t of o.recoverByInsert(a))lu&&console.log(h+this.stackID(t)+" (via recover-insert)"),this.advanceFully(t,i);this.stream.end>o.pos?(l==o.pos&&(l++,a=0),o.recoverByDelete(a,l),lu&&console.log(h+this.stackID(o)+` (via recover-delete ${this.parser.getName(a)})`),fu(o,i)):(!n||n.score<o.score)&&(n=o)}return n}stackToTree(t){return t.close(),js.build({buffer:FO.create(t),nodeSet:this.parser.nodeSet,topID:this.topTerm,maxBufferLength:this.parser.bufferLength,reused:this.reused,start:this.ranges[0].from,length:t.pos-this.ranges[0].from,minRepeatType:this.parser.minRepeatTerm})}stackID(t){let e=(hu||(hu=new WeakMap)).get(t);return e||hu.set(t,e=String.fromCodePoint(this.nextStackID++)),e+t}}function fu(t,e){for(let i=0;i<e.length;i++){let n=e[i];if(n.pos==t.pos&&n.sameState(t))return void(e[i].score<t.score&&(e[i]=t))}e.push(t)}class pu{constructor(t,e,i){this.source=t,this.flags=e,this.disabled=i}allows(t){return!this.disabled||0==this.disabled[t]}}const gu=t=>t;class mu{constructor(t){this.start=t.start,this.shift=t.shift||gu,this.reduce=t.reduce||gu,this.reuse=t.reuse||gu,this.hash=t.hash||(()=>0),this.strict=!1!==t.strict}}class Qu extends co{constructor(t){if(super(),this.wrappers=[],14!=t.version)throw new RangeError(`Parser version (${t.version}) doesn't match runtime version (14)`);let e=t.nodeNames.split(" ");this.minRepeatTerm=e.length;for(let i=0;i<t.repeatNodeCount;i++)e.push("");let i=Object.keys(t.topRules).map(e=>t.topRules[e][1]),n=[];for(let t=0;t<e.length;t++)n.push([]);function r(t,e,i){n[t].push([e,e.deserialize(String(i))])}if(t.nodeProps)for(let e of t.nodeProps){let t=e[0];"string"==typeof t&&(t=zs[t]);for(let i=1;i<e.length;){let n=e[i++];if(n>=0)r(n,t,e[i++]);else{let s=e[i+-n];for(let o=-n;o>0;o--)r(e[i++],t,s);i++}}}this.nodeSet=new qs(e.map((e,r)=>Vs.define({name:r>=this.minRepeatTerm?void 0:e,id:r,props:n[r],top:i.indexOf(r)>-1,error:0==r,skipped:t.skippedNodes&&t.skippedNodes.indexOf(r)>-1}))),t.propSources&&(this.nodeSet=this.nodeSet.extend(...t.propSources)),this.strict=!1,this.bufferLength=Ms;let s=KO(t.tokenData);this.context=t.context,this.specializerSpecs=t.specialized||[],this.specialized=new Uint16Array(this.specializerSpecs.length);for(let t=0;t<this.specializerSpecs.length;t++)this.specialized[t]=this.specializerSpecs[t].term;this.specializers=this.specializerSpecs.map(xu),this.states=KO(t.states,Uint32Array),this.data=KO(t.stateData),this.goto=KO(t.goto),this.maxTerm=t.maxTerm,this.tokenizers=t.tokenizers.map(t=>"number"==typeof t?new iu(s,t):t),this.topRules=t.topRules,this.dialects=t.dialects||{},this.dynamicPrecedences=t.dynamicPrecedences||null,this.tokenPrecTable=t.tokenPrec,this.termNames=t.termNames||null,this.maxNode=this.nodeSet.types.length-1,this.dialect=this.parseDialect(),this.top=this.topRules[Object.keys(this.topRules)[0]]}createParse(t,e,i){let n=new du(this,t,e,i);for(let r of this.wrappers)n=r(n,t,e,i);return n}getGoto(t,e,i=!1){let n=this.goto;if(e>=n[0])return-1;for(let r=n[e+1];;){let e=n[r++],s=1&e,o=n[r++];if(s&&i)return o;for(let i=r+(e>>1);r<i;r++)if(n[r]==t)return o;if(s)return-1}}hasAction(t,e){let i=this.data;for(let n=0;n<2;n++)for(let r,s=this.stateSlot(t,n?2:1);;s+=3){if(65535==(r=i[s])){if(1!=i[s+1]){if(2==i[s+1])return Su(i,s+2);break}r=i[s=Su(i,s+2)]}if(r==e||0==r)return Su(i,s+1)}return 0}stateSlot(t,e){return this.states[6*t+e]}stateFlag(t,e){return(this.stateSlot(t,0)&e)>0}validAction(t,e){return!!this.allActions(t,t=>t==e||null)}allActions(t,e){let i=this.stateSlot(t,4),n=i?e(i):void 0;for(let i=this.stateSlot(t,1);null==n;i+=3){if(65535==this.data[i]){if(1!=this.data[i+1])break;i=Su(this.data,i+2)}n=e(Su(this.data,i+1))}return n}nextStates(t){let e=[];for(let i=this.stateSlot(t,1);;i+=3){if(65535==this.data[i]){if(1!=this.data[i+1])break;i=Su(this.data,i+2)}if(!(1&this.data[i+2])){let t=this.data[i+1];e.some((e,i)=>1&i&&e==t)||e.push(this.data[i],t)}}return e}configure(t){let e=Object.assign(Object.create(Qu.prototype),this);if(t.props&&(e.nodeSet=this.nodeSet.extend(...t.props)),t.top){let i=this.topRules[t.top];if(!i)throw new RangeError(`Invalid top rule name ${t.top}`);e.top=i}return t.tokenizers&&(e.tokenizers=this.tokenizers.map(e=>{let i=t.tokenizers.find(t=>t.from==e);return i?i.to:e})),t.specializers&&(e.specializers=this.specializers.slice(),e.specializerSpecs=this.specializerSpecs.map((i,n)=>{let r=t.specializers.find(t=>t.from==i.external);if(!r)return i;let s=Object.assign(Object.assign({},i),{external:r.to});return e.specializers[n]=xu(s),s})),t.contextTracker&&(e.context=t.contextTracker),t.dialect&&(e.dialect=this.parseDialect(t.dialect)),null!=t.strict&&(e.strict=t.strict),t.wrap&&(e.wrappers=e.wrappers.concat(t.wrap)),null!=t.bufferLength&&(e.bufferLength=t.bufferLength),e}hasWrappers(){return this.wrappers.length>0}getName(t){return this.termNames?this.termNames[t]:String(t<=this.maxNode&&this.nodeSet.types[t].name||t)}get eofTerm(){return this.maxNode+1}get topNode(){return this.nodeSet.types[this.top[1]]}dynamicPrecedence(t){let e=this.dynamicPrecedences;return null==e?0:e[t]||0}parseDialect(t){let e=Object.keys(this.dialects),i=e.map(()=>!1);if(t)for(let n of t.split(" ")){let t=e.indexOf(n);t>=0&&(i[t]=!0)}let n=null;for(let t=0;t<e.length;t++)if(!i[t])for(let i,r=this.dialects[e[t]];65535!=(i=this.data[r++]);)(n||(n=new Uint8Array(this.maxTerm+1)))[i]=1;return new pu(t,i,n)}static deserialize(t){return new Qu(t)}}function Su(t,e){return t[e]|t[e+1]<<16}function xu(t){if(t.external){let e=t.extend?1:0;return(i,n)=>t.external(i,n)<<1|e}return t.get}const yu={area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},wu={dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},bu={dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}};function ku(t){return 45==t||46==t||58==t||t>=65&&t<=90||95==t||t>=97&&t<=122||t>=161}let vu=null,$u=null,Pu=0;function Tu(t,e){let i=t.pos+e;if(Pu==i&&$u==t)return vu;let n=t.peek(e),r="";for(;ku(n);)r+=String.fromCharCode(n),n=t.peek(++e);return $u=t,Pu=i,vu=r?r.toLowerCase():n==Zu||n==Xu?void 0:null}const Zu=63,Xu=33;function Au(t,e){this.name=t,this.parent=e}const Cu=[6,10,7,8,9],Mu=new mu({start:null,shift:(t,e,i,n)=>Cu.indexOf(e)>-1?new Au(Tu(n,1)||"",t):t,reduce:(t,e)=>21==e&&t?t.parent:t,reuse(t,e,i,n){let r=e.type.id;return 6==r||37==r?new Au(Tu(n,1)||"",t):t},strict:!1}),Ru=new ru((t,e)=>{if(60!=t.next)return void(t.next<0&&e.context&&t.acceptToken(58));t.advance();let i=47==t.next;i&&t.advance();let n=Tu(t,0);if(void 0===n)return;if(!n)return t.acceptToken(i?15:14);let r=e.context?e.context.name:null;if(i){if(n==r)return t.acceptToken(11);if(r&&wu[r])return t.acceptToken(58,-2);if(e.dialectEnabled(0))return t.acceptToken(12);for(let t=e.context;t;t=t.parent)if(t.name==n)return;t.acceptToken(13)}else{if("script"==n)return t.acceptToken(7);if("style"==n)return t.acceptToken(8);if("textarea"==n)return t.acceptToken(9);if(yu.hasOwnProperty(n))return t.acceptToken(10);r&&bu[r]&&bu[r][n]?t.acceptToken(58,-1):t.acceptToken(6)}},{contextual:!0}),_u=new ru(t=>{for(let e=0,i=0;;i++){if(t.next<0){i&&t.acceptToken(59);break}if(45==t.next)e++;else{if(62==t.next&&e>=2){i>=3&&t.acceptToken(59,-2);break}e=0}t.advance()}}),zu=new ru((t,e)=>{if(47==t.next&&62==t.peek(1)){let i=e.dialectEnabled(1)||function(t){for(;t;t=t.parent)if("svg"==t.name||"math"==t.name)return!0;return!1}(e.context);t.acceptToken(i?5:4,2)}else 62==t.next&&t.acceptToken(4,1)});function Yu(t,e,i){let n=2+t.length;return new ru(r=>{for(let s=0,o=0,a=0;;a++){if(r.next<0){a&&r.acceptToken(e);break}if(0==s&&60==r.next||1==s&&47==r.next||s>=2&&s<n&&r.next==t.charCodeAt(s-2))s++,o++;else{if(s==n&&62==r.next){a>o?r.acceptToken(e,-o):r.acceptToken(i,-(o-2));break}if((10==r.next||13==r.next)&&a){r.acceptToken(e,1);break}s=o=0}r.advance()}})}const Eu=Yu("script",55,1),Vu=Yu("style",56,2),qu=Yu("textarea",57,3),Lu=Ao({"Text RawText IncompleteTag IncompleteCloseTag":Ko.content,"StartTag StartCloseTag SelfClosingEndTag EndTag":Ko.angleBracket,TagName:Ko.tagName,"MismatchedCloseTag/TagName":[Ko.tagName,Ko.invalid],AttributeName:Ko.attributeName,"AttributeValue UnquotedAttributeValue":Ko.attributeValue,Is:Ko.definitionOperator,"EntityReference CharacterReference":Ko.character,Comment:Ko.blockComment,ProcessingInst:Ko.processingInstruction,DoctypeDecl:Ko.documentMeta}),Du=Qu.deserialize({version:14,states:",xOVO!rOOO!ZQ#tO'#CrO!`Q#tO'#C{O!eQ#tO'#DOO!jQ#tO'#DRO!oQ#tO'#DTO!tOaO'#CqO#PObO'#CqO#[OdO'#CqO$kO!rO'#CqOOO`'#Cq'#CqO$rO$fO'#DUO$zQ#tO'#DWO%PQ#tO'#DXOOO`'#Dl'#DlOOO`'#DZ'#DZQVO!rOOO%UQ&rO,59^O%aQ&rO,59gO%lQ&rO,59jO%wQ&rO,59mO&SQ&rO,59oOOOa'#D_'#D_O&_OaO'#CyO&jOaO,59]OOOb'#D`'#D`O&rObO'#C|O&}ObO,59]OOOd'#Da'#DaO'VOdO'#DPO'bOdO,59]OOO`'#Db'#DbO'jO!rO,59]O'qQ#tO'#DSOOO`,59],59]OOOp'#Dc'#DcO'vO$fO,59pOOO`,59p,59pO(OQ#|O,59rO(TQ#|O,59sOOO`-E7X-E7XO(YQ&rO'#CtOOQW'#D['#D[O(hQ&rO1G.xOOOa1G.x1G.xOOO`1G/Z1G/ZO(sQ&rO1G/ROOOb1G/R1G/RO)OQ&rO1G/UOOOd1G/U1G/UO)ZQ&rO1G/XOOO`1G/X1G/XO)fQ&rO1G/ZOOOa-E7]-E7]O)qQ#tO'#CzOOO`1G.w1G.wOOOb-E7^-E7^O)vQ#tO'#C}OOOd-E7_-E7_O){Q#tO'#DQOOO`-E7`-E7`O*QQ#|O,59nOOOp-E7a-E7aOOO`1G/[1G/[OOO`1G/^1G/^OOO`1G/_1G/_O*VQ,UO,59`OOQW-E7Y-E7YOOOa7+$d7+$dOOO`7+$u7+$uOOOb7+$m7+$mOOOd7+$p7+$pOOO`7+$s7+$sO*bQ#|O,59fO*gQ#|O,59iO*lQ#|O,59lOOO`1G/Y1G/YO*qO7[O'#CwO+SOMhO'#CwOOQW1G.z1G.zOOO`1G/Q1G/QOOO`1G/T1G/TOOO`1G/W1G/WOOOO'#D]'#D]O+eO7[O,59cOOQW,59c,59cOOOO'#D^'#D^O+vOMhO,59cOOOO-E7Z-E7ZOOQW1G.}1G.}OOOO-E7[-E7[",stateData:",c~O!_OS~OUSOVPOWQOXROYTO[]O][O^^O_^Oa^Ob^Oc^Od^Oy^O|_O!eZO~OgaO~OgbO~OgcO~OgdO~OgeO~O!XfOPmP![mP~O!YiOQpP![pP~O!ZlORsP![sP~OUSOVPOWQOXROYTOZqO[]O][O^^O_^Oa^Ob^Oc^Od^Oy^O!eZO~O![rO~P#gO!]sO!fuO~OgvO~OgwO~OS|OT}OiyO~OS!POT}OiyO~OS!ROT}OiyO~OS!TOT}OiyO~OS}OT}OiyO~O!XfOPmX![mX~OP!WO![!XO~O!YiOQpX![pX~OQ!ZO![!XO~O!ZlORsX![sX~OR!]O![!XO~O![!XO~P#gOg!_O~O!]sO!f!aO~OS!bO~OS!cO~Oj!dOShXThXihX~OS!fOT!gOiyO~OS!hOT!gOiyO~OS!iOT!gOiyO~OS!jOT!gOiyO~OS!gOT!gOiyO~Og!kO~Og!lO~Og!mO~OS!nO~Ol!qO!a!oO!c!pO~OS!rO~OS!sO~OS!tO~Ob!uOc!uOd!uO!a!wO!b!uO~Ob!xOc!xOd!xO!c!wO!d!xO~Ob!uOc!uOd!uO!a!{O!b!uO~Ob!xOc!xOd!xO!c!{O!d!xO~OT~cbd!ey|!e~",goto:"%q!aPPPPPPPPPPPPPPPPPPPPP!b!hP!nPP!zP!}#Q#T#Z#^#a#g#j#m#s#y!bP!b!bP$P$V$m$s$y%P%V%]%cPPPPPPPP%iX^OX`pXUOX`pezabcde{!O!Q!S!UR!q!dRhUR!XhXVOX`pRkVR!XkXWOX`pRnWR!XnXXOX`pQrXR!XpXYOX`pQ`ORx`Q{aQ!ObQ!QcQ!SdQ!UeZ!e{!O!Q!S!UQ!v!oR!z!vQ!y!pR!|!yQgUR!VgQjVR!YjQmWR![mQpXR!^pQtZR!`tS_O`ToXp",nodeNames:"⚠ StartCloseTag StartCloseTag StartCloseTag EndTag SelfClosingEndTag StartTag StartTag StartTag StartTag StartTag StartCloseTag StartCloseTag StartCloseTag IncompleteTag IncompleteCloseTag Document Text EntityReference CharacterReference InvalidEntity Element OpenTag TagName Attribute AttributeName Is AttributeValue UnquotedAttributeValue ScriptText CloseTag OpenTag StyleText CloseTag OpenTag TextareaText CloseTag OpenTag CloseTag SelfClosingTag Comment ProcessingInst MismatchedCloseTag CloseTag DoctypeDecl",maxTerm:68,context:Mu,nodeProps:[["closedBy",-10,1,2,3,7,8,9,10,11,12,13,"EndTag",6,"EndTag SelfClosingEndTag",-4,22,31,34,37,"CloseTag"],["openedBy",4,"StartTag StartCloseTag",5,"StartTag",-4,30,33,36,38,"OpenTag"],["group",-10,14,15,18,19,20,21,40,41,42,43,"Entity",17,"Entity TextContent",-3,29,32,35,"TextContent Entity"],["isolate",-11,22,30,31,33,34,36,37,38,39,42,43,"ltr",-3,27,28,40,""]],propSources:[Lu],skippedNodes:[0],repeatNodeCount:9,tokenData:"!<p!aR!YOX$qXY,QYZ,QZ[$q[]&X]^,Q^p$qpq,Qqr-_rs3_sv-_vw3}wxHYx}-_}!OH{!O!P-_!P!Q$q!Q![-_![!]Mz!]!^-_!^!_!$S!_!`!;x!`!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4U-_4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!Z$|caPlW!b`!dpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr$qrs&}sv$qvw+Pwx(tx!^$q!^!_*V!_!a&X!a#S$q#S#T&X#T;'S$q;'S;=`+z<%lO$q!R&bXaP!b`!dpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&Xq'UVaP!dpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}P'pTaPOv'kw!^'k!_;'S'k;'S;=`(P<%lO'kP(SP;=`<%l'kp([S!dpOv(Vx;'S(V;'S;=`(h<%lO(Vp(kP;=`<%l(Vq(qP;=`<%l&}a({WaP!b`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t`)jT!b`Or)esv)ew;'S)e;'S;=`)y<%lO)e`)|P;=`<%l)ea*SP;=`<%l(t!Q*^V!b`!dpOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!Q*vP;=`<%l*V!R*|P;=`<%l&XW+UYlWOX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+PW+wP;=`<%l+P!Z+}P;=`<%l$q!a,]`aP!b`!dp!_^OX&XXY,QYZ,QZ]&X]^,Q^p&Xpq,Qqr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!_-ljiSaPlW!b`!dpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q[/ebiSlWOX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+PS0rXiSqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0mS1bP;=`<%l0m[1hP;=`<%l/^!V1vciSaP!b`!dpOq&Xqr1krs&}sv1kvw0mwx(tx!P1k!P!Q&X!Q!^1k!^!_*V!_!a&X!a#s1k#s$f&X$f;'S1k;'S;=`3R<%l?Ah1k?Ah?BY&X?BY?Mn1k?MnO&X!V3UP;=`<%l1k!_3[P;=`<%l-_!Z3hV!ahaP!dpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}!_4WiiSlWd!ROX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst>]tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^/^!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!Z5zblWOX5uXZ7SZ[5u[^7S^p5uqr5urs7Sst+Ptw5uwx7Sx!]5u!]!^7w!^!a7S!a#S5u#S#T7S#T;'S5u;'S;=`8n<%lO5u!R7VVOp7Sqs7St!]7S!]!^7l!^;'S7S;'S;=`7q<%lO7S!R7qOb!R!R7tP;=`<%l7S!Z8OYlWb!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!Z8qP;=`<%l5u!_8{iiSlWOX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst/^tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^:j!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!_:sbiSlWb!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!V<QciSOp7Sqr;{rs7Sst0mtw;{wx7Sx!P;{!P!Q7S!Q!];{!]!^=]!^!a7S!a#s;{#s$f7S$f;'S;{;'S;=`>P<%l?Ah;{?Ah?BY7S?BY?Mn;{?MnO7S!V=dXiSb!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!V>SP;=`<%l;{!_>YP;=`<%l8t!_>dhiSlWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^/^!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!Z@TalWOX@OXZAYZ[@O[^AY^p@Oqr@OrsAYsw@OwxAYx!]@O!]!^Az!^!aAY!a#S@O#S#TAY#T;'S@O;'S;=`Bq<%lO@O!RA]UOpAYq!]AY!]!^Ao!^;'SAY;'S;=`At<%lOAY!RAtOc!R!RAwP;=`<%lAY!ZBRYlWc!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!ZBtP;=`<%l@O!_COhiSlWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^Dj!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!_DsbiSlWc!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!VFQbiSOpAYqrE{rsAYswE{wxAYx!PE{!P!QAY!Q!]E{!]!^GY!^!aAY!a#sE{#s$fAY$f;'SE{;'S;=`G|<%l?AhE{?Ah?BYAY?BY?MnE{?MnOAY!VGaXiSc!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!VHPP;=`<%lE{!_HVP;=`<%lBw!ZHcW!cxaP!b`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t!aIYliSaPlW!b`!dpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OKQ!O!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!aK_kiSaPlW!b`!dpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!`&X!`!aMS!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!TM_XaP!b`!dp!fQOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!aNZ!ZiSgQaPlW!b`!dpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OMz!O!PMz!P!Q$q!Q![Mz![!]Mz!]!^-_!^!_*V!_!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f$}-_$}%OMz%O%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4UMz4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Je-_$Je$JgMz$Jg$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!a!$PP;=`<%lMz!R!$ZY!b`!dpOq*Vqr!$yrs(Vsv*Vwx)ex!a*V!a!b!4t!b;'S*V;'S;=`*s<%lO*V!R!%Q]!b`!dpOr*Vrs(Vsv*Vwx)ex}*V}!O!%y!O!f*V!f!g!']!g#W*V#W#X!0`#X;'S*V;'S;=`*s<%lO*V!R!&QX!b`!dpOr*Vrs(Vsv*Vwx)ex}*V}!O!&m!O;'S*V;'S;=`*s<%lO*V!R!&vV!b`!dp!ePOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!'dX!b`!dpOr*Vrs(Vsv*Vwx)ex!q*V!q!r!(P!r;'S*V;'S;=`*s<%lO*V!R!(WX!b`!dpOr*Vrs(Vsv*Vwx)ex!e*V!e!f!(s!f;'S*V;'S;=`*s<%lO*V!R!(zX!b`!dpOr*Vrs(Vsv*Vwx)ex!v*V!v!w!)g!w;'S*V;'S;=`*s<%lO*V!R!)nX!b`!dpOr*Vrs(Vsv*Vwx)ex!{*V!{!|!*Z!|;'S*V;'S;=`*s<%lO*V!R!*bX!b`!dpOr*Vrs(Vsv*Vwx)ex!r*V!r!s!*}!s;'S*V;'S;=`*s<%lO*V!R!+UX!b`!dpOr*Vrs(Vsv*Vwx)ex!g*V!g!h!+q!h;'S*V;'S;=`*s<%lO*V!R!+xY!b`!dpOr!+qrs!,hsv!+qvw!-Swx!.[x!`!+q!`!a!/j!a;'S!+q;'S;=`!0Y<%lO!+qq!,mV!dpOv!,hvx!-Sx!`!,h!`!a!-q!a;'S!,h;'S;=`!.U<%lO!,hP!-VTO!`!-S!`!a!-f!a;'S!-S;'S;=`!-k<%lO!-SP!-kO|PP!-nP;=`<%l!-Sq!-xS!dp|POv(Vx;'S(V;'S;=`(h<%lO(Vq!.XP;=`<%l!,ha!.aX!b`Or!.[rs!-Ssv!.[vw!-Sw!`!.[!`!a!.|!a;'S!.[;'S;=`!/d<%lO!.[a!/TT!b`|POr)esv)ew;'S)e;'S;=`)y<%lO)ea!/gP;=`<%l!.[!R!/sV!b`!dp|POr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!0]P;=`<%l!+q!R!0gX!b`!dpOr*Vrs(Vsv*Vwx)ex#c*V#c#d!1S#d;'S*V;'S;=`*s<%lO*V!R!1ZX!b`!dpOr*Vrs(Vsv*Vwx)ex#V*V#V#W!1v#W;'S*V;'S;=`*s<%lO*V!R!1}X!b`!dpOr*Vrs(Vsv*Vwx)ex#h*V#h#i!2j#i;'S*V;'S;=`*s<%lO*V!R!2qX!b`!dpOr*Vrs(Vsv*Vwx)ex#m*V#m#n!3^#n;'S*V;'S;=`*s<%lO*V!R!3eX!b`!dpOr*Vrs(Vsv*Vwx)ex#d*V#d#e!4Q#e;'S*V;'S;=`*s<%lO*V!R!4XX!b`!dpOr*Vrs(Vsv*Vwx)ex#X*V#X#Y!+q#Y;'S*V;'S;=`*s<%lO*V!R!4{Y!b`!dpOr!4trs!5ksv!4tvw!6Vwx!8]x!a!4t!a!b!:]!b;'S!4t;'S;=`!;r<%lO!4tq!5pV!dpOv!5kvx!6Vx!a!5k!a!b!7W!b;'S!5k;'S;=`!8V<%lO!5kP!6YTO!a!6V!a!b!6i!b;'S!6V;'S;=`!7Q<%lO!6VP!6lTO!`!6V!`!a!6{!a;'S!6V;'S;=`!7Q<%lO!6VP!7QOyPP!7TP;=`<%l!6Vq!7]V!dpOv!5kvx!6Vx!`!5k!`!a!7r!a;'S!5k;'S;=`!8V<%lO!5kq!7yS!dpyPOv(Vx;'S(V;'S;=`(h<%lO(Vq!8YP;=`<%l!5ka!8bX!b`Or!8]rs!6Vsv!8]vw!6Vw!a!8]!a!b!8}!b;'S!8];'S;=`!:V<%lO!8]a!9SX!b`Or!8]rs!6Vsv!8]vw!6Vw!`!8]!`!a!9o!a;'S!8];'S;=`!:V<%lO!8]a!9vT!b`yPOr)esv)ew;'S)e;'S;=`)y<%lO)ea!:YP;=`<%l!8]!R!:dY!b`!dpOr!4trs!5ksv!4tvw!6Vwx!8]x!`!4t!`!a!;S!a;'S!4t;'S;=`!;r<%lO!4t!R!;]V!b`!dpyPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!;uP;=`<%l!4t!V!<TXjSaP!b`!dpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X",tokenizers:[Eu,Vu,qu,zu,Ru,_u,0,1,2,3,4,5],topRules:{Document:[0,16]},dialects:{noMatch:0,selfClosing:515},tokenPrec:517});function Wu(t,e){let i=Object.create(null);for(let n of t.getChildren(24)){let t=n.getChild(25),r=n.getChild(27)||n.getChild(28);t&&(i[e.read(t.from,t.to)]=r?27==r.type.id?e.read(r.from+1,r.to-1):e.read(r.from,r.to):"")}return i}function ju(t,e){let i=t.getChild(23);return i?e.read(i.from,i.to):" "}function Bu(t,e,i){let n;for(let r of i)if(!r.attrs||r.attrs(n||(n=Wu(t.node.parent.firstChild,e))))return{parser:r.parser};return null}function Iu(t=[],e=[]){let i=[],n=[],r=[],s=[];for(let e of t)("script"==e.tag?i:"style"==e.tag?n:"textarea"==e.tag?r:s).push(e);let o=e.length?Object.create(null):null;for(let t of e)(o[t.name]||(o[t.name]=[])).push(t);return uo((t,e)=>{let a=t.type.id;if(29==a)return Bu(t,e,i);if(32==a)return Bu(t,e,n);if(35==a)return Bu(t,e,r);if(21==a&&s.length){let i,n=t.node,r=n.firstChild,o=r&&ju(r,e);if(o)for(let t of s)if(t.tag==o&&(!t.attrs||t.attrs(i||(i=Wu(r,e))))){let e=n.lastChild,i=38==e.type.id?e.from:n.to;if(i>r.to)return{parser:t.parser,overlay:[{from:r.to,to:i}]}}}if(o&&24==a){let i,n=t.node;if(i=n.firstChild){let t=o[e.read(i.from,i.to)];if(t)for(let i of t){if(i.tagName&&i.tagName!=ju(n.parent,e))continue;let t=n.lastChild;if(27==t.type.id){let e=t.from+1,n=t.lastChild,r=t.to-(n&&n.isError?0:1);if(r>e)return{parser:i.parser,overlay:[{from:e,to:r}]}}else if(28==t.type.id)return{parser:i.parser,overlay:[{from:t.from,to:t.to}]}}}}return null})}const Gu=[9,10,11,12,13,32,133,160,5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8232,8233,8239,8287,12288];function Nu(t){return t>=65&&t<=90||t>=97&&t<=122||t>=161}function Uu(t){return t>=48&&t<=57}function Hu(t){return Uu(t)||t>=97&&t<=102||t>=65&&t<=70}const Fu=(t,e,i)=>(n,r)=>{for(let s=!1,o=0,a=0;;a++){let{next:l}=n;if(Nu(l)||45==l||95==l||s&&Uu(l))!s&&(45!=l||a>0)&&(s=!0),o===a&&45==l&&o++,n.advance();else{if(92!=l||10==n.peek(1)){s&&n.acceptToken(2==o&&r.canShift(2)?e:40==l?i:t);break}if(n.advance(),Hu(n.next)){do{n.advance()}while(Hu(n.next));32==n.next&&n.advance()}else n.next>-1&&n.advance();s=!0}}},Ku=new ru(Fu(123,2,124)),Ju=new ru(Fu(125,3,4)),td=new ru(t=>{if(Gu.includes(t.peek(-1))){let{next:e}=t;(Nu(e)||95==e||35==e||46==e||42==e||91==e||58==e&&Nu(t.peek(1))||45==e||38==e)&&t.acceptToken(122)}}),ed=new ru(t=>{if(!Gu.includes(t.peek(-1))){let{next:e}=t;if(37==e&&(t.advance(),t.acceptToken(1)),Nu(e)){do{t.advance()}while(Nu(t.next)||Uu(t.next));t.acceptToken(1)}}}),id=Ao({"AtKeyword import charset namespace keyframes media supports":Ko.definitionKeyword,"from to selector":Ko.keyword,NamespaceName:Ko.namespace,KeyframeName:Ko.labelName,KeyframeRangeName:Ko.operatorKeyword,TagName:Ko.tagName,ClassName:Ko.className,PseudoClassName:Ko.constant(Ko.className),IdName:Ko.labelName,"FeatureName PropertyName":Ko.propertyName,AttributeName:Ko.attributeName,NumberLiteral:Ko.number,KeywordQuery:Ko.keyword,UnaryQueryOp:Ko.operatorKeyword,"CallTag ValueName":Ko.atom,VariableName:Ko.variableName,Callee:Ko.operatorKeyword,Unit:Ko.unit,"UniversalSelector NestingSelector":Ko.definitionOperator,"MatchOp CompareOp":Ko.compareOperator,"ChildOp SiblingOp, LogicOp":Ko.logicOperator,BinOp:Ko.arithmeticOperator,Important:Ko.modifier,Comment:Ko.blockComment,ColorLiteral:Ko.color,"ParenthesizedContent StringLiteral":Ko.string,":":Ko.punctuation,"PseudoOp #":Ko.derefOperator,"; ,":Ko.separator,"( )":Ko.paren,"[ ]":Ko.squareBracket,"{ }":Ko.brace}),nd={__proto__:null,lang:38,"nth-child":38,"nth-last-child":38,"nth-of-type":38,"nth-last-of-type":38,dir:38,"host-context":38,if:84,url:124,"url-prefix":124,domain:124,regexp:124},rd={__proto__:null,or:98,and:98,not:106,only:106,layer:170},sd={__proto__:null,selector:112,layer:166},od={__proto__:null,"@import":162,"@media":174,"@charset":178,"@namespace":182,"@keyframes":188,"@supports":200,"@scope":204},ad={__proto__:null,to:207},ld=Qu.deserialize({version:14,states:"EbQYQdOOO#qQdOOP#xO`OOOOQP'#Cf'#CfOOQP'#Ce'#CeO#}QdO'#ChO$nQaO'#CcO$xQdO'#CkO%TQdO'#DpO%YQdO'#DrO%_QdO'#DuO%_QdO'#DxOOQP'#FV'#FVO&eQhO'#EhOOQS'#FU'#FUOOQS'#Ek'#EkQYQdOOO&lQdO'#EOO&PQhO'#EUO&lQdO'#EWO'aQdO'#EYO'lQdO'#E]O'tQhO'#EcO(VQdO'#EeO(bQaO'#CfO)VQ`O'#D{O)[Q`O'#F`O)gQdO'#F`QOQ`OOP)qO&jO'#CaPOOO)C@t)C@tOOQP'#Cj'#CjOOQP,59S,59SO#}QdO,59SO)|QdO,59VO%TQdO,5:[O%YQdO,5:^O%_QdO,5:aO%_QdO,5:cO%_QdO,5:dO%_QdO'#ErO*XQ`O,58}O*aQdO'#DzOOQS,58},58}OOQP'#Cn'#CnOOQO'#Dn'#DnOOQP,59V,59VO*hQ`O,59VO*mQ`O,59VOOQP'#Dq'#DqOOQP,5:[,5:[OOQO'#Ds'#DsO*rQpO,5:^O+]QaO,5:aO+sQaO,5:dOOQW'#DZ'#DZO,ZQhO'#DdO,xQhO'#FaO'tQhO'#DbO-WQ`O'#DhOOQW'#F['#F[O-]Q`O,5;SO-eQ`O'#DeOOQS-E8i-E8iOOQ['#Cs'#CsO-jQdO'#CtO.QQdO'#CzO.hQdO'#C}O/OQ!pO'#DPO1RQ!jO,5:jOOQO'#DU'#DUO*mQ`O'#DTO1cQ!nO'#FXO3`Q`O'#DVO3eQ`O'#DkOOQ['#FX'#FXO-`Q`O,5:pO3jQ!bO,5:rOOQS'#E['#E[O3rQ`O,5:tO3wQdO,5:tOOQO'#E_'#E_O4PQ`O,5:wO4UQhO,5:}O%_QdO'#DgOOQS,5;P,5;PO-eQ`O,5;PO4^QdO,5;PO4fQdO,5:gO4vQdO'#EtO5TQ`O,5;zO5TQ`O,5;zPOOO'#Ej'#EjP5`O&jO,58{POOO,58{,58{OOQP1G.n1G.nOOQP1G.q1G.qO*hQ`O1G.qO*mQ`O1G.qOOQP1G/v1G/vO5kQpO1G/xO5sQaO1G/{O6ZQaO1G/}O6qQaO1G0OO7XQaO,5;^OOQO-E8p-E8pOOQS1G.i1G.iO7cQ`O,5:fO7hQdO'#DoO7oQdO'#CrOOQP1G/x1G/xO&lQdO1G/xO7vQ!jO'#DZO8UQ!bO,59vO8^QhO,5:OOOQO'#F]'#F]O8XQ!bO,59zO'tQhO,59xO8fQhO'#EvO8sQ`O,5;{O9OQhO,59|O9uQhO'#DiOOQW,5:S,5:SOOQS1G0n1G0nOOQW,5:P,5:PO9|Q!fO'#FYOOQS'#FY'#FYOOQS'#Em'#EmO;^QdO,59`OOQ[,59`,59`O;tQdO,59fOOQ[,59f,59fO<[QdO,59iOOQ[,59i,59iOOQ[,59k,59kO&lQdO,59mO<rQhO'#EQOOQW'#EQ'#EQO=WQ`O1G0UO1[QhO1G0UOOQ[,59o,59oO'tQhO'#DXOOQ[,59q,59qO=]Q#tO,5:VOOQS1G0[1G0[OOQS1G0^1G0^OOQS1G0`1G0`O=hQ`O1G0`O=mQdO'#E`OOQS1G0c1G0cOOQS1G0i1G0iO=xQaO,5:RO-`Q`O1G0kOOQS1G0k1G0kO-eQ`O1G0kO>PQ!fO1G0ROOQO1G0R1G0ROOQO,5;`,5;`O>gQdO,5;`OOQO-E8r-E8rO>tQ`O1G1fPOOO-E8h-E8hPOOO1G.g1G.gOOQP7+$]7+$]OOQP7+%d7+%dO&lQdO7+%dOOQS1G0Q1G0QO?PQaO'#F_O?ZQ`O,5:ZO?`Q!fO'#ElO@^QdO'#FWO@hQ`O,59^O@mQ!bO7+%dO&lQdO1G/bO@uQhO1G/fOOQW1G/j1G/jOOQW1G/d1G/dOAWQhO,5;bOOQO-E8t-E8tOAfQhO'#DZOAtQhO'#F^OBPQ`O'#F^OBUQ`O,5:TOOQS-E8k-E8kOOQ[1G.z1G.zOOQ[1G/Q1G/QOOQ[1G/T1G/TOOQ[1G/X1G/XOBZQdO,5:lOOQS7+%p7+%pOB`Q`O7+%pOBeQhO'#DYOBmQ`O,59sO'tQhO,59sOOQ[1G/q1G/qOBuQ`O1G/qOOQS7+%z7+%zOBzQbO'#DPOOQO'#Eb'#EbOCYQ`O'#EaOOQO'#Ea'#EaOCeQ`O'#EwOCmQdO,5:zOOQS,5:z,5:zOOQ[1G/m1G/mOOQS7+&V7+&VO-`Q`O7+&VOCxQ!fO'#EsO&lQdO'#EsOEPQdO7+%mOOQO7+%m7+%mOOQO1G0z1G0zOEdQ!bO<<IOOElQdO'#EqOEvQ`O,5;yOOQP1G/u1G/uOOQS-E8j-E8jOFOQdO'#EpOFYQ`O,5;rOOQ]1G.x1G.xOOQP<<IO<<IOOFbQdO7+$|OOQO'#D]'#D]OFiQ!bO7+%QOFqQhO'#EoOF{Q`O,5;xO&lQdO,5;xOOQW1G/o1G/oOOQO'#ES'#ESOGTQ`O1G0WOOQS<<I[<<I[O&lQdO,59tOGnQhO1G/_OOQ[1G/_1G/_OGuQ`O1G/_OOQW-E8l-E8lOOQ[7+%]7+%]OOQO,5:{,5:{O=pQdO'#ExOCeQ`O,5;cOOQS,5;c,5;cOOQS-E8u-E8uOOQS1G0f1G0fOOQS<<Iq<<IqOG}Q!fO,5;_OOQS-E8q-E8qOOQO<<IX<<IXOOQPAN>jAN>jOIUQaO,5;]OOQO-E8o-E8oOI`QdO,5;[OOQO-E8n-E8nOOQW<<Hh<<HhOOQW<<Hl<<HlOIjQhO<<HlOI{QhO,5;ZOJWQ`O,5;ZOOQO-E8m-E8mOJ]QdO1G1dOBZQdO'#EuOJgQ`O7+%rOOQW7+%r7+%rOJoQ!bO1G/`OOQ[7+$y7+$yOJzQhO7+$yPKRQ`O'#EnOOQO,5;d,5;dOOQO-E8v-E8vOOQS1G0}1G0}OKWQ`OAN>WO&lQdO1G0uOK]Q`O7+'OOOQO,5;a,5;aOOQO-E8s-E8sOOQW<<I^<<I^OOQ[<<He<<HePOQW,5;Y,5;YOOQWG23rG23rOKeQdO7+&a",stateData:"Kx~O#sOS#tQQ~OW[OZ[O]TO`VOaVOi]OjWOmXO!jYO!mZO!saO!ybO!{cO!}dO#QeO#WfO#YgO#oRO~OQiOW[OZ[O]TO`VOaVOi]OjWOmXO!jYO!mZO!saO!ybO!{cO!}dO#QeO#WfO#YgO#ohO~O#m$SP~P!dO#tmO~O#ooO~O]qO`rOarOjsOmtO!juO!mwO#nvO~OpzO!^xO~P$SOc!QO#o|O#p}O~O#o!RO~O#o!TO~OW[OZ[O]TO`VOaVOjWOmXO!jYO!mZO#oRO~OS!]Oe!YO!V![O!Y!`O#q!XOp$TP~Ok$TP~P&POQ!jOe!cOm!dOp!eOr!mOt!mOz!kO!`!lO#o!bO#p!hO#}!fO~Ot!qO!`!lO#o!pO~Ot!sO#o!sO~OS!]Oe!YO!V![O!Y!`O#q!XO~Oe!vOpzO#Z!xO~O]YX`YX`!pXaYXjYXmYXpYX!^YX!jYX!mYX#nYX~O`!zO~Ok!{O#m$SXo$SX~O#m$SXo$SX~P!dO#u#OO#v#OO#w#QO~Oc#UO#o|O#p}O~OpzO!^xO~Oo$SP~P!dOe#`O~Oe#aO~Ol#bO!h#cO~O]qO`rOarOjsOmtO~Op!ia!^!ia!j!ia!m!ia#n!iad!ia~P*zOp!la!^!la!j!la!m!la#n!lad!la~P*zOR#gOS!]Oe!YOr#gOt#gO!V![O!Y!`O#q#dO#}!fO~O!R#iO!^#jOk$TXp$TX~Oe#mO~Ok#oOpzO~Oe!vO~O]#rO`#rOd#uOi#rOj#rOk#rO~P&lO]#rO`#rOi#rOj#rOk#rOl#wO~P&lO]#rO`#rOi#rOj#rOk#rOo#yO~P&lOP#zOSsXesXksXvsX!VsX!YsX!usX!wsX#qsX!TsXQsX]sX`sXdsXisXjsXmsXpsXrsXtsXzsX!`sX#osX#psX#}sXlsXosX!^sX!qsX#msX~Ov#{O!u#|O!w#}Ok$TP~P'tOe#aOS#{Xk#{Xv#{X!V#{X!Y#{X!u#{X!w#{X#q#{XQ#{X]#{X`#{Xd#{Xi#{Xj#{Xm#{Xp#{Xr#{Xt#{Xz#{X!`#{X#o#{X#p#{X#}#{Xl#{Xo#{X!^#{X!q#{X#m#{X~Oe$RO~Oe$TO~Ok$VOv#{O~Ok$WO~Ot$XO!`!lO~Op$YO~OpzO!R#iO~OpzO#Z$`O~O!q$bOk!oa#m!oao!oa~P&lOk#hX#m#hXo#hX~P!dOk!{O#m$Sao$Sa~O#u#OO#v#OO#w$hO~Ol$jO!h$kO~Op!ii!^!ii!j!ii!m!ii#n!iid!ii~P*zOp!ki!^!ki!j!ki!m!ki#n!kid!ki~P*zOp!li!^!li!j!li!m!li#n!lid!li~P*zOp#fa!^#fa~P$SOo$lO~Od$RP~P%_Od#zP~P&lO`!PXd}X!R}X!T!PX~O`$sO!T$tO~Od$uO!R#iO~Ok#jXp#jX!^#jX~P'tO!^#jOk$Tap$Ta~O!R#iOk!Uap!Ua!^!Uad!Ua`!Ua~OS!]Oe!YO!V![O!Y!`O#q$yO~Od$QP~P9dOv#{OQ#|X]#|X`#|Xd#|Xe#|Xi#|Xj#|Xk#|Xm#|Xp#|Xr#|Xt#|Xz#|X!`#|X#o#|X#p#|X#}#|Xl#|Xo#|X~O]#rO`#rOd%OOi#rOj#rOk#rO~P&lO]#rO`#rOi#rOj#rOk#rOl%PO~P&lO]#rO`#rOi#rOj#rOk#rOo%QO~P&lOe%SOS!tXk!tX!V!tX!Y!tX#q!tX~Ok%TO~Od%YOt%ZO!a%ZO~Ok%[O~Oo%cO#o%^O#}%]O~Od%dO~P$SOv#{O!^%hO!q%jOk!oi#m!oio!oi~P&lOk#ha#m#hao#ha~P!dOk!{O#m$Sio$Si~O!^%mOd$RX~P$SOd%oO~Ov#{OQ#`Xd#`Xe#`Xm#`Xp#`Xr#`Xt#`Xz#`X!^#`X!`#`X#o#`X#p#`X#}#`X~O!^%qOd#zX~P&lOd%sO~Ol%tOv#{O~OR#gOr#gOt#gO#q%vO#}!fO~O!R#iOk#jap#ja!^#ja~O`!PXd}X!R}X!^}X~O!R#iO!^%xOd$QX~O`%zO~Od%{O~O#o%|O~Ok&OO~O`&PO!R#iO~Od&ROk&QO~Od&UO~OP#zOpsX!^sXdsX~O#}%]Op#TX!^#TX~OpzO!^&WO~Oo&[O#o%^O#}%]O~Ov#{OQ#gXe#gXk#gXm#gXp#gXr#gXt#gXz#gX!^#gX!`#gX!q#gX#m#gX#o#gX#p#gX#}#gXo#gX~O!^%hO!q&`Ok!oq#m!oqo!oq~P&lOl&aOv#{O~Od#eX!^#eX~P%_O!^%mOd$Ra~Od#dX!^#dX~P&lO!^%qOd#za~Od&fO~P&lOd&gO!T&hO~Od#cX!^#cX~P9dO!^%xOd$Qa~O]&mOd&oO~OS#bae#ba!V#ba!Y#ba#q#ba~Od&qO~PG]Od&qOk&rO~Ov#{OQ#gae#gak#gam#gap#gar#gat#gaz#ga!^#ga!`#ga!q#ga#m#ga#o#ga#p#ga#}#gao#ga~Od#ea!^#ea~P$SOd#da!^#da~P&lOR#gOr#gOt#gO#q%vO#}%]O~O!R#iOd#ca!^#ca~O`&xO~O!^%xOd$Qi~P&lO]&mOd&|O~Ov#{Od|ik|i~Od&}O~PG]Ok'OO~Od'PO~O!^%xOd$Qq~Od#cq!^#cq~P&lO#s!a#t#}]#}v!m~",goto:"2h$UPPPPP$VP$YP$c$uP$cP%X$cPP%_PPP%e%o%oPPPPP%oPP%oP&]P%oP%o'W%oP't'w'}'}(^'}P'}P'}P'}'}P(m'}(yP(|PP)p)v$c)|$c*SP$cP$c$cP*Y*{+YP$YP+aP+dP$YP$YP$YP+j$YP+m+p+s+z$YP$YPP$YP,P,V,f,|-[-b-l-r-x.O.U.`.f.l.rPPPPPPPPPPP.x/R/w/z0|P1U1u2O2R2U2[RnQ_^OP`kz!{$dq[OPYZ`kuvwxz!v!{#`$d%mqSOPYZ`kuvwxz!v!{#`$d%mQpTR#RqQ!OVR#SrQ#S!QS$Q!i!jR$i#U!V!mac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'Q!U!mac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'QU#g!Y$t&hU%`$Y%b&WR&V%_!V!iac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'QR$S!kQ%W$RR&S%Xk!^]bf!Y![!g#i#j#m$P$R%X%xQ#e!YQ${#mQ%w$tQ&j%xR&w&hQ!ygQ#p!`Q$^!xR%f$`R#n!]!U!mac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'QQ!qdR$X!rQ!PVR#TrQ#S!PR$i#TQ!SWR#VsQ!UXR#WtQ{UQ!wgQ#^yQ#o!_Q$U!nQ$[!uQ$_!yQ%e$^Q&Y%aQ&]%fR&v&XSjPzQ!}kQ$c!{R%k$dZiPkz!{$dR$P!gQ%}%SR&z&mR!rdR!teR$Z!tS%a$Y%bR&t&WV%_$Y%b&WQ#PmR$g#PQ`OSkPzU!a`k$dR$d!{Q$p#aY%p$p%u&d&l'QQ%u$sQ&d%qQ&l%zR'Q&xQ#t!cQ#v!dQ#x!eV$}#t#v#xQ%X$RR&T%XQ%y$zS&k%y&yR&y&lQ%r$pR&e%rQ%n$mR&c%nQyUR#]yQ%i$aR&_%iQ!|jS$e!|$fR$f!}Q&n%}R&{&nQ#k!ZR$x#kQ%b$YR&Z%bQ&X%aR&u&X__OP`kz!{$d^UOP`kz!{$dQ!VYQ!WZQ#XuQ#YvQ#ZwQ#[xQ$]!vQ$m#`R&b%mR$q#aQ!gaQ!oc[#q!c!d!e#t#v#xQ$a!zd$o#a$p$s%q%u%z&d&l&x'QQ$r#cQ%R#{S%g$a%iQ%l$kQ&^%hR&p&P]#s!c!d!e#t#v#xW!Z]b!g$PQ!ufQ#f!YQ#l![Q$v#iQ$w#jQ$z#mS%V$R%XR&i%xQ#h!YQ%w$tR&w&hR$|#mR$n#`QlPR#_zQ!_]Q!nbQ$O!gR%U$P",nodeNames:"⚠ Unit VariableName VariableName QueryCallee Comment StyleSheet RuleSet UniversalSelector TagSelector TagName NestingSelector ClassSelector . ClassName PseudoClassSelector : :: PseudoClassName PseudoClassName ) ( ArgList ValueName ParenthesizedValue AtKeyword # ; ] [ BracketedValue } { BracedValue ColorLiteral NumberLiteral StringLiteral BinaryExpression BinOp CallExpression Callee IfExpression if ArgList IfBranch KeywordQuery FeatureQuery FeatureName BinaryQuery LogicOp ComparisonQuery CompareOp UnaryQuery UnaryQueryOp ParenthesizedQuery SelectorQuery selector ParenthesizedSelector CallQuery ArgList , CallLiteral CallTag ParenthesizedContent PseudoClassName ArgList IdSelector IdName AttributeSelector AttributeName MatchOp ChildSelector ChildOp DescendantSelector SiblingSelector SiblingOp Block Declaration PropertyName Important ImportStatement import Layer layer LayerName layer MediaStatement media CharsetStatement charset NamespaceStatement namespace NamespaceName KeyframesStatement keyframes KeyframeName KeyframeList KeyframeSelector KeyframeRangeName SupportsStatement supports ScopeStatement scope to AtRule Styles",maxTerm:143,nodeProps:[["isolate",-2,5,36,""],["openedBy",20,"(",28,"[",31,"{"],["closedBy",21,")",29,"]",32,"}"]],propSources:[id],skippedNodes:[0,5,106],repeatNodeCount:15,tokenData:"JQ~R!YOX$qX^%i^p$qpq%iqr({rs-ust/itu6Wuv$qvw7Qwx7cxy9Qyz9cz{9h{|:R|}>t}!O?V!O!P?t!P!Q@]!Q![AU![!]BP!]!^B{!^!_C^!_!`DY!`!aDm!a!b$q!b!cEn!c!}$q!}#OG{#O#P$q#P#QH^#Q#R6W#R#o$q#o#pHo#p#q6W#q#rIQ#r#sIc#s#y$q#y#z%i#z$f$q$f$g%i$g#BY$q#BY#BZ%i#BZ$IS$q$IS$I_%i$I_$I|$q$I|$JO%i$JO$JT$q$JT$JU%i$JU$KV$q$KV$KW%i$KW&FU$q&FU&FV%i&FV;'S$q;'S;=`Iz<%lO$q`$tSOy%Qz;'S%Q;'S;=`%c<%lO%Q`%VS!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Q`%fP;=`<%l%Q~%nh#s~OX%QX^'Y^p%Qpq'Yqy%Qz#y%Q#y#z'Y#z$f%Q$f$g'Y$g#BY%Q#BY#BZ'Y#BZ$IS%Q$IS$I_'Y$I_$I|%Q$I|$JO'Y$JO$JT%Q$JT$JU'Y$JU$KV%Q$KV$KW'Y$KW&FU%Q&FU&FV'Y&FV;'S%Q;'S;=`%c<%lO%Q~'ah#s~!a`OX%QX^'Y^p%Qpq'Yqy%Qz#y%Q#y#z'Y#z$f%Q$f$g'Y$g#BY%Q#BY#BZ'Y#BZ$IS%Q$IS$I_'Y$I_$I|%Q$I|$JO'Y$JO$JT%Q$JT$JU'Y$JU$KV%Q$KV$KW'Y$KW&FU%Q&FU&FV'Y&FV;'S%Q;'S;=`%c<%lO%Qj)OUOy%Qz#]%Q#]#^)b#^;'S%Q;'S;=`%c<%lO%Qj)gU!a`Oy%Qz#a%Q#a#b)y#b;'S%Q;'S;=`%c<%lO%Qj*OU!a`Oy%Qz#d%Q#d#e*b#e;'S%Q;'S;=`%c<%lO%Qj*gU!a`Oy%Qz#c%Q#c#d*y#d;'S%Q;'S;=`%c<%lO%Qj+OU!a`Oy%Qz#f%Q#f#g+b#g;'S%Q;'S;=`%c<%lO%Qj+gU!a`Oy%Qz#h%Q#h#i+y#i;'S%Q;'S;=`%c<%lO%Qj,OU!a`Oy%Qz#T%Q#T#U,b#U;'S%Q;'S;=`%c<%lO%Qj,gU!a`Oy%Qz#b%Q#b#c,y#c;'S%Q;'S;=`%c<%lO%Qj-OU!a`Oy%Qz#h%Q#h#i-b#i;'S%Q;'S;=`%c<%lO%Qj-iS!qY!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Q~-xWOY-uZr-urs.bs#O-u#O#P.g#P;'S-u;'S;=`/c<%lO-u~.gOt~~.jRO;'S-u;'S;=`.s;=`O-u~.vXOY-uZr-urs.bs#O-u#O#P.g#P;'S-u;'S;=`/c;=`<%l-u<%lO-u~/fP;=`<%l-uj/nYjYOy%Qz!Q%Q!Q![0^![!c%Q!c!i0^!i#T%Q#T#Z0^#Z;'S%Q;'S;=`%c<%lO%Qj0cY!a`Oy%Qz!Q%Q!Q![1R![!c%Q!c!i1R!i#T%Q#T#Z1R#Z;'S%Q;'S;=`%c<%lO%Qj1WY!a`Oy%Qz!Q%Q!Q![1v![!c%Q!c!i1v!i#T%Q#T#Z1v#Z;'S%Q;'S;=`%c<%lO%Qj1}YrY!a`Oy%Qz!Q%Q!Q![2m![!c%Q!c!i2m!i#T%Q#T#Z2m#Z;'S%Q;'S;=`%c<%lO%Qj2tYrY!a`Oy%Qz!Q%Q!Q![3d![!c%Q!c!i3d!i#T%Q#T#Z3d#Z;'S%Q;'S;=`%c<%lO%Qj3iY!a`Oy%Qz!Q%Q!Q![4X![!c%Q!c!i4X!i#T%Q#T#Z4X#Z;'S%Q;'S;=`%c<%lO%Qj4`YrY!a`Oy%Qz!Q%Q!Q![5O![!c%Q!c!i5O!i#T%Q#T#Z5O#Z;'S%Q;'S;=`%c<%lO%Qj5TY!a`Oy%Qz!Q%Q!Q![5s![!c%Q!c!i5s!i#T%Q#T#Z5s#Z;'S%Q;'S;=`%c<%lO%Qj5zSrY!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Qd6ZUOy%Qz!_%Q!_!`6m!`;'S%Q;'S;=`%c<%lO%Qd6tS!hS!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Qb7VSZQOy%Qz;'S%Q;'S;=`%c<%lO%Q~7fWOY7cZw7cwx.bx#O7c#O#P8O#P;'S7c;'S;=`8z<%lO7c~8RRO;'S7c;'S;=`8[;=`O7c~8_XOY7cZw7cwx.bx#O7c#O#P8O#P;'S7c;'S;=`8z;=`<%l7c<%lO7c~8}P;=`<%l7cj9VSeYOy%Qz;'S%Q;'S;=`%c<%lO%Q~9hOd~n9oUWQvWOy%Qz!_%Q!_!`6m!`;'S%Q;'S;=`%c<%lO%Qj:YWvW!mQOy%Qz!O%Q!O!P:r!P!Q%Q!Q![=w![;'S%Q;'S;=`%c<%lO%Qj:wU!a`Oy%Qz!Q%Q!Q![;Z![;'S%Q;'S;=`%c<%lO%Qj;bY!a`#}YOy%Qz!Q%Q!Q![;Z![!g%Q!g!h<Q!h#X%Q#X#Y<Q#Y;'S%Q;'S;=`%c<%lO%Qj<VY!a`Oy%Qz{%Q{|<u|}%Q}!O<u!O!Q%Q!Q![=^![;'S%Q;'S;=`%c<%lO%Qj<zU!a`Oy%Qz!Q%Q!Q![=^![;'S%Q;'S;=`%c<%lO%Qj=eU!a`#}YOy%Qz!Q%Q!Q![=^![;'S%Q;'S;=`%c<%lO%Qj>O[!a`#}YOy%Qz!O%Q!O!P;Z!P!Q%Q!Q![=w![!g%Q!g!h<Q!h#X%Q#X#Y<Q#Y;'S%Q;'S;=`%c<%lO%Qj>yS!^YOy%Qz;'S%Q;'S;=`%c<%lO%Qj?[WvWOy%Qz!O%Q!O!P:r!P!Q%Q!Q![=w![;'S%Q;'S;=`%c<%lO%Qj?yU]YOy%Qz!Q%Q!Q![;Z![;'S%Q;'S;=`%c<%lO%Q~@bTvWOy%Qz{@q{;'S%Q;'S;=`%c<%lO%Q~@xS!a`#t~Oy%Qz;'S%Q;'S;=`%c<%lO%QjAZ[#}YOy%Qz!O%Q!O!P;Z!P!Q%Q!Q![=w![!g%Q!g!h<Q!h#X%Q#X#Y<Q#Y;'S%Q;'S;=`%c<%lO%QjBUU`YOy%Qz![%Q![!]Bh!];'S%Q;'S;=`%c<%lO%QbBoSaQ!a`Oy%Qz;'S%Q;'S;=`%c<%lO%QjCQSkYOy%Qz;'S%Q;'S;=`%c<%lO%QhCcU!TWOy%Qz!_%Q!_!`Cu!`;'S%Q;'S;=`%c<%lO%QhC|S!TW!a`Oy%Qz;'S%Q;'S;=`%c<%lO%QlDaS!TW!hSOy%Qz;'S%Q;'S;=`%c<%lO%QjDtV!jQ!TWOy%Qz!_%Q!_!`Cu!`!aEZ!a;'S%Q;'S;=`%c<%lO%QbEbS!jQ!a`Oy%Qz;'S%Q;'S;=`%c<%lO%QjEqYOy%Qz}%Q}!OFa!O!c%Q!c!}GO!}#T%Q#T#oGO#o;'S%Q;'S;=`%c<%lO%QjFfW!a`Oy%Qz!c%Q!c!}GO!}#T%Q#T#oGO#o;'S%Q;'S;=`%c<%lO%QjGV[iY!a`Oy%Qz}%Q}!OGO!O!Q%Q!Q![GO![!c%Q!c!}GO!}#T%Q#T#oGO#o;'S%Q;'S;=`%c<%lO%QjHQSmYOy%Qz;'S%Q;'S;=`%c<%lO%QnHcSl^Oy%Qz;'S%Q;'S;=`%c<%lO%QjHtSpYOy%Qz;'S%Q;'S;=`%c<%lO%QjIVSoYOy%Qz;'S%Q;'S;=`%c<%lO%QfIhU!mQOy%Qz!_%Q!_!`6m!`;'S%Q;'S;=`%c<%lO%Q`I}P;=`<%l$q",tokenizers:[td,ed,Ku,Ju,1,2,3,4,new nu("m~RRYZ[z{a~~g~aO#v~~dP!P!Qg~lO#w~~",28,129)],topRules:{StyleSheet:[0,6],Styles:[1,105]},specialized:[{term:124,get:t=>nd[t]||-1},{term:125,get:t=>rd[t]||-1},{term:4,get:t=>sd[t]||-1},{term:25,get:t=>od[t]||-1},{term:123,get:t=>ad[t]||-1}],tokenPrec:1963});let hd=null;function cd(){if(!hd&&"object"==typeof document&&document.body){let{style:t}=document.body,e=[],i=new Set;for(let n in t)"cssText"!=n&&"cssFloat"!=n&&"string"==typeof t[n]&&(/[A-Z]/.test(n)&&(n=n.replace(/[A-Z]/g,t=>"-"+t.toLowerCase())),i.has(n)||(e.push(n),i.add(n)));hd=e.sort().map(t=>({type:"property",label:t,apply:t+": "}))}return hd||[]}const Od=["active","after","any-link","autofill","backdrop","before","checked","cue","default","defined","disabled","empty","enabled","file-selector-button","first","first-child","first-letter","first-line","first-of-type","focus","focus-visible","focus-within","fullscreen","has","host","host-context","hover","in-range","indeterminate","invalid","is","lang","last-child","last-of-type","left","link","marker","modal","not","nth-child","nth-last-child","nth-last-of-type","nth-of-type","only-child","only-of-type","optional","out-of-range","part","placeholder","placeholder-shown","read-only","read-write","required","right","root","scope","selection","slotted","target","target-text","valid","visited","where"].map(t=>({type:"class",label:t})),ud=["above","absolute","activeborder","additive","activecaption","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","antialiased","appworkspace","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","axis-pan","background","backwards","baseline","below","bidi-override","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","bullets","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","calc","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","contain","content","contents","content-box","context-menu","continuous","copy","counter","counters","cover","crop","cross","crosshair","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic-abegede-gez","ethiopic-halehame-aa-er","ethiopic-halehame-gez","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fill-box","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","graytext","grid","groove","hand","hard-light","help","hidden","hide","higher","highlight","highlighttext","horizontal","hsl","hsla","hue","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","justify","keep-all","landscape","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-hexadecimal","lower-latin","lower-norwegian","lowercase","ltr","luminosity","manipulation","match","matrix","matrix3d","medium","menu","menutext","message-box","middle","min-intrinsic","mix","monospace","move","multiple","multiple_mask_images","multiply","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","opacity","open-quote","optimizeLegibility","optimizeSpeed","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","perspective","pinch-zoom","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","self-start","self-end","semi-condensed","semi-expanded","separate","serif","show","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","start","static","status-bar","stretch","stroke","stroke-box","sub","subpixel-antialiased","svg_masks","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","text","text-bottom","text-top","textarea","textfield","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","to","top","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unidirectional-pan","unset","up","upper-latin","uppercase","url","var","vertical","vertical-text","view-box","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"].map(t=>({type:"keyword",label:t})).concat(["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"].map(t=>({type:"constant",label:t}))),dd=["a","abbr","address","article","aside","b","bdi","bdo","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","dd","del","details","dfn","dialog","div","dl","dt","em","figcaption","figure","footer","form","header","hgroup","h1","h2","h3","h4","h5","h6","hr","html","i","iframe","img","input","ins","kbd","label","legend","li","main","meter","nav","ol","output","p","pre","ruby","section","select","small","source","span","strong","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","tr","u","ul"].map(t=>({type:"type",label:t})),fd=["@charset","@color-profile","@container","@counter-style","@font-face","@font-feature-values","@font-palette-values","@import","@keyframes","@layer","@media","@namespace","@page","@position-try","@property","@scope","@starting-style","@supports","@view-transition"].map(t=>({type:"keyword",label:t})),pd=/^(\w[\w-]*|-\w[\w-]*|)$/,gd=/^-(-[\w-]*)?$/,md=new lo,Qd=["Declaration"];function Sd(t){for(let e=t;;){if(e.type.isTop)return e;if(!(e=e.parent))return t}}function xd(t,e,i){if(e.to-e.from>4096){let n=md.get(e);if(n)return n;let r=[],s=new Set,o=e.cursor(Ws.IncludeAnonymous);if(o.firstChild())do{for(let e of xd(t,o.node,i))s.has(e.label)||(s.add(e.label),r.push(e))}while(o.nextSibling());return md.set(e,r),r}{let n=[],r=new Set;return e.cursor().iterate(e=>{var s;if(i(e)&&e.matchContext(Qd)&&":"==(null===(s=e.node.nextSibling)||void 0===s?void 0:s.name)){let i=t.sliceString(e.from,e.to);r.has(i)||(r.add(i),n.push({label:i,type:"variable"}))}}),n}}const yd=t=>e=>{let{state:i,pos:n}=e,r=oa(i).resolveInner(n,-1),s=r.type.isError&&r.from==r.to-1&&"-"==i.doc.sliceString(r.from,r.to);if("PropertyName"==r.name||(s||"TagName"==r.name)&&/^(Block|Styles)$/.test(r.resolve(r.to).name))return{from:r.from,options:cd(),validFor:pd};if("ValueName"==r.name)return{from:r.from,options:ud,validFor:pd};if("PseudoClassName"==r.name)return{from:r.from,options:Od,validFor:pd};if(t(r)||(e.explicit||s)&&function(t,e){var i;if(("("==t.name||t.type.isError)&&(t=t.parent||t),"ArgList"!=t.name)return!1;let n=null===(i=t.parent)||void 0===i?void 0:i.firstChild;return"Callee"==(null==n?void 0:n.name)&&"var"==e.sliceString(n.from,n.to)}(r,i.doc))return{from:t(r)||s?r.from:n,options:xd(i.doc,Sd(r),t),validFor:gd};if("TagName"==r.name){for(let{parent:t}=r;t;t=t.parent)if("Block"==t.name)return{from:r.from,options:cd(),validFor:pd};return{from:r.from,options:dd,validFor:pd}}if("AtKeyword"==r.name)return{from:r.from,options:fd,validFor:pd};if(!e.explicit)return null;let o=r.resolve(n),a=o.childBefore(n);return a&&":"==a.name&&"PseudoClassSelector"==o.name?{from:n,options:Od,validFor:pd}:a&&":"==a.name&&"Declaration"==o.name||"ArgList"==o.name?{from:n,options:ud,validFor:pd}:"Block"==o.name||"Styles"==o.name?{from:n,options:cd(),validFor:pd}:null},wd=yd(t=>"VariableName"==t.name),bd=sa.define({name:"css",parser:ld.configure({props:[ka.add({Declaration:Ca()}),Ra.add({"Block KeyframeList":_a})]}),languageData:{commentTokens:{block:{open:"/*",close:"*/"}},indentOnInput:/^\s*\}$/,wordChars:"-"}}),kd=[9,10,11,12,13,32,133,160,5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8232,8233,8239,8287,12288],vd=new mu({start:!1,shift:(t,e)=>5==e||6==e||320==e?t:321==e,strict:!1}),$d=new ru((t,e)=>{let{next:i}=t;(125==i||-1==i||e.context)&&t.acceptToken(318)},{contextual:!0,fallback:!0}),Pd=new ru((t,e)=>{let i,{next:n}=t;kd.indexOf(n)>-1||(47!=n||47!=(i=t.peek(1))&&42!=i)&&(125==n||59==n||-1==n||e.context||t.acceptToken(316))},{contextual:!0}),Td=new ru((t,e)=>{91!=t.next||e.context||t.acceptToken(317)},{contextual:!0}),Zd=new ru((t,e)=>{let{next:i}=t;if(43==i||45==i){if(t.advance(),i==t.next){t.advance();let i=!e.context&&e.canShift(1);t.acceptToken(i?1:2)}}else 63==i&&46==t.peek(1)&&(t.advance(),t.advance(),(t.next<48||t.next>57)&&t.acceptToken(3))},{contextual:!0});function Xd(t,e){return t>=65&&t<=90||t>=97&&t<=122||95==t||t>=192||!e&&t>=48&&t<=57}const Ad=new ru((t,e)=>{if(60!=t.next||!e.dialectEnabled(0))return;if(t.advance(),47==t.next)return;let i=0;for(;kd.indexOf(t.next)>-1;)t.advance(),i++;if(Xd(t.next,!0)){for(t.advance(),i++;Xd(t.next,!1);)t.advance(),i++;for(;kd.indexOf(t.next)>-1;)t.advance(),i++;if(44==t.next)return;for(let e=0;;e++){if(7==e){if(!Xd(t.next,!0))return;break}if(t.next!="extends".charCodeAt(e))break;t.advance(),i++}}t.acceptToken(4,-i)}),Cd=Ao({"get set async static":Ko.modifier,"for while do if else switch try catch finally return throw break continue default case defer":Ko.controlKeyword,"in of await yield void typeof delete instanceof as satisfies":Ko.operatorKeyword,"let var const using function class extends":Ko.definitionKeyword,"import export from":Ko.moduleKeyword,"with debugger new":Ko.keyword,TemplateString:Ko.special(Ko.string),super:Ko.atom,BooleanLiteral:Ko.bool,this:Ko.self,null:Ko.null,Star:Ko.modifier,VariableName:Ko.variableName,"CallExpression/VariableName TaggedTemplateExpression/VariableName":Ko.function(Ko.variableName),VariableDefinition:Ko.definition(Ko.variableName),Label:Ko.labelName,PropertyName:Ko.propertyName,PrivatePropertyName:Ko.special(Ko.propertyName),"CallExpression/MemberExpression/PropertyName":Ko.function(Ko.propertyName),"FunctionDeclaration/VariableDefinition":Ko.function(Ko.definition(Ko.variableName)),"ClassDeclaration/VariableDefinition":Ko.definition(Ko.className),"NewExpression/VariableName":Ko.className,PropertyDefinition:Ko.definition(Ko.propertyName),PrivatePropertyDefinition:Ko.definition(Ko.special(Ko.propertyName)),UpdateOp:Ko.updateOperator,"LineComment Hashbang":Ko.lineComment,BlockComment:Ko.blockComment,Number:Ko.number,String:Ko.string,Escape:Ko.escape,ArithOp:Ko.arithmeticOperator,LogicOp:Ko.logicOperator,BitOp:Ko.bitwiseOperator,CompareOp:Ko.compareOperator,RegExp:Ko.regexp,Equals:Ko.definitionOperator,Arrow:Ko.function(Ko.punctuation),": Spread":Ko.punctuation,"( )":Ko.paren,"[ ]":Ko.squareBracket,"{ }":Ko.brace,"InterpolationStart InterpolationEnd":Ko.special(Ko.brace),".":Ko.derefOperator,", ;":Ko.separator,"@":Ko.meta,TypeName:Ko.typeName,TypeDefinition:Ko.definition(Ko.typeName),"type enum interface implements namespace module declare":Ko.definitionKeyword,"abstract global Privacy readonly override":Ko.modifier,"is keyof unique infer asserts":Ko.operatorKeyword,JSXAttributeValue:Ko.attributeValue,JSXText:Ko.content,"JSXStartTag JSXStartCloseTag JSXSelfCloseEndTag JSXEndTag":Ko.angleBracket,"JSXIdentifier JSXNameSpacedName":Ko.tagName,"JSXAttribute/JSXIdentifier JSXAttribute/JSXNameSpacedName":Ko.attributeName,"JSXBuiltin/JSXIdentifier":Ko.standard(Ko.tagName)}),Md={__proto__:null,export:20,as:25,from:33,default:36,async:41,function:42,in:52,out:55,const:56,extends:60,this:64,true:72,false:72,null:84,void:88,typeof:92,super:108,new:142,delete:154,yield:163,await:167,class:172,public:235,private:235,protected:235,readonly:237,instanceof:256,satisfies:259,import:292,keyof:349,unique:353,infer:359,asserts:395,is:397,abstract:417,implements:419,type:421,let:424,var:426,using:429,interface:435,enum:439,namespace:445,module:447,declare:451,global:455,defer:471,for:476,of:485,while:488,with:492,do:496,if:500,else:502,switch:506,case:512,try:518,catch:522,finally:526,return:530,throw:534,break:538,continue:542,debugger:546},Rd={__proto__:null,async:129,get:131,set:133,declare:195,public:197,private:197,protected:197,static:199,abstract:201,override:203,readonly:209,accessor:211,new:401},_d={__proto__:null,"<":193},zd=Qu.deserialize({version:14,states:"$F|Q%TQlOOO%[QlOOO'_QpOOP(lO`OOO*zQ!0MxO'#CiO+RO#tO'#CjO+aO&jO'#CjO+oO#@ItO'#DaO.QQlO'#DgO.bQlO'#DrO%[QlO'#DzO0fQlO'#ESOOQ!0Lf'#E['#E[O1PQ`O'#EXOOQO'#Ep'#EpOOQO'#Il'#IlO1XQ`O'#GsO1dQ`O'#EoO1iQ`O'#EoO3hQ!0MxO'#JrO6[Q!0MxO'#JsO6uQ`O'#F]O6zQ,UO'#FtOOQ!0Lf'#Ff'#FfO7VO7dO'#FfO9XQMhO'#F|O9`Q`O'#F{OOQ!0Lf'#Js'#JsOOQ!0Lb'#Jr'#JrO9eQ`O'#GwOOQ['#K_'#K_O9pQ`O'#IYO9uQ!0LrO'#IZOOQ['#J`'#J`OOQ['#I_'#I_Q`QlOOQ`QlOOO9}Q!L^O'#DvO:UQlO'#EOO:]QlO'#EQO9kQ`O'#GsO:dQMhO'#CoO:rQ`O'#EnO:}Q`O'#EyO;hQMhO'#FeO;xQ`O'#GsOOQO'#K`'#K`O;}Q`O'#K`O<]Q`O'#G{O<]Q`O'#G|O<]Q`O'#HOO9kQ`O'#HRO=SQ`O'#HUO>kQ`O'#CeO>{Q`O'#HcO?TQ`O'#HiO?TQ`O'#HkO`QlO'#HmO?TQ`O'#HoO?TQ`O'#HrO?YQ`O'#HxO?_Q!0LsO'#IOO%[QlO'#IQO?jQ!0LsO'#ISO?uQ!0LsO'#IUO9uQ!0LrO'#IWO@QQ!0MxO'#CiOASQpO'#DlQOQ`OOO%[QlO'#EQOAjQ`O'#ETO:dQMhO'#EnOAuQ`O'#EnOBQQ!bO'#FeOOQ['#Cg'#CgOOQ!0Lb'#Dq'#DqOOQ!0Lb'#Jv'#JvO%[QlO'#JvOOQO'#Jy'#JyOOQO'#Ih'#IhOCQQpO'#EgOOQ!0Lb'#Ef'#EfOOQ!0Lb'#J}'#J}OC|Q!0MSO'#EgODWQpO'#EWOOQO'#Jx'#JxODlQpO'#JyOEyQpO'#EWODWQpO'#EgPFWO&2DjO'#CbPOOO)CD})CD}OOOO'#I`'#I`OFcO#tO,59UOOQ!0Lh,59U,59UOOOO'#Ia'#IaOFqO&jO,59UOGPQ!L^O'#DcOOOO'#Ic'#IcOGWO#@ItO,59{OOQ!0Lf,59{,59{OGfQlO'#IdOGyQ`O'#JtOIxQ!fO'#JtO+}QlO'#JtOJPQ`O,5:ROJgQ`O'#EpOJtQ`O'#KTOKPQ`O'#KSOKPQ`O'#KSOKXQ`O,5;^OK^Q`O'#KROOQ!0Ln,5:^,5:^OKeQlO,5:^OMcQ!0MxO,5:fONSQ`O,5:nONmQ!0LrO'#KQONtQ`O'#KPO9eQ`O'#KPO! YQ`O'#KPO! bQ`O,5;]O! gQ`O'#KPO!#lQ!fO'#JsOOQ!0Lh'#Ci'#CiO%[QlO'#ESO!$[Q!fO,5:sOOQS'#Jz'#JzOOQO-E<j-E<jO9kQ`O,5=_O!$rQ`O,5=_O!$wQlO,5;ZO!&zQMhO'#EkO!(eQ`O,5;ZO!(jQlO'#DyO!(tQpO,5;dO!(|QpO,5;dO%[QlO,5;dOOQ['#FT'#FTOOQ['#FV'#FVO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eOOQ['#FZ'#FZO!)[QlO,5;tOOQ!0Lf,5;y,5;yOOQ!0Lf,5;z,5;zOOQ!0Lf,5;|,5;|O%[QlO'#IpO!+_Q!0LrO,5<iO%[QlO,5;eO!&zQMhO,5;eO!+|QMhO,5;eO!-nQMhO'#E^O%[QlO,5;wOOQ!0Lf,5;{,5;{O!-uQ,UO'#FjO!.rQ,UO'#KXO!.^Q,UO'#KXO!.yQ,UO'#KXOOQO'#KX'#KXO!/_Q,UO,5<SOOOW,5<`,5<`O!/pQlO'#FvOOOW'#Io'#IoO7VO7dO,5<QO!/wQ,UO'#FxOOQ!0Lf,5<Q,5<QO!0hQ$IUO'#CyOOQ!0Lh'#C}'#C}O!0{O#@ItO'#DRO!1iQMjO,5<eO!1pQ`O,5<hO!3YQ(CWO'#GXO!3jQ`O'#GYO!3oQ`O'#GYO!5_Q(CWO'#G^O!6dQpO'#GbOOQO'#Gn'#GnO!,TQMhO'#GmOOQO'#Gp'#GpO!,TQMhO'#GoO!7VQ$IUO'#JlOOQ!0Lh'#Jl'#JlO!7aQ`O'#JkO!7oQ`O'#JjO!7wQ`O'#CuOOQ!0Lh'#C{'#C{O!8YQ`O'#C}OOQ!0Lh'#DV'#DVOOQ!0Lh'#DX'#DXO!8_Q`O,5<eO1SQ`O'#DZO!,TQMhO'#GPO!,TQMhO'#GRO!8gQ`O'#GTO!8lQ`O'#GUO!3oQ`O'#G[O!,TQMhO'#GaO<]Q`O'#JkO!8qQ`O'#EqO!9`Q`O,5<gOOQ!0Lb'#Cr'#CrO!9hQ`O'#ErO!:bQpO'#EsOOQ!0Lb'#KR'#KRO!:iQ!0LrO'#KaO9uQ!0LrO,5=cO`QlO,5>tOOQ['#Jh'#JhOOQ[,5>u,5>uOOQ[-E<]-E<]O!<hQ!0MxO,5:bO!:]QpO,5:`O!?RQ!0MxO,5:jO%[QlO,5:jO!AiQ!0MxO,5:lOOQO,5@z,5@zO!BYQMhO,5=_O!BhQ!0LrO'#JiO9`Q`O'#JiO!ByQ!0LrO,59ZO!CUQpO,59ZO!C^QMhO,59ZO:dQMhO,59ZO!CiQ`O,5;ZO!CqQ`O'#HbO!DVQ`O'#KdO%[QlO,5;}O!:]QpO,5<PO!D_Q`O,5=zO!DdQ`O,5=zO!DiQ`O,5=zO!DwQ`O,5=zO9uQ!0LrO,5=zO<]Q`O,5=jOOQO'#Cy'#CyO!EOQpO,5=gO!EWQMhO,5=hO!EcQ`O,5=jO!EhQ!bO,5=mO!EpQ`O'#K`O?YQ`O'#HWO9kQ`O'#HYO!EuQ`O'#HYO:dQMhO'#H[O!EzQ`O'#H[OOQ[,5=p,5=pO!FPQ`O'#H]O!FbQ`O'#CoO!FgQ`O,59PO!FqQ`O,59PO!HvQlO,59POOQ[,59P,59PO!IWQ!0LrO,59PO%[QlO,59PO!KcQlO'#HeOOQ['#Hf'#HfOOQ['#Hg'#HgO`QlO,5=}O!KyQ`O,5=}O`QlO,5>TO`QlO,5>VO!LOQ`O,5>XO`QlO,5>ZO!LTQ`O,5>^O!LYQlO,5>dOOQ[,5>j,5>jO%[QlO,5>jO9uQ!0LrO,5>lOOQ[,5>n,5>nO#!dQ`O,5>nOOQ[,5>p,5>pO#!dQ`O,5>pOOQ[,5>r,5>rO##QQpO'#D_O%[QlO'#JvO##sQpO'#JvO##}QpO'#DmO#$`QpO'#DmO#&qQlO'#DmO#&xQ`O'#JuO#'QQ`O,5:WO#'VQ`O'#EtO#'eQ`O'#KUO#'mQ`O,5;_O#'rQpO'#DmO#(PQpO'#EVOOQ!0Lf,5:o,5:oO%[QlO,5:oO#(WQ`O,5:oO?YQ`O,5;YO!CUQpO,5;YO!C^QMhO,5;YO:dQMhO,5;YO#(`Q`O,5@bO#(eQ07dO,5:sOOQO-E<f-E<fO#)kQ!0MSO,5;RODWQpO,5:rO#)uQpO,5:rODWQpO,5;RO!ByQ!0LrO,5:rOOQ!0Lb'#Ej'#EjOOQO,5;R,5;RO%[QlO,5;RO#*SQ!0LrO,5;RO#*_Q!0LrO,5;RO!CUQpO,5:rOOQO,5;X,5;XO#*mQ!0LrO,5;RPOOO'#I^'#I^P#+RO&2DjO,58|POOO,58|,58|OOOO-E<^-E<^OOQ!0Lh1G.p1G.pOOOO-E<_-E<_OOOO,59},59}O#+^Q!bO,59}OOOO-E<a-E<aOOQ!0Lf1G/g1G/gO#+cQ!fO,5?OO+}QlO,5?OOOQO,5?U,5?UO#+mQlO'#IdOOQO-E<b-E<bO#+zQ`O,5@`O#,SQ!fO,5@`O#,ZQ`O,5@nOOQ!0Lf1G/m1G/mO%[QlO,5@oO#,cQ`O'#IjOOQO-E<h-E<hO#,ZQ`O,5@nOOQ!0Lb1G0x1G0xOOQ!0Ln1G/x1G/xOOQ!0Ln1G0Y1G0YO%[QlO,5@lO#,wQ!0LrO,5@lO#-YQ!0LrO,5@lO#-aQ`O,5@kO9eQ`O,5@kO#-iQ`O,5@kO#-wQ`O'#ImO#-aQ`O,5@kOOQ!0Lb1G0w1G0wO!(tQpO,5:uO!)PQpO,5:uOOQS,5:w,5:wO#.iQdO,5:wO#.qQMhO1G2yO9kQ`O1G2yOOQ!0Lf1G0u1G0uO#/PQ!0MxO1G0uO#0UQ!0MvO,5;VOOQ!0Lh'#GW'#GWO#0rQ!0MzO'#JlO!$wQlO1G0uO#2}Q!fO'#JwO%[QlO'#JwO#3XQ`O,5:eOOQ!0Lh'#D_'#D_OOQ!0Lf1G1O1G1OO%[QlO1G1OOOQ!0Lf1G1f1G1fO#3^Q`O1G1OO#5rQ!0MxO1G1PO#5yQ!0MxO1G1PO#8aQ!0MxO1G1PO#8hQ!0MxO1G1PO#;OQ!0MxO1G1PO#=fQ!0MxO1G1PO#=mQ!0MxO1G1PO#=tQ!0MxO1G1PO#@[Q!0MxO1G1PO#@cQ!0MxO1G1PO#BpQ?MtO'#CiO#DkQ?MtO1G1`O#DrQ?MtO'#JsO#EVQ!0MxO,5?[OOQ!0Lb-E<n-E<nO#GdQ!0MxO1G1PO#HaQ!0MzO1G1POOQ!0Lf1G1P1G1PO#IdQMjO'#J|O#InQ`O,5:xO#IsQ!0MxO1G1cO#JgQ,UO,5<WO#JoQ,UO,5<XO#JwQ,UO'#FoO#K`Q`O'#FnOOQO'#KY'#KYOOQO'#In'#InO#KeQ,UO1G1nOOQ!0Lf1G1n1G1nOOOW1G1y1G1yO#KvQ?MtO'#JrO#LQQ`O,5<bO!)[QlO,5<bOOOW-E<m-E<mOOQ!0Lf1G1l1G1lO#LVQpO'#KXOOQ!0Lf,5<d,5<dO#L_QpO,5<dO#LdQMhO'#DTOOOO'#Ib'#IbO#LkO#@ItO,59mOOQ!0Lh,59m,59mO%[QlO1G2PO!8lQ`O'#IrO#LvQ`O,5<zOOQ!0Lh,5<w,5<wO!,TQMhO'#IuO#MdQMjO,5=XO!,TQMhO'#IwO#NVQMjO,5=ZO!&zQMhO,5=]OOQO1G2S1G2SO#NaQ!dO'#CrO#NtQ(CWO'#ErO$ |QpO'#GbO$!dQ!dO,5<sO$!kQ`O'#K[O9eQ`O'#K[O$!yQ`O,5<uO$#aQ!dO'#C{O!,TQMhO,5<tO$#kQ`O'#GZO$$PQ`O,5<tO$$UQ!dO'#GWO$$cQ!dO'#K]O$$mQ`O'#K]O!&zQMhO'#K]O$$rQ`O,5<xO$$wQlO'#JvO$%RQpO'#GcO#$`QpO'#GcO$%dQ`O'#GgO!3oQ`O'#GkO$%iQ!0LrO'#ItO$%tQpO,5<|OOQ!0Lp,5<|,5<|O$%{QpO'#GcO$&YQpO'#GdO$&kQpO'#GdO$&pQMjO,5=XO$'QQMjO,5=ZOOQ!0Lh,5=^,5=^O!,TQMhO,5@VO!,TQMhO,5@VO$'bQ`O'#IyO$'vQ`O,5@UO$(OQ`O,59aOOQ!0Lh,59i,59iO$(TQ`O,5@VO$)TQ$IYO,59uOOQ!0Lh'#Jp'#JpO$)vQMjO,5<kO$*iQMjO,5<mO@zQ`O,5<oOOQ!0Lh,5<p,5<pO$*sQ`O,5<vO$*xQMjO,5<{O$+YQ`O'#KPO!$wQlO1G2RO$+_Q`O1G2RO9eQ`O'#KSO9eQ`O'#EtO%[QlO'#EtO9eQ`O'#I{O$+dQ!0LrO,5@{OOQ[1G2}1G2}OOQ[1G4`1G4`OOQ!0Lf1G/|1G/|OOQ!0Lf1G/z1G/zO$-fQ!0MxO1G0UOOQ[1G2y1G2yO!&zQMhO1G2yO%[QlO1G2yO#.tQ`O1G2yO$/jQMhO'#EkOOQ!0Lb,5@T,5@TO$/wQ!0LrO,5@TOOQ[1G.u1G.uO!ByQ!0LrO1G.uO!CUQpO1G.uO!C^QMhO1G.uO$0YQ`O1G0uO$0_Q`O'#CiO$0jQ`O'#KeO$0rQ`O,5=|O$0wQ`O'#KeO$0|Q`O'#KeO$1[Q`O'#JRO$1jQ`O,5AOO$1rQ!fO1G1iOOQ!0Lf1G1k1G1kO9kQ`O1G3fO@zQ`O1G3fO$1yQ`O1G3fO$2OQ`O1G3fO!DiQ`O1G3fO9uQ!0LrO1G3fOOQ[1G3f1G3fO!EcQ`O1G3UO!&zQMhO1G3RO$2TQ`O1G3ROOQ[1G3S1G3SO!&zQMhO1G3SO$2YQ`O1G3SO$2bQpO'#HQOOQ[1G3U1G3UO!6_QpO'#I}O!EhQ!bO1G3XOOQ[1G3X1G3XOOQ[,5=r,5=rO$2jQMhO,5=tO9kQ`O,5=tO$%dQ`O,5=vO9`Q`O,5=vO!CUQpO,5=vO!C^QMhO,5=vO:dQMhO,5=vO$2xQ`O'#KcO$3TQ`O,5=wOOQ[1G.k1G.kO$3YQ!0LrO1G.kO@zQ`O1G.kO$3eQ`O1G.kO9uQ!0LrO1G.kO$5mQ!fO,5AQO$5zQ`O,5AQO9eQ`O,5AQO$6VQlO,5>PO$6^Q`O,5>POOQ[1G3i1G3iO`QlO1G3iOOQ[1G3o1G3oOOQ[1G3q1G3qO?TQ`O1G3sO$6cQlO1G3uO$:gQlO'#HtOOQ[1G3x1G3xO$:tQ`O'#HzO?YQ`O'#H|OOQ[1G4O1G4OO$:|QlO1G4OO9uQ!0LrO1G4UOOQ[1G4W1G4WOOQ!0Lb'#G_'#G_O9uQ!0LrO1G4YO9uQ!0LrO1G4[O$?TQ`O,5@bO!)[QlO,5;`O9eQ`O,5;`O?YQ`O,5:XO!)[QlO,5:XO!CUQpO,5:XO$?YQ?MtO,5:XOOQO,5;`,5;`O$?dQpO'#IeO$?zQ`O,5@aOOQ!0Lf1G/r1G/rO$@SQpO'#IkO$@^Q`O,5@pOOQ!0Lb1G0y1G0yO#$`QpO,5:XOOQO'#Ig'#IgO$@fQpO,5:qOOQ!0Ln,5:q,5:qO#(ZQ`O1G0ZOOQ!0Lf1G0Z1G0ZO%[QlO1G0ZOOQ!0Lf1G0t1G0tO?YQ`O1G0tO!CUQpO1G0tO!C^QMhO1G0tOOQ!0Lb1G5|1G5|O!ByQ!0LrO1G0^OOQO1G0m1G0mO%[QlO1G0mO$@mQ!0LrO1G0mO$@xQ!0LrO1G0mO!CUQpO1G0^ODWQpO1G0^O$AWQ!0LrO1G0mOOQO1G0^1G0^O$AlQ!0MxO1G0mPOOO-E<[-E<[POOO1G.h1G.hOOOO1G/i1G/iO$AvQ!bO,5<iO$BOQ!fO1G4jOOQO1G4p1G4pO%[QlO,5?OO$BYQ`O1G5zO$BbQ`O1G6YO$BjQ!fO1G6ZO9eQ`O,5?UO$BtQ!0MxO1G6WO%[QlO1G6WO$CUQ!0LrO1G6WO$CgQ`O1G6VO$CgQ`O1G6VO9eQ`O1G6VO$CoQ`O,5?XO9eQ`O,5?XOOQO,5?X,5?XO$DTQ`O,5?XO$+YQ`O,5?XOOQO-E<k-E<kOOQS1G0a1G0aOOQS1G0c1G0cO#.lQ`O1G0cOOQ[7+(e7+(eO!&zQMhO7+(eO%[QlO7+(eO$DcQ`O7+(eO$DnQMhO7+(eO$D|Q!0MzO,5=XO$GXQ!0MzO,5=ZO$IdQ!0MzO,5=XO$KuQ!0MzO,5=ZO$NWQ!0MzO,59uO%!]Q!0MzO,5<kO%$hQ!0MzO,5<mO%&sQ!0MzO,5<{OOQ!0Lf7+&a7+&aO%)UQ!0MxO7+&aO%)xQlO'#IfO%*VQ`O,5@cO%*_Q!fO,5@cOOQ!0Lf1G0P1G0PO%*iQ`O7+&jOOQ!0Lf7+&j7+&jO%*nQ?MtO,5:fO%[QlO7+&zO%*xQ?MtO,5:bO%+VQ?MtO,5:jO%+aQ?MtO,5:lO%+kQMhO'#IiO%+uQ`O,5@hOOQ!0Lh1G0d1G0dOOQO1G1r1G1rOOQO1G1s1G1sO%+}Q!jO,5<ZO!)[QlO,5<YOOQO-E<l-E<lOOQ!0Lf7+'Y7+'YOOOW7+'e7+'eOOOW1G1|1G1|O%,YQ`O1G1|OOQ!0Lf1G2O1G2OOOOO,59o,59oO%,_Q!dO,59oOOOO-E<`-E<`OOQ!0Lh1G/X1G/XO%,fQ!0MxO7+'kOOQ!0Lh,5?^,5?^O%-YQMhO1G2fP%-aQ`O'#IrPOQ!0Lh-E<p-E<pO%-}QMjO,5?aOOQ!0Lh-E<s-E<sO%.pQMjO,5?cOOQ!0Lh-E<u-E<uO%.zQ!dO1G2wO%/RQ!dO'#CrO%/iQMhO'#KSO$$wQlO'#JvOOQ!0Lh1G2_1G2_O%/sQ`O'#IqO%0[Q`O,5@vO%0[Q`O,5@vO%0dQ`O,5@vO%0oQ`O,5@vOOQO1G2a1G2aO%0}QMjO1G2`O$+YQ`O'#K[O!,TQMhO1G2`O%1_Q(CWO'#IsO%1lQ`O,5@wO!&zQMhO,5@wO%1tQ!dO,5@wOOQ!0Lh1G2d1G2dO%4UQ!fO'#CiO%4`Q`O,5=POOQ!0Lb,5<},5<}O%4hQpO,5<}OOQ!0Lb,5=O,5=OOCwQ`O,5<}O%4sQpO,5<}OOQ!0Lb,5=R,5=RO$+YQ`O,5=VOOQO,5?`,5?`OOQO-E<r-E<rOOQ!0Lp1G2h1G2hO#$`QpO,5<}O$$wQlO,5=PO%5RQ`O,5=OO%5^QpO,5=OO!,TQMhO'#IuO%6WQMjO1G2sO!,TQMhO'#IwO%6yQMjO1G2uO%7TQMjO1G5qO%7_QMjO1G5qOOQO,5?e,5?eOOQO-E<w-E<wOOQO1G.{1G.{O!,TQMhO1G5qO!,TQMhO1G5qO!:]QpO,59wO%[QlO,59wOOQ!0Lh,5<j,5<jO%7lQ`O1G2ZO!,TQMhO1G2bO%7qQ!0MxO7+'mOOQ!0Lf7+'m7+'mO!$wQlO7+'mO%8eQ`O,5;`OOQ!0Lb,5?g,5?gOOQ!0Lb-E<y-E<yO%8jQ!dO'#K^O#(ZQ`O7+(eO4UQ!fO7+(eO$DfQ`O7+(eO%8tQ!0MvO'#CiO%9XQ!0MvO,5=SO%9lQ`O,5=SO%9tQ`O,5=SOOQ!0Lb1G5o1G5oOOQ[7+$a7+$aO!ByQ!0LrO7+$aO!CUQpO7+$aO!$wQlO7+&aO%9yQ`O'#JQO%:bQ`O,5APOOQO1G3h1G3hO9kQ`O,5APO%:bQ`O,5APO%:jQ`O,5APOOQO,5?m,5?mOOQO-E=P-E=POOQ!0Lf7+'T7+'TO%:oQ`O7+)QO9uQ!0LrO7+)QO9kQ`O7+)QO@zQ`O7+)QO%:tQ`O7+)QOOQ[7+)Q7+)QOOQ[7+(p7+(pO%:yQ!0MvO7+(mO!&zQMhO7+(mO!E^Q`O7+(nOOQ[7+(n7+(nO!&zQMhO7+(nO%;TQ`O'#KbO%;`Q`O,5=lOOQO,5?i,5?iOOQO-E<{-E<{OOQ[7+(s7+(sO%<rQpO'#HZOOQ[1G3`1G3`O!&zQMhO1G3`O%[QlO1G3`O%<yQ`O1G3`O%=UQMhO1G3`O9uQ!0LrO1G3bO$%dQ`O1G3bO9`Q`O1G3bO!CUQpO1G3bO!C^QMhO1G3bO%=dQ`O'#JPO%=xQ`O,5@}O%>QQpO,5@}OOQ!0Lb1G3c1G3cOOQ[7+$V7+$VO@zQ`O7+$VO9uQ!0LrO7+$VO%>]Q`O7+$VO%[QlO1G6lO%[QlO1G6mO%>bQ!0LrO1G6lO%>lQlO1G3kO%>sQ`O1G3kO%>xQlO1G3kOOQ[7+)T7+)TO9uQ!0LrO7+)_O`QlO7+)aOOQ['#Kh'#KhOOQ['#JS'#JSO%?PQlO,5>`OOQ[,5>`,5>`O%[QlO'#HuO%?^Q`O'#HwOOQ[,5>f,5>fO9eQ`O,5>fOOQ[,5>h,5>hOOQ[7+)j7+)jOOQ[7+)p7+)pOOQ[7+)t7+)tOOQ[7+)v7+)vO%?cQpO1G5|O%?}Q?MtO1G0zO%@XQ`O1G0zOOQO1G/s1G/sO%@dQ?MtO1G/sO?YQ`O1G/sO!)[QlO'#DmOOQO,5?P,5?POOQO-E<c-E<cOOQO,5?V,5?VOOQO-E<i-E<iO!CUQpO1G/sOOQO-E<e-E<eOOQ!0Ln1G0]1G0]OOQ!0Lf7+%u7+%uO#(ZQ`O7+%uOOQ!0Lf7+&`7+&`O?YQ`O7+&`O!CUQpO7+&`OOQO7+%x7+%xO$AlQ!0MxO7+&XOOQO7+&X7+&XO%[QlO7+&XO%@nQ!0LrO7+&XO!ByQ!0LrO7+%xO!CUQpO7+%xO%@yQ!0LrO7+&XO%AXQ!0MxO7++rO%[QlO7++rO%AiQ`O7++qO%AiQ`O7++qOOQO1G4s1G4sO9eQ`O1G4sO%AqQ`O1G4sOOQS7+%}7+%}O#(ZQ`O<<LPO4UQ!fO<<LPO%BPQ`O<<LPOOQ[<<LP<<LPO!&zQMhO<<LPO%[QlO<<LPO%BXQ`O<<LPO%BdQ!0MzO,5?aO%DoQ!0MzO,5?cO%FzQ!0MzO1G2`O%I]Q!0MzO1G2sO%KhQ!0MzO1G2uO%MsQ!fO,5?QO%[QlO,5?QOOQO-E<d-E<dO%M}Q`O1G5}OOQ!0Lf<<JU<<JUO%NVQ?MtO1G0uO&!^Q?MtO1G1PO&!eQ?MtO1G1PO&$fQ?MtO1G1PO&$mQ?MtO1G1PO&&nQ?MtO1G1PO&(oQ?MtO1G1PO&(vQ?MtO1G1PO&(}Q?MtO1G1PO&+OQ?MtO1G1PO&+VQ?MtO1G1PO&+^Q!0MxO<<JfO&-UQ?MtO1G1PO&.RQ?MvO1G1PO&/UQ?MvO'#JlO&1[Q?MtO1G1cO&1iQ?MtO1G0UO&1sQMjO,5?TOOQO-E<g-E<gO!)[QlO'#FqOOQO'#KZ'#KZOOQO1G1u1G1uO&1}Q`O1G1tO&2SQ?MtO,5?[OOOW7+'h7+'hOOOO1G/Z1G/ZO&2^Q!dO1G4xOOQ!0Lh7+(Q7+(QP!&zQMhO,5?^O!,TQMhO7+(cO&2eQ`O,5?]O9eQ`O,5?]O$+YQ`O,5?]OOQO-E<o-E<oO&2sQ`O1G6bO&2sQ`O1G6bO&2{Q`O1G6bO&3WQMjO7+'zO&3hQ!dO,5?_O&3rQ`O,5?_O!&zQMhO,5?_OOQO-E<q-E<qO&3wQ!dO1G6cO&4RQ`O1G6cO&4ZQ`O1G2kO!&zQMhO1G2kOOQ!0Lb1G2i1G2iOOQ!0Lb1G2j1G2jO%4hQpO1G2iO!CUQpO1G2iOCwQ`O1G2iOOQ!0Lb1G2q1G2qO&4`QpO1G2iO&4nQ`O1G2kO$+YQ`O1G2jOCwQ`O1G2jO$$wQlO1G2kO&4vQ`O1G2jO&5jQMjO,5?aOOQ!0Lh-E<t-E<tO&6]QMjO,5?cOOQ!0Lh-E<v-E<vO!,TQMhO7++]O&6gQMjO7++]O&6qQMjO7++]OOQ!0Lh1G/c1G/cO&7OQ`O1G/cOOQ!0Lh7+'u7+'uO&7TQMjO7+'|O&7eQ!0MxO<<KXOOQ!0Lf<<KX<<KXO&8XQ`O1G0zO!&zQMhO'#IzO&8^Q`O,5@xO&:`Q!fO<<LPO!&zQMhO1G2nO&:gQ!0LrO1G2nOOQ[<<G{<<G{O!ByQ!0LrO<<G{O&:xQ!0MxO<<I{OOQ!0Lf<<I{<<I{OOQO,5?l,5?lO&;lQ`O,5?lO&;qQ`O,5?lOOQO-E=O-E=OO&<PQ`O1G6kO&<PQ`O1G6kO9kQ`O1G6kO@zQ`O<<LlOOQ[<<Ll<<LlO&<XQ`O<<LlO9uQ!0LrO<<LlO9kQ`O<<LlOOQ[<<LX<<LXO%:yQ!0MvO<<LXOOQ[<<LY<<LYO!E^Q`O<<LYO&<^QpO'#I|O&<iQ`O,5@|O!)[QlO,5@|OOQ[1G3W1G3WOOQO'#JO'#JOO9uQ!0LrO'#JOO&<qQpO,5=uOOQ[,5=u,5=uO&<xQpO'#EgO&=PQpO'#GeO&=UQ`O7+(zO&=ZQ`O7+(zOOQ[7+(z7+(zO!&zQMhO7+(zO%[QlO7+(zO&=cQ`O7+(zOOQ[7+(|7+(|O9uQ!0LrO7+(|O$%dQ`O7+(|O9`Q`O7+(|O!CUQpO7+(|O&=nQ`O,5?kOOQO-E<}-E<}OOQO'#H^'#H^O&=yQ`O1G6iO9uQ!0LrO<<GqOOQ[<<Gq<<GqO@zQ`O<<GqO&>RQ`O7+,WO&>WQ`O7+,XO%[QlO7+,WO%[QlO7+,XOOQ[7+)V7+)VO&>]Q`O7+)VO&>bQlO7+)VO&>iQ`O7+)VOOQ[<<Ly<<LyOOQ[<<L{<<L{OOQ[-E=Q-E=QOOQ[1G3z1G3zO&>nQ`O,5>aOOQ[,5>c,5>cO&>sQ`O1G4QO9eQ`O7+&fO!)[QlO7+&fOOQO7+%_7+%_O&>xQ?MtO1G6ZO?YQ`O7+%_OOQ!0Lf<<Ia<<IaOOQ!0Lf<<Iz<<IzO?YQ`O<<IzOOQO<<Is<<IsO$AlQ!0MxO<<IsO%[QlO<<IsOOQO<<Id<<IdO!ByQ!0LrO<<IdO&?SQ!0LrO<<IsO&?_Q!0MxO<= ^O&?oQ`O<= ]OOQO7+*_7+*_O9eQ`O7+*_OOQ[ANAkANAkO&?wQ!fOANAkO!&zQMhOANAkO#(ZQ`OANAkO4UQ!fOANAkO&@OQ`OANAkO%[QlOANAkO&@WQ!0MzO7+'zO&BiQ!0MzO,5?aO&DtQ!0MzO,5?cO&GPQ!0MzO7+'|O&IbQ!fO1G4lO&IlQ?MtO7+&aO&KpQ?MvO,5=XO&MwQ?MvO,5=ZO&NXQ?MvO,5=XO&NiQ?MvO,5=ZO&NyQ?MvO,59uO'#PQ?MvO,5<kO'%SQ?MvO,5<mO''hQ?MvO,5<{O')^Q?MtO7+'kO')kQ?MtO7+'mO')xQ`O,5<]OOQO7+'`7+'`OOQ!0Lh7+*d7+*dO')}QMjO<<K}OOQO1G4w1G4wO'*UQ`O1G4wO'*aQ`O1G4wO'*oQ`O7++|O'*oQ`O7++|O!&zQMhO1G4yO'*wQ!dO1G4yO'+RQ`O7++}O'+ZQ`O7+(VO'+fQ!dO7+(VOOQ!0Lb7+(T7+(TOOQ!0Lb7+(U7+(UO!CUQpO7+(TOCwQ`O7+(TO'+pQ`O7+(VO!&zQMhO7+(VO$+YQ`O7+(UO'+uQ`O7+(VOCwQ`O7+(UO'+}QMjO<<NwO!,TQMhO<<NwOOQ!0Lh7+$}7+$}O',XQ!dO,5?fOOQO-E<x-E<xO',cQ!0MvO7+(YO!&zQMhO7+(YOOQ[AN=gAN=gO9kQ`O1G5WOOQO1G5W1G5WO',sQ`O1G5WO',xQ`O7+,VO',xQ`O7+,VO9uQ!0LrOANBWO@zQ`OANBWOOQ[ANBWANBWO'-QQ`OANBWOOQ[ANAsANAsOOQ[ANAtANAtO'-VQ`O,5?hOOQO-E<z-E<zO'-bQ?MtO1G6hOOQO,5?j,5?jOOQO-E<|-E<|OOQ[1G3a1G3aO'-lQ`O,5=POOQ[<<Lf<<LfO!&zQMhO<<LfO&=UQ`O<<LfO'-qQ`O<<LfO%[QlO<<LfOOQ[<<Lh<<LhO9uQ!0LrO<<LhO$%dQ`O<<LhO9`Q`O<<LhO'-yQpO1G5VO'.UQ`O7+,TOOQ[AN=]AN=]O9uQ!0LrOAN=]OOQ[<= r<= rOOQ[<= s<= sO'.^Q`O<= rO'.cQ`O<= sOOQ[<<Lq<<LqO'.hQ`O<<LqO'.mQlO<<LqOOQ[1G3{1G3{O?YQ`O7+)lO'.tQ`O<<JQO'/PQ?MtO<<JQOOQO<<Hy<<HyOOQ!0LfAN?fAN?fOOQOAN?_AN?_O$AlQ!0MxOAN?_OOQOAN?OAN?OO%[QlOAN?_OOQO<<My<<MyOOQ[G27VG27VO!&zQMhOG27VO#(ZQ`OG27VO'/ZQ!fOG27VO4UQ!fOG27VO'/bQ`OG27VO'/jQ?MtO<<JfO'/wQ?MvO1G2`O'1mQ?MvO,5?aO'3pQ?MvO,5?cO'5sQ?MvO1G2sO'7vQ?MvO1G2uO'9yQ?MtO<<KXO':WQ?MtO<<I{OOQO1G1w1G1wO!,TQMhOANAiOOQO7+*c7+*cO':eQ`O7+*cO':pQ`O<= hO':xQ!dO7+*eOOQ!0Lb<<Kq<<KqO$+YQ`O<<KqOCwQ`O<<KqO';SQ`O<<KqO!&zQMhO<<KqOOQ!0Lb<<Ko<<KoO!CUQpO<<KoO';_Q!dO<<KqOOQ!0Lb<<Kp<<KpO';iQ`O<<KqO!&zQMhO<<KqO$+YQ`O<<KpO';nQMjOANDcO';xQ!0MvO<<KtOOQO7+*r7+*rO9kQ`O7+*rO'<YQ`O<= qOOQ[G27rG27rO9uQ!0LrOG27rO@zQ`OG27rO!)[QlO1G5SO'<bQ`O7+,SO'<jQ`O1G2kO&=UQ`OANBQOOQ[ANBQANBQO!&zQMhOANBQO'<oQ`OANBQOOQ[ANBSANBSO9uQ!0LrOANBSO$%dQ`OANBSOOQO'#H_'#H_OOQO7+*q7+*qOOQ[G22wG22wOOQ[ANE^ANE^OOQ[ANE_ANE_OOQ[ANB]ANB]O'<wQ`OANB]OOQ[<<MW<<MWO!)[QlOAN?lOOQOG24yG24yO$AlQ!0MxOG24yO#(ZQ`OLD,qOOQ[LD,qLD,qO!&zQMhOLD,qO'<|Q!fOLD,qO'=TQ?MvO7+'zO'>yQ?MvO,5?aO'@|Q?MvO,5?cO'CPQ?MvO7+'|O'DuQMjOG27TOOQO<<M}<<M}OOQ!0LbANA]ANA]O$+YQ`OANA]OCwQ`OANA]O'EVQ!dOANA]OOQ!0LbANAZANAZO'E^Q`OANA]O!&zQMhOANA]O'EiQ!dOANA]OOQ!0LbANA[ANA[OOQO<<N^<<N^OOQ[LD-^LD-^O9uQ!0LrOLD-^O'EsQ?MtO7+*nOOQO'#Gf'#GfOOQ[G27lG27lO&=UQ`OG27lO!&zQMhOG27lOOQ[G27nG27nO9uQ!0LrOG27nOOQ[G27wG27wO'E}Q?MtOG25WOOQOLD*eLD*eOOQ[!$(!]!$(!]O#(ZQ`O!$(!]O!&zQMhO!$(!]O'FXQ!0MzOG27TOOQ!0LbG26wG26wO$+YQ`OG26wO'HjQ`OG26wOCwQ`OG26wO'HuQ!dOG26wO!&zQMhOG26wOOQ[!$(!x!$(!xOOQ[LD-WLD-WO&=UQ`OLD-WOOQ[LD-YLD-YOOQ[!)9Ew!)9EwO#(ZQ`O!)9EwOOQ!0LbLD,cLD,cO$+YQ`OLD,cOCwQ`OLD,cO'H|Q`OLD,cO'IXQ!dOLD,cOOQ[!$(!r!$(!rOOQ[!.K;c!.K;cO'I`Q?MvOG27TOOQ!0Lb!$( }!$( }O$+YQ`O!$( }OCwQ`O!$( }O'KUQ`O!$( }OOQ!0Lb!)9Ei!)9EiO$+YQ`O!)9EiOCwQ`O!)9EiOOQ!0Lb!.K;T!.K;TO$+YQ`O!.K;TOOQ!0Lb!4/0o!4/0oO!)[QlO'#DzO1PQ`O'#EXO'KaQ!fO'#JrO'KhQ!L^O'#DvO'KoQlO'#EOO'KvQ!fO'#CiO'N^Q!fO'#CiO!)[QlO'#EQO'NnQlO,5;ZO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO'#IpO(!qQ`O,5<iO!)[QlO,5;eO(!yQMhO,5;eO($dQMhO,5;eO!)[QlO,5;wO!&zQMhO'#GmO(!yQMhO'#GmO!&zQMhO'#GoO(!yQMhO'#GoO1SQ`O'#DZO1SQ`O'#DZO!&zQMhO'#GPO(!yQMhO'#GPO!&zQMhO'#GRO(!yQMhO'#GRO!&zQMhO'#GaO(!yQMhO'#GaO!)[QlO,5:jO($kQpO'#D_O($uQpO'#JvO!)[QlO,5@oO'NnQlO1G0uO(%PQ?MtO'#CiO!)[QlO1G2PO!&zQMhO'#IuO(!yQMhO'#IuO!&zQMhO'#IwO(!yQMhO'#IwO(%ZQ!dO'#CrO!&zQMhO,5<tO(!yQMhO,5<tO'NnQlO1G2RO!)[QlO7+&zO!&zQMhO1G2`O(!yQMhO1G2`O!&zQMhO'#IuO(!yQMhO'#IuO!&zQMhO'#IwO(!yQMhO'#IwO!&zQMhO1G2bO(!yQMhO1G2bO'NnQlO7+'mO'NnQlO7+&aO!&zQMhOANAiO(!yQMhOANAiO(%nQ`O'#EoO(%sQ`O'#EoO(%{Q`O'#F]O(&QQ`O'#EyO(&VQ`O'#KTO(&bQ`O'#KRO(&mQ`O,5;ZO(&rQMjO,5<eO(&yQ`O'#GYO('OQ`O'#GYO('TQ`O,5<eO(']Q`O,5<gO('eQ`O,5;ZO('mQ?MtO1G1`O('tQ`O,5<tO('yQ`O,5<tO((OQ`O,5<vO((TQ`O,5<vO((YQ`O1G2RO((_Q`O1G0uO((dQMjO<<K}O((kQMjO<<K}O((rQMhO'#F|O9`Q`O'#F{OAuQ`O'#EnO!)[QlO,5;tO!3oQ`O'#GYO!3oQ`O'#GYO!3oQ`O'#G[O!3oQ`O'#G[O!,TQMhO7+(cO!,TQMhO7+(cO%.zQ!dO1G2wO%.zQ!dO1G2wO!&zQMhO,5=]O!&zQMhO,5=]",stateData:"()x~O'|OS'}OSTOS(ORQ~OPYOQYOSfOY!VOaqOdzOeyOl!POpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_XO!iuO!lZO!oYO!pYO!qYO!svO!uwO!xxO!|]O$W|O$niO%h}O%j!QO%l!OO%m!OO%n!OO%q!RO%s!SO%v!TO%w!TO%y!UO&W!WO&^!XO&`!YO&b!ZO&d![O&g!]O&m!^O&s!_O&u!`O&w!aO&y!bO&{!cO(TSO(VTO(YUO(aVO(o[O~OWtO~P`OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(T!dO(VTO(YUO(aVO(o[O~Oa!wOs!nO!S!oO!b!yO!c!vO!d!vO!|<VO#T!pO#U!pO#V!xO#W!pO#X!pO#[!zO#]!zO(U!lO(VTO(YUO(e!mO(o!sO~O(O!{O~OP]XR]X[]Xa]Xj]Xr]X!Q]X!S]X!]]X!l]X!p]X#R]X#S]X#`]X#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X'z]X(a]X(r]X(y]X(z]X~O!g%RX~P(qO_!}O(V#PO(W!}O(X#PO~O_#QO(X#PO(Y#PO(Z#QO~Ox#SO!U#TO(b#TO(c#VO~OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(T<ZO(VTO(YUO(aVO(o[O~O![#ZO!]#WO!Y(hP!Y(vP~P+}O!^#cO~P`OPYOQYOSfOd!jOe!iOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(VTO(YUO(aVO(o[O~Op#mO![#iO!|]O#i#lO#j#iO(T<[O!k(sP~P.iO!l#oO(T#nO~O!x#sO!|]O%h#tO~O#k#uO~O!g#vO#k#uO~OP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!]$_O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO#z$WO#{$XO(aVO(r$YO(y#|O(z#}O~Oa(fX'z(fX'w(fX!k(fX!Y(fX!_(fX%i(fX!g(fX~P1qO#S$dO#`$eO$Q$eOP(gXR(gX[(gXj(gXr(gX!Q(gX!S(gX!](gX!l(gX!p(gX#R(gX#n(gX#o(gX#p(gX#q(gX#r(gX#s(gX#t(gX#u(gX#v(gX#x(gX#z(gX#{(gX(a(gX(r(gX(y(gX(z(gX!_(gX%i(gX~Oa(gX'z(gX'w(gX!Y(gX!k(gXv(gX!g(gX~P4UO#`$eO~O$]$hO$_$gO$f$mO~OSfO!_$nO$i$oO$k$qO~Oh%VOj%dOk%dOp%WOr%XOs$tOt$tOz%YO|%ZO!O%]O!S${O!_$|O!i%bO!l$xO#j%cO$W%`O$t%^O$v%_O$y%aO(T$sO(VTO(YUO(a$uO(y$}O(z%POg(^P~Ol%[O~P7eO!l%eO~O!S%hO!_%iO(T%gO~O!g%mO~Oa%nO'z%nO~O!Q%rO~P%[O(U!lO~P%[O%n%vO~P%[Oh%VO!l%eO(T%gO(U!lO~Oe%}O!l%eO(T%gO~Oj$RO~O!_&PO(T%gO(U!lO(VTO(YUO`)WP~O!Q&SO!l&RO%j&VO&T&WO~P;SO!x#sO~O%s&YO!S)SX!_)SX(T)SX~O(T&ZO~Ol!PO!u&`O%j!QO%l!OO%m!OO%n!OO%q!RO%s!SO%v!TO%w!TO~Od&eOe&dO!x&bO%h&cO%{&aO~P<bOd&hOeyOl!PO!_&gO!u&`O!xxO!|]O%h}O%l!OO%m!OO%n!OO%q!RO%s!SO%v!TO%w!TO%y!UO~Ob&kO#`&nO%j&iO(U!lO~P=gO!l&oO!u&sO~O!l#oO~O!_XO~Oa%nO'x&{O'z%nO~Oa%nO'x'OO'z%nO~Oa%nO'x'QO'z%nO~O'w]X!Y]Xv]X!k]X&[]X!_]X%i]X!g]X~P(qO!b'_O!c'WO!d'WO(U!lO(VTO(YUO~Os'UO!S'TO!['XO(e'SO!^(iP!^(xP~P@nOn'bO!_'`O(T%gO~Oe'gO!l%eO(T%gO~O!Q&SO!l&RO~Os!nO!S!oO!|<VO#T!pO#U!pO#W!pO#X!pO(U!lO(VTO(YUO(e!mO(o!sO~O!b'mO!c'lO!d'lO#V!pO#['nO#]'nO~PBYOa%nOh%VO!g#vO!l%eO'z%nO(r'pO~O!p'tO#`'rO~PChOs!nO!S!oO(VTO(YUO(e!mO(o!sO~O!_XOs(mX!S(mX!b(mX!c(mX!d(mX!|(mX#T(mX#U(mX#V(mX#W(mX#X(mX#[(mX#](mX(U(mX(V(mX(Y(mX(e(mX(o(mX~O!c'lO!d'lO(U!lO~PDWO(P'xO(Q'xO(R'zO~O_!}O(V'|O(W!}O(X'|O~O_#QO(X'|O(Y'|O(Z#QO~Ov(OO~P%[Ox#SO!U#TO(b#TO(c(RO~O![(TO!Y'WX!Y'^X!]'WX!]'^X~P+}O!](VO!Y(hX~OP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!](VO!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO#z$WO#{$XO(aVO(r$YO(y#|O(z#}O~O!Y(hX~PHRO!Y([O~O!Y(uX!](uX!g(uX!k(uX(r(uX~O#`(uX#k#dX!^(uX~PJUO#`(]O!Y(wX!](wX~O!](^O!Y(vX~O!Y(aO~O#`$eO~PJUO!^(bO~P`OR#zO!Q#yO!S#{O!l#xO(aVOP!na[!naj!nar!na!]!na!p!na#R!na#n!na#o!na#p!na#q!na#r!na#s!na#t!na#u!na#v!na#x!na#z!na#{!na(r!na(y!na(z!na~Oa!na'z!na'w!na!Y!na!k!nav!na!_!na%i!na!g!na~PKlO!k(cO~O!g#vO#`(dO(r'pO!](tXa(tX'z(tX~O!k(tX~PNXO!S%hO!_%iO!|]O#i(iO#j(hO(T%gO~O!](jO!k(sX~O!k(lO~O!S%hO!_%iO#j(hO(T%gO~OP(gXR(gX[(gXj(gXr(gX!Q(gX!S(gX!](gX!l(gX!p(gX#R(gX#n(gX#o(gX#p(gX#q(gX#r(gX#s(gX#t(gX#u(gX#v(gX#x(gX#z(gX#{(gX(a(gX(r(gX(y(gX(z(gX~O!g#vO!k(gX~P! uOR(nO!Q(mO!l#xO#S$dO!|!{a!S!{a~O!x!{a%h!{a!_!{a#i!{a#j!{a(T!{a~P!#vO!x(rO~OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_XO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(T!dO(VTO(YUO(aVO(o[O~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<sO!S${O!_$|O!i>VO!l$xO#j<yO$W%`O$t<uO$v<wO$y%aO(T(vO(VTO(YUO(a$uO(y$}O(z%PO~O#k(xO~O![(zO!k(kP~P%[O(e(|O(o[O~O!S)OO!l#xO(e(|O(o[O~OP<UOQ<UOSfOd>ROe!iOpkOr<UOskOtkOzkO|<UO!O<UO!SWO!WkO!XkO!_!eO!i<XO!lZO!o<UO!p<UO!q<UO!s<YO!u<]O!x!hO$W!kO$n>PO(T)]O(VTO(YUO(aVO(o[O~O!]$_Oa$qa'z$qa'w$qa!k$qa!Y$qa!_$qa%i$qa!g$qa~Ol)dO~P!&zOh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O%]O!S${O!_$|O!i%bO!l$xO#j%cO$W%`O$t%^O$v%_O$y%aO(T(vO(VTO(YUO(a$uO(y$}O(z%PO~Og(pP~P!,TO!Q)iO!g)hO!_$^X$Z$^X$]$^X$_$^X$f$^X~O!g)hO!_({X$Z({X$]({X$_({X$f({X~O!Q)iO~P!.^O!Q)iO!_({X$Z({X$]({X$_({X$f({X~O!_)kO$Z)oO$])jO$_)jO$f)pO~O![)sO~P!)[O$]$hO$_$gO$f)wO~On$zX!Q$zX#S$zX'y$zX(y$zX(z$zX~OgmXg$zXnmX!]mX#`mX~P!0SOx)yO(b)zO(c)|O~On*VO!Q*OO'y*PO(y$}O(z%PO~Og)}O~P!1WOg*WO~Oh%VOr%XOs$tOt$tOz%YO|%ZO!O<sO!S*YO!_*ZO!i>VO!l$xO#j<yO$W%`O$t<uO$v<wO$y%aO(VTO(YUO(a$uO(y$}O(z%PO~Op*`O![*^O(T*XO!k)OP~P!1uO#k*aO~O!l*bO~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<sO!S${O!_$|O!i>VO!l$xO#j<yO$W%`O$t<uO$v<wO$y%aO(T*dO(VTO(YUO(a$uO(y$}O(z%PO~O![*gO!Y)PP~P!3tOr*sOs!nO!S*iO!b*qO!c*kO!d*kO!l*bO#[*rO%`*mO(U!lO(VTO(YUO(e!mO~O!^*pO~P!5iO#S$dOn(`X!Q(`X'y(`X(y(`X(z(`X!](`X#`(`X~Og(`X$O(`X~P!6kOn*xO#`*wOg(_X!](_X~O!]*yOg(^X~Oj%dOk%dOl%dO(T&ZOg(^P~Os*|O~Og)}O(T&ZO~O!l+SO~O(T(vO~Op+WO!S%hO![#iO!_%iO!|]O#i#lO#j#iO(T%gO!k(sP~O!g#vO#k+XO~O!S%hO![+ZO!](^O!_%iO(T%gO!Y(vP~Os'[O!S+]O![+[O(VTO(YUO(e(|O~O!^(xP~P!9|O!]+^Oa)TX'z)TX~OP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO#z$WO#{$XO(aVO(r$YO(y#|O(z#}O~Oa!ja!]!ja'z!ja'w!ja!Y!ja!k!jav!ja!_!ja%i!ja!g!ja~P!:tOR#zO!Q#yO!S#{O!l#xO(aVOP!ra[!raj!rar!ra!]!ra!p!ra#R!ra#n!ra#o!ra#p!ra#q!ra#r!ra#s!ra#t!ra#u!ra#v!ra#x!ra#z!ra#{!ra(r!ra(y!ra(z!ra~Oa!ra'z!ra'w!ra!Y!ra!k!rav!ra!_!ra%i!ra!g!ra~P!=[OR#zO!Q#yO!S#{O!l#xO(aVOP!ta[!taj!tar!ta!]!ta!p!ta#R!ta#n!ta#o!ta#p!ta#q!ta#r!ta#s!ta#t!ta#u!ta#v!ta#x!ta#z!ta#{!ta(r!ta(y!ta(z!ta~Oa!ta'z!ta'w!ta!Y!ta!k!tav!ta!_!ta%i!ta!g!ta~P!?rOh%VOn+gO!_'`O%i+fO~O!g+iOa(]X!_(]X'z(]X!](]X~Oa%nO!_XO'z%nO~Oh%VO!l%eO~Oh%VO!l%eO(T%gO~O!g#vO#k(xO~Ob+tO%j+uO(T+qO(VTO(YUO!^)XP~O!]+vO`)WX~O[+zO~O`+{O~O!_&PO(T%gO(U!lO`)WP~O%j,OO~P;SOh%VO#`,SO~Oh%VOn,VO!_$|O~O!_,XO~O!Q,ZO!_XO~O%n%vO~O!x,`O~Oe,eO~Ob,fO(T#nO(VTO(YUO!^)VP~Oe%}O~O%j!QO(T&ZO~P=gO[,kO`,jO~OPYOQYOSfOdzOeyOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!iuO!lZO!oYO!pYO!qYO!svO!xxO!|]O$niO%h}O(VTO(YUO(aVO(o[O~O!_!eO!u!gO$W!kO(T!dO~P!FyO`,jOa%nO'z%nO~OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!x!hO$W!kO$niO(T!dO(VTO(YUO(aVO(o[O~Oa,pOl!OO!uwO%l!OO%m!OO%n!OO~P!IcO!l&oO~O&^,vO~O!_,xO~O&o,zO&q,{OP&laQ&laS&laY&laa&lad&lae&lal&lap&lar&las&lat&laz&la|&la!O&la!S&la!W&la!X&la!_&la!i&la!l&la!o&la!p&la!q&la!s&la!u&la!x&la!|&la$W&la$n&la%h&la%j&la%l&la%m&la%n&la%q&la%s&la%v&la%w&la%y&la&W&la&^&la&`&la&b&la&d&la&g&la&m&la&s&la&u&la&w&la&y&la&{&la'w&la(T&la(V&la(Y&la(a&la(o&la!^&la&e&lab&la&j&la~O(T-QO~Oh!eX!]!RX!^!RX!g!RX!g!eX!l!eX#`!RX~O!]!eX!^!eX~P#!iO!g-VO#`-UOh(jX!]#hX!^#hX!g(jX!l(jX~O!](jX!^(jX~P##[Oh%VO!g-XO!l%eO!]!aX!^!aX~Os!nO!S!oO(VTO(YUO(e!mO~OP<UOQ<UOSfOd>ROe!iOpkOr<UOskOtkOzkO|<UO!O<UO!SWO!WkO!XkO!_!eO!i<XO!lZO!o<UO!p<UO!q<UO!s<YO!u<]O!x!hO$W!kO$n>PO(VTO(YUO(aVO(o[O~O(T=QO~P#$qO!]-]O!^(iX~O!^-_O~O!g-VO#`-UO!]#hX!^#hX~O!]-`O!^(xX~O!^-bO~O!c-cO!d-cO(U!lO~P#$`O!^-fO~P'_On-iO!_'`O~O!Y-nO~Os!{a!b!{a!c!{a!d!{a#T!{a#U!{a#V!{a#W!{a#X!{a#[!{a#]!{a(U!{a(V!{a(Y!{a(e!{a(o!{a~P!#vO!p-sO#`-qO~PChO!c-uO!d-uO(U!lO~PDWOa%nO#`-qO'z%nO~Oa%nO!g#vO#`-qO'z%nO~Oa%nO!g#vO!p-sO#`-qO'z%nO(r'pO~O(P'xO(Q'xO(R-zO~Ov-{O~O!Y'Wa!]'Wa~P!:tO![.PO!Y'WX!]'WX~P%[O!](VO!Y(ha~O!Y(ha~PHRO!](^O!Y(va~O!S%hO![.TO!_%iO(T%gO!Y'^X!]'^X~O#`.VO!](ta!k(taa(ta'z(ta~O!g#vO~P#,wO!](jO!k(sa~O!S%hO!_%iO#j.ZO(T%gO~Op.`O!S%hO![.]O!_%iO!|]O#i._O#j.]O(T%gO!]'aX!k'aX~OR.dO!l#xO~Oh%VOn.gO!_'`O%i.fO~Oa#ci!]#ci'z#ci'w#ci!Y#ci!k#civ#ci!_#ci%i#ci!g#ci~P!:tOn>]O!Q*OO'y*PO(y$}O(z%PO~O#k#_aa#_a#`#_a'z#_a!]#_a!k#_a!_#_a!Y#_a~P#/sO#k(`XP(`XR(`X[(`Xa(`Xj(`Xr(`X!S(`X!l(`X!p(`X#R(`X#n(`X#o(`X#p(`X#q(`X#r(`X#s(`X#t(`X#u(`X#v(`X#x(`X#z(`X#{(`X'z(`X(a(`X(r(`X!k(`X!Y(`X'w(`Xv(`X!_(`X%i(`X!g(`X~P!6kO!].tO!k(kX~P!:tO!k.wO~O!Y.yO~OP$[OR#zO!Q#yO!S#{O!l#xO!p$[O(aVO[#mia#mij#mir#mi!]#mi#R#mi#o#mi#p#mi#q#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi'z#mi(r#mi(y#mi(z#mi'w#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#n#mi~P#3cO#n$OO~P#3cOP$[OR#zOr$aO!Q#yO!S#{O!l#xO!p$[O#n$OO#o$PO#p$PO#q$PO(aVO[#mia#mij#mi!]#mi#R#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi'z#mi(r#mi(y#mi(z#mi'w#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#r#mi~P#6QO#r$QO~P#6QOP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO(aVOa#mi!]#mi#x#mi#z#mi#{#mi'z#mi(r#mi(y#mi(z#mi'w#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#v#mi~P#8oOP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO(aVO(z#}Oa#mi!]#mi#z#mi#{#mi'z#mi(r#mi(y#mi'w#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#x$UO~P#;VO#x#mi~P#;VO#v$SO~P#8oOP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO(aVO(y#|O(z#}Oa#mi!]#mi#{#mi'z#mi(r#mi'w#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#z#mi~P#={O#z$WO~P#={OP]XR]X[]Xj]Xr]X!Q]X!S]X!l]X!p]X#R]X#S]X#`]X#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X(a]X(r]X(y]X(z]X!]]X!^]X~O$O]X~P#@jOP$[OR#zO[<mOj<bOr<kO!Q#yO!S#{O!l#xO!p$[O#R<bO#n<_O#o<`O#p<`O#q<`O#r<aO#s<bO#t<bO#u<lO#v<cO#x<eO#z<gO#{<hO(aVO(r$YO(y#|O(z#}O~O$O.{O~P#BwO#S$dO#`<nO$Q<nO$O(gX!^(gX~P! uOa'da!]'da'z'da'w'da!k'da!Y'dav'da!_'da%i'da!g'da~P!:tO[#mia#mij#mir#mi!]#mi#R#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi'z#mi(r#mi'w#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~OP$[OR#zO!Q#yO!S#{O!l#xO!p$[O#n$OO#o$PO#p$PO#q$PO(aVO(y#mi(z#mi~P#EyOn>]O!Q*OO'y*PO(y$}O(z%POP#miR#mi!S#mi!l#mi!p#mi#n#mi#o#mi#p#mi#q#mi(a#mi~P#EyO!]/POg(pX~P!1WOg/RO~Oa$Pi!]$Pi'z$Pi'w$Pi!Y$Pi!k$Piv$Pi!_$Pi%i$Pi!g$Pi~P!:tO$]/SO$_/SO~O$]/TO$_/TO~O!g)hO#`/UO!_$cX$Z$cX$]$cX$_$cX$f$cX~O![/VO~O!_)kO$Z/XO$])jO$_)jO$f/YO~O!]<iO!^(fX~P#BwO!^/ZO~O!g)hO$f({X~O$f/]O~Ov/^O~P!&zOx)yO(b)zO(c/aO~O!S/dO~O(y$}On%aa!Q%aa'y%aa(z%aa!]%aa#`%aa~Og%aa$O%aa~P#L{O(z%POn%ca!Q%ca'y%ca(y%ca!]%ca#`%ca~Og%ca$O%ca~P#MnO!]fX!gfX!kfX!k$zX(rfX~P!0SOp%WO![/mO!](^O(T/lO!Y(vP!Y)PP~P!1uOr*sO!b*qO!c*kO!d*kO!l*bO#[*rO%`*mO(U!lO(VTO(YUO~Os<}O!S/nO![+[O!^*pO(e<|O!^(xP~P$ [O!k/oO~P#/sO!]/pO!g#vO(r'pO!k)OX~O!k/uO~OnoX!QoX'yoX(yoX(zoX~O!g#vO!koX~P$#OOp/wO!S%hO![*^O!_%iO(T%gO!k)OP~O#k/xO~O!Y$zX!]$zX!g%RX~P!0SO!]/yO!Y)PX~P#/sO!g/{O~O!Y/}O~OpkO(T0OO~P.iOh%VOr0TO!g#vO!l%eO(r'pO~O!g+iO~Oa%nO!]0XO'z%nO~O!^0ZO~P!5iO!c0[O!d0[O(U!lO~P#$`Os!nO!S0]O(VTO(YUO(e!mO~O#[0_O~Og%aa!]%aa#`%aa$O%aa~P!1WOg%ca!]%ca#`%ca$O%ca~P!1WOj%dOk%dOl%dO(T&ZOg'mX!]'mX~O!]*yOg(^a~Og0hO~On0jO#`0iOg(_a!](_a~OR0kO!Q0kO!S0lO#S$dOn}a'y}a(y}a(z}a!]}a#`}a~Og}a$O}a~P$(cO!Q*OO'y*POn$sa(y$sa(z$sa!]$sa#`$sa~Og$sa$O$sa~P$)_O!Q*OO'y*POn$ua(y$ua(z$ua!]$ua#`$ua~Og$ua$O$ua~P$*QO#k0oO~Og%Ta!]%Ta#`%Ta$O%Ta~P!1WO!g#vO~O#k0rO~O!]+^Oa)Ta'z)Ta~OR#zO!Q#yO!S#{O!l#xO(aVOP!ri[!rij!rir!ri!]!ri!p!ri#R!ri#n!ri#o!ri#p!ri#q!ri#r!ri#s!ri#t!ri#u!ri#v!ri#x!ri#z!ri#{!ri(r!ri(y!ri(z!ri~Oa!ri'z!ri'w!ri!Y!ri!k!riv!ri!_!ri%i!ri!g!ri~P$+oOh%VOr%XOs$tOt$tOz%YO|%ZO!O<sO!S${O!_$|O!i>VO!l$xO#j<yO$W%`O$t<uO$v<wO$y%aO(VTO(YUO(a$uO(y$}O(z%PO~Op0{O%]0|O(T0zO~P$.VO!g+iOa(]a!_(]a'z(]a!](]a~O#k1SO~O[]X!]fX!^fX~O!]1TO!^)XX~O!^1VO~O[1WO~Ob1YO(T+qO(VTO(YUO~O!_&PO(T%gO`'uX!]'uX~O!]+vO`)Wa~O!k1]O~P!:tO[1`O~O`1aO~O#`1fO~On1iO!_$|O~O(e(|O!^)UP~Oh%VOn1rO!_1oO%i1qO~O[1|O!]1zO!^)VX~O!^1}O~O`2POa%nO'z%nO~O(T#nO(VTO(YUO~O#S$dO#`$eO$Q$eOP(gXR(gX[(gXr(gX!Q(gX!S(gX!](gX!l(gX!p(gX#R(gX#n(gX#o(gX#p(gX#q(gX#r(gX#s(gX#t(gX#u(gX#v(gX#x(gX#z(gX#{(gX(a(gX(r(gX(y(gX(z(gX~Oj2SO&[2TOa(gX~P$3pOj2SO#`$eO&[2TO~Oa2VO~P%[Oa2XO~O&e2[OP&ciQ&ciS&ciY&cia&cid&cie&cil&cip&cir&cis&cit&ciz&ci|&ci!O&ci!S&ci!W&ci!X&ci!_&ci!i&ci!l&ci!o&ci!p&ci!q&ci!s&ci!u&ci!x&ci!|&ci$W&ci$n&ci%h&ci%j&ci%l&ci%m&ci%n&ci%q&ci%s&ci%v&ci%w&ci%y&ci&W&ci&^&ci&`&ci&b&ci&d&ci&g&ci&m&ci&s&ci&u&ci&w&ci&y&ci&{&ci'w&ci(T&ci(V&ci(Y&ci(a&ci(o&ci!^&cib&ci&j&ci~Ob2bO!^2`O&j2aO~P`O!_XO!l2dO~O&q,{OP&liQ&liS&liY&lia&lid&lie&lil&lip&lir&lis&lit&liz&li|&li!O&li!S&li!W&li!X&li!_&li!i&li!l&li!o&li!p&li!q&li!s&li!u&li!x&li!|&li$W&li$n&li%h&li%j&li%l&li%m&li%n&li%q&li%s&li%v&li%w&li%y&li&W&li&^&li&`&li&b&li&d&li&g&li&m&li&s&li&u&li&w&li&y&li&{&li'w&li(T&li(V&li(Y&li(a&li(o&li!^&li&e&lib&li&j&li~O!Y2jO~O!]!aa!^!aa~P#BwOs!nO!S!oO![2pO(e!mO!]'XX!^'XX~P@nO!]-]O!^(ia~O!]'_X!^'_X~P!9|O!]-`O!^(xa~O!^2wO~P'_Oa%nO#`3QO'z%nO~Oa%nO!g#vO#`3QO'z%nO~Oa%nO!g#vO!p3UO#`3QO'z%nO(r'pO~Oa%nO'z%nO~P!:tO!]$_Ov$qa~O!Y'Wi!]'Wi~P!:tO!](VO!Y(hi~O!](^O!Y(vi~O!Y(wi!](wi~P!:tO!](ti!k(tia(ti'z(ti~P!:tO#`3WO!](ti!k(tia(ti'z(ti~O!](jO!k(si~O!S%hO!_%iO!|]O#i3]O#j3[O(T%gO~O!S%hO!_%iO#j3[O(T%gO~On3dO!_'`O%i3cO~Oh%VOn3dO!_'`O%i3cO~O#k%aaP%aaR%aa[%aaa%aaj%aar%aa!S%aa!l%aa!p%aa#R%aa#n%aa#o%aa#p%aa#q%aa#r%aa#s%aa#t%aa#u%aa#v%aa#x%aa#z%aa#{%aa'z%aa(a%aa(r%aa!k%aa!Y%aa'w%aav%aa!_%aa%i%aa!g%aa~P#L{O#k%caP%caR%ca[%caa%caj%car%ca!S%ca!l%ca!p%ca#R%ca#n%ca#o%ca#p%ca#q%ca#r%ca#s%ca#t%ca#u%ca#v%ca#x%ca#z%ca#{%ca'z%ca(a%ca(r%ca!k%ca!Y%ca'w%cav%ca!_%ca%i%ca!g%ca~P#MnO#k%aaP%aaR%aa[%aaa%aaj%aar%aa!S%aa!]%aa!l%aa!p%aa#R%aa#n%aa#o%aa#p%aa#q%aa#r%aa#s%aa#t%aa#u%aa#v%aa#x%aa#z%aa#{%aa'z%aa(a%aa(r%aa!k%aa!Y%aa'w%aa#`%aav%aa!_%aa%i%aa!g%aa~P#/sO#k%caP%caR%ca[%caa%caj%car%ca!S%ca!]%ca!l%ca!p%ca#R%ca#n%ca#o%ca#p%ca#q%ca#r%ca#s%ca#t%ca#u%ca#v%ca#x%ca#z%ca#{%ca'z%ca(a%ca(r%ca!k%ca!Y%ca'w%ca#`%cav%ca!_%ca%i%ca!g%ca~P#/sO#k}aP}a[}aa}aj}ar}a!l}a!p}a#R}a#n}a#o}a#p}a#q}a#r}a#s}a#t}a#u}a#v}a#x}a#z}a#{}a'z}a(a}a(r}a!k}a!Y}a'w}av}a!_}a%i}a!g}a~P$(cO#k$saP$saR$sa[$saa$saj$sar$sa!S$sa!l$sa!p$sa#R$sa#n$sa#o$sa#p$sa#q$sa#r$sa#s$sa#t$sa#u$sa#v$sa#x$sa#z$sa#{$sa'z$sa(a$sa(r$sa!k$sa!Y$sa'w$sav$sa!_$sa%i$sa!g$sa~P$)_O#k$uaP$uaR$ua[$uaa$uaj$uar$ua!S$ua!l$ua!p$ua#R$ua#n$ua#o$ua#p$ua#q$ua#r$ua#s$ua#t$ua#u$ua#v$ua#x$ua#z$ua#{$ua'z$ua(a$ua(r$ua!k$ua!Y$ua'w$uav$ua!_$ua%i$ua!g$ua~P$*QO#k%TaP%TaR%Ta[%Taa%Taj%Tar%Ta!S%Ta!]%Ta!l%Ta!p%Ta#R%Ta#n%Ta#o%Ta#p%Ta#q%Ta#r%Ta#s%Ta#t%Ta#u%Ta#v%Ta#x%Ta#z%Ta#{%Ta'z%Ta(a%Ta(r%Ta!k%Ta!Y%Ta'w%Ta#`%Tav%Ta!_%Ta%i%Ta!g%Ta~P#/sOa#cq!]#cq'z#cq'w#cq!Y#cq!k#cqv#cq!_#cq%i#cq!g#cq~P!:tO![3lO!]'YX!k'YX~P%[O!].tO!k(ka~O!].tO!k(ka~P!:tO!Y3oO~O$O!na!^!na~PKlO$O!ja!]!ja!^!ja~P#BwO$O!ra!^!ra~P!=[O$O!ta!^!ta~P!?rOg']X!]']X~P!,TO!]/POg(pa~OSfO!_4TO$d4UO~O!^4YO~Ov4ZO~P#/sOa$mq!]$mq'z$mq'w$mq!Y$mq!k$mqv$mq!_$mq%i$mq!g$mq~P!:tO!Y4]O~P!&zO!S4^O~O!Q*OO'y*PO(z%POn'ia(y'ia!]'ia#`'ia~Og'ia$O'ia~P%-fO!Q*OO'y*POn'ka(y'ka(z'ka!]'ka#`'ka~Og'ka$O'ka~P%.XO(r$YO~P#/sO!YfX!Y$zX!]fX!]$zX!g%RX#`fX~P!0SOp%WO(T=WO~P!1uOp4bO!S%hO![4aO!_%iO(T%gO!]'eX!k'eX~O!]/pO!k)Oa~O!]/pO!g#vO!k)Oa~O!]/pO!g#vO(r'pO!k)Oa~Og$|i!]$|i#`$|i$O$|i~P!1WO![4jO!Y'gX!]'gX~P!3tO!]/yO!Y)Pa~O!]/yO!Y)Pa~P#/sOP]XR]X[]Xj]Xr]X!Q]X!S]X!Y]X!]]X!l]X!p]X#R]X#S]X#`]X#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X(a]X(r]X(y]X(z]X~Oj%YX!g%YX~P%2OOj4oO!g#vO~Oh%VO!g#vO!l%eO~Oh%VOr4tO!l%eO(r'pO~Or4yO!g#vO(r'pO~Os!nO!S4zO(VTO(YUO(e!mO~O(y$}On%ai!Q%ai'y%ai(z%ai!]%ai#`%ai~Og%ai$O%ai~P%5oO(z%POn%ci!Q%ci'y%ci(y%ci!]%ci#`%ci~Og%ci$O%ci~P%6bOg(_i!](_i~P!1WO#`5QOg(_i!](_i~P!1WO!k5VO~Oa$oq!]$oq'z$oq'w$oq!Y$oq!k$oqv$oq!_$oq%i$oq!g$oq~P!:tO!Y5ZO~O!]5[O!_)QX~P#/sOa$zX!_$zX%^]X'z$zX!]$zX~P!0SO%^5_OaoX!_oX'zoX!]oX~P$#OOp5`O(T#nO~O%^5_O~Ob5fO%j5gO(T+qO(VTO(YUO!]'tX!^'tX~O!]1TO!^)Xa~O[5kO~O`5lO~O[5pO~Oa%nO'z%nO~P#/sO!]5uO#`5wO!^)UX~O!^5xO~Or6OOs!nO!S*iO!b!yO!c!vO!d!vO!|<VO#T!pO#U!pO#V!pO#W!pO#X!pO#[5}O#]!zO(U!lO(VTO(YUO(e!mO(o!sO~O!^5|O~P%;eOn6TO!_1oO%i6SO~Oh%VOn6TO!_1oO%i6SO~Ob6[O(T#nO(VTO(YUO!]'sX!^'sX~O!]1zO!^)Va~O(VTO(YUO(e6^O~O`6bO~Oj6eO&[6fO~PNXO!k6gO~P%[Oa6iO~Oa6iO~P%[Ob2bO!^6nO&j2aO~P`O!g6pO~O!g6rOh(ji!](ji!^(ji!g(ji!l(jir(ji(r(ji~O!]#hi!^#hi~P#BwO#`6sO!]#hi!^#hi~O!]!ai!^!ai~P#BwOa%nO#`6|O'z%nO~Oa%nO!g#vO#`6|O'z%nO~O!](tq!k(tqa(tq'z(tq~P!:tO!](jO!k(sq~O!S%hO!_%iO#j7TO(T%gO~O!_'`O%i7WO~On7[O!_'`O%i7WO~O#k'iaP'iaR'ia['iaa'iaj'iar'ia!S'ia!l'ia!p'ia#R'ia#n'ia#o'ia#p'ia#q'ia#r'ia#s'ia#t'ia#u'ia#v'ia#x'ia#z'ia#{'ia'z'ia(a'ia(r'ia!k'ia!Y'ia'w'iav'ia!_'ia%i'ia!g'ia~P%-fO#k'kaP'kaR'ka['kaa'kaj'kar'ka!S'ka!l'ka!p'ka#R'ka#n'ka#o'ka#p'ka#q'ka#r'ka#s'ka#t'ka#u'ka#v'ka#x'ka#z'ka#{'ka'z'ka(a'ka(r'ka!k'ka!Y'ka'w'kav'ka!_'ka%i'ka!g'ka~P%.XO#k$|iP$|iR$|i[$|ia$|ij$|ir$|i!S$|i!]$|i!l$|i!p$|i#R$|i#n$|i#o$|i#p$|i#q$|i#r$|i#s$|i#t$|i#u$|i#v$|i#x$|i#z$|i#{$|i'z$|i(a$|i(r$|i!k$|i!Y$|i'w$|i#`$|iv$|i!_$|i%i$|i!g$|i~P#/sO#k%aiP%aiR%ai[%aia%aij%air%ai!S%ai!l%ai!p%ai#R%ai#n%ai#o%ai#p%ai#q%ai#r%ai#s%ai#t%ai#u%ai#v%ai#x%ai#z%ai#{%ai'z%ai(a%ai(r%ai!k%ai!Y%ai'w%aiv%ai!_%ai%i%ai!g%ai~P%5oO#k%ciP%ciR%ci[%cia%cij%cir%ci!S%ci!l%ci!p%ci#R%ci#n%ci#o%ci#p%ci#q%ci#r%ci#s%ci#t%ci#u%ci#v%ci#x%ci#z%ci#{%ci'z%ci(a%ci(r%ci!k%ci!Y%ci'w%civ%ci!_%ci%i%ci!g%ci~P%6bO!]'Ya!k'Ya~P!:tO!].tO!k(ki~O$O#ci!]#ci!^#ci~P#BwOP$[OR#zO!Q#yO!S#{O!l#xO!p$[O(aVO[#mij#mir#mi#R#mi#o#mi#p#mi#q#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi$O#mi(r#mi(y#mi(z#mi!]#mi!^#mi~O#n#mi~P%NdO#n<_O~P%NdOP$[OR#zOr<kO!Q#yO!S#{O!l#xO!p$[O#n<_O#o<`O#p<`O#q<`O(aVO[#mij#mi#R#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi$O#mi(r#mi(y#mi(z#mi!]#mi!^#mi~O#r#mi~P&!lO#r<aO~P&!lOP$[OR#zO[<mOj<bOr<kO!Q#yO!S#{O!l#xO!p$[O#R<bO#n<_O#o<`O#p<`O#q<`O#r<aO#s<bO#t<bO#u<lO(aVO#x#mi#z#mi#{#mi$O#mi(r#mi(y#mi(z#mi!]#mi!^#mi~O#v#mi~P&$tOP$[OR#zO[<mOj<bOr<kO!Q#yO!S#{O!l#xO!p$[O#R<bO#n<_O#o<`O#p<`O#q<`O#r<aO#s<bO#t<bO#u<lO#v<cO(aVO(z#}O#z#mi#{#mi$O#mi(r#mi(y#mi!]#mi!^#mi~O#x<eO~P&&uO#x#mi~P&&uO#v<cO~P&$tOP$[OR#zO[<mOj<bOr<kO!Q#yO!S#{O!l#xO!p$[O#R<bO#n<_O#o<`O#p<`O#q<`O#r<aO#s<bO#t<bO#u<lO#v<cO#x<eO(aVO(y#|O(z#}O#{#mi$O#mi(r#mi!]#mi!^#mi~O#z#mi~P&)UO#z<gO~P&)UOa#|y!]#|y'z#|y'w#|y!Y#|y!k#|yv#|y!_#|y%i#|y!g#|y~P!:tO[#mij#mir#mi#R#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi$O#mi(r#mi!]#mi!^#mi~OP$[OR#zO!Q#yO!S#{O!l#xO!p$[O#n<_O#o<`O#p<`O#q<`O(aVO(y#mi(z#mi~P&,QOn>^O!Q*OO'y*PO(y$}O(z%POP#miR#mi!S#mi!l#mi!p#mi#n#mi#o#mi#p#mi#q#mi(a#mi~P&,QO#S$dOP(`XR(`X[(`Xj(`Xn(`Xr(`X!Q(`X!S(`X!l(`X!p(`X#R(`X#n(`X#o(`X#p(`X#q(`X#r(`X#s(`X#t(`X#u(`X#v(`X#x(`X#z(`X#{(`X$O(`X'y(`X(a(`X(r(`X(y(`X(z(`X!](`X!^(`X~O$O$Pi!]$Pi!^$Pi~P#BwO$O!ri!^!ri~P$+oOg']a!]']a~P!1WO!^7nO~O!]'da!^'da~P#BwO!Y7oO~P#/sO!g#vO(r'pO!]'ea!k'ea~O!]/pO!k)Oi~O!]/pO!g#vO!k)Oi~Og$|q!]$|q#`$|q$O$|q~P!1WO!Y'ga!]'ga~P#/sO!g7vO~O!]/yO!Y)Pi~P#/sO!]/yO!Y)Pi~O!Y7yO~Oh%VOr8OO!l%eO(r'pO~Oj8QO!g#vO~Or8TO!g#vO(r'pO~O!Q*OO'y*PO(z%POn'ja(y'ja!]'ja#`'ja~Og'ja$O'ja~P&5RO!Q*OO'y*POn'la(y'la(z'la!]'la#`'la~Og'la$O'la~P&5tOg(_q!](_q~P!1WO#`8VOg(_q!](_q~P!1WO!Y8WO~Og%Oq!]%Oq#`%Oq$O%Oq~P!1WOa$oy!]$oy'z$oy'w$oy!Y$oy!k$oyv$oy!_$oy%i$oy!g$oy~P!:tO!g6rO~O!]5[O!_)Qa~O!_'`OP$TaR$Ta[$Taj$Tar$Ta!Q$Ta!S$Ta!]$Ta!l$Ta!p$Ta#R$Ta#n$Ta#o$Ta#p$Ta#q$Ta#r$Ta#s$Ta#t$Ta#u$Ta#v$Ta#x$Ta#z$Ta#{$Ta(a$Ta(r$Ta(y$Ta(z$Ta~O%i7WO~P&8fO%^8[Oa%[i!_%[i'z%[i!]%[i~Oa#cy!]#cy'z#cy'w#cy!Y#cy!k#cyv#cy!_#cy%i#cy!g#cy~P!:tO[8^O~Ob8`O(T+qO(VTO(YUO~O!]1TO!^)Xi~O`8dO~O(e(|O!]'pX!^'pX~O!]5uO!^)Ua~O!^8nO~P%;eO(o!sO~P$&YO#[8oO~O!_1oO~O!_1oO%i8qO~On8tO!_1oO%i8qO~O[8yO!]'sa!^'sa~O!]1zO!^)Vi~O!k8}O~O!k9OO~O!k9RO~O!k9RO~P%[Oa9TO~O!g9UO~O!k9VO~O!](wi!^(wi~P#BwOa%nO#`9_O'z%nO~O!](ty!k(tya(ty'z(ty~P!:tO!](jO!k(sy~O%i9bO~P&8fO!_'`O%i9bO~O#k$|qP$|qR$|q[$|qa$|qj$|qr$|q!S$|q!]$|q!l$|q!p$|q#R$|q#n$|q#o$|q#p$|q#q$|q#r$|q#s$|q#t$|q#u$|q#v$|q#x$|q#z$|q#{$|q'z$|q(a$|q(r$|q!k$|q!Y$|q'w$|q#`$|qv$|q!_$|q%i$|q!g$|q~P#/sO#k'jaP'jaR'ja['jaa'jaj'jar'ja!S'ja!l'ja!p'ja#R'ja#n'ja#o'ja#p'ja#q'ja#r'ja#s'ja#t'ja#u'ja#v'ja#x'ja#z'ja#{'ja'z'ja(a'ja(r'ja!k'ja!Y'ja'w'jav'ja!_'ja%i'ja!g'ja~P&5RO#k'laP'laR'la['laa'laj'lar'la!S'la!l'la!p'la#R'la#n'la#o'la#p'la#q'la#r'la#s'la#t'la#u'la#v'la#x'la#z'la#{'la'z'la(a'la(r'la!k'la!Y'la'w'lav'la!_'la%i'la!g'la~P&5tO#k%OqP%OqR%Oq[%Oqa%Oqj%Oqr%Oq!S%Oq!]%Oq!l%Oq!p%Oq#R%Oq#n%Oq#o%Oq#p%Oq#q%Oq#r%Oq#s%Oq#t%Oq#u%Oq#v%Oq#x%Oq#z%Oq#{%Oq'z%Oq(a%Oq(r%Oq!k%Oq!Y%Oq'w%Oq#`%Oqv%Oq!_%Oq%i%Oq!g%Oq~P#/sO!]'Yi!k'Yi~P!:tO$O#cq!]#cq!^#cq~P#BwO(y$}OP%aaR%aa[%aaj%aar%aa!S%aa!l%aa!p%aa#R%aa#n%aa#o%aa#p%aa#q%aa#r%aa#s%aa#t%aa#u%aa#v%aa#x%aa#z%aa#{%aa$O%aa(a%aa(r%aa!]%aa!^%aa~On%aa!Q%aa'y%aa(z%aa~P&IyO(z%POP%caR%ca[%caj%car%ca!S%ca!l%ca!p%ca#R%ca#n%ca#o%ca#p%ca#q%ca#r%ca#s%ca#t%ca#u%ca#v%ca#x%ca#z%ca#{%ca$O%ca(a%ca(r%ca!]%ca!^%ca~On%ca!Q%ca'y%ca(y%ca~P&LQOn>^O!Q*OO'y*PO(z%PO~P&IyOn>^O!Q*OO'y*PO(y$}O~P&LQOR0kO!Q0kO!S0lO#S$dOP}a[}aj}an}ar}a!l}a!p}a#R}a#n}a#o}a#p}a#q}a#r}a#s}a#t}a#u}a#v}a#x}a#z}a#{}a$O}a'y}a(a}a(r}a(y}a(z}a!]}a!^}a~O!Q*OO'y*POP$saR$sa[$saj$san$sar$sa!S$sa!l$sa!p$sa#R$sa#n$sa#o$sa#p$sa#q$sa#r$sa#s$sa#t$sa#u$sa#v$sa#x$sa#z$sa#{$sa$O$sa(a$sa(r$sa(y$sa(z$sa!]$sa!^$sa~O!Q*OO'y*POP$uaR$ua[$uaj$uan$uar$ua!S$ua!l$ua!p$ua#R$ua#n$ua#o$ua#p$ua#q$ua#r$ua#s$ua#t$ua#u$ua#v$ua#x$ua#z$ua#{$ua$O$ua(a$ua(r$ua(y$ua(z$ua!]$ua!^$ua~On>^O!Q*OO'y*PO(y$}O(z%PO~OP%TaR%Ta[%Taj%Tar%Ta!S%Ta!l%Ta!p%Ta#R%Ta#n%Ta#o%Ta#p%Ta#q%Ta#r%Ta#s%Ta#t%Ta#u%Ta#v%Ta#x%Ta#z%Ta#{%Ta$O%Ta(a%Ta(r%Ta!]%Ta!^%Ta~P''VO$O$mq!]$mq!^$mq~P#BwO$O$oq!]$oq!^$oq~P#BwO!^9oO~O$O9pO~P!1WO!g#vO!]'ei!k'ei~O!g#vO(r'pO!]'ei!k'ei~O!]/pO!k)Oq~O!Y'gi!]'gi~P#/sO!]/yO!Y)Pq~Or9wO!g#vO(r'pO~O[9yO!Y9xO~P#/sO!Y9xO~Oj:PO!g#vO~Og(_y!](_y~P!1WO!]'na!_'na~P#/sOa%[q!_%[q'z%[q!]%[q~P#/sO[:UO~O!]1TO!^)Xq~O`:YO~O#`:ZO!]'pa!^'pa~O!]5uO!^)Ui~P#BwO!S:]O~O!_1oO%i:`O~O(VTO(YUO(e:eO~O!]1zO!^)Vq~O!k:hO~O!k:iO~O!k:jO~O!k:jO~P%[O#`:mO!]#hy!^#hy~O!]#hy!^#hy~P#BwO%i:rO~P&8fO!_'`O%i:rO~O$O#|y!]#|y!^#|y~P#BwOP$|iR$|i[$|ij$|ir$|i!S$|i!l$|i!p$|i#R$|i#n$|i#o$|i#p$|i#q$|i#r$|i#s$|i#t$|i#u$|i#v$|i#x$|i#z$|i#{$|i$O$|i(a$|i(r$|i!]$|i!^$|i~P''VO!Q*OO'y*PO(z%POP'iaR'ia['iaj'ian'iar'ia!S'ia!l'ia!p'ia#R'ia#n'ia#o'ia#p'ia#q'ia#r'ia#s'ia#t'ia#u'ia#v'ia#x'ia#z'ia#{'ia$O'ia(a'ia(r'ia(y'ia!]'ia!^'ia~O!Q*OO'y*POP'kaR'ka['kaj'kan'kar'ka!S'ka!l'ka!p'ka#R'ka#n'ka#o'ka#p'ka#q'ka#r'ka#s'ka#t'ka#u'ka#v'ka#x'ka#z'ka#{'ka$O'ka(a'ka(r'ka(y'ka(z'ka!]'ka!^'ka~O(y$}OP%aiR%ai[%aij%ain%air%ai!Q%ai!S%ai!l%ai!p%ai#R%ai#n%ai#o%ai#p%ai#q%ai#r%ai#s%ai#t%ai#u%ai#v%ai#x%ai#z%ai#{%ai$O%ai'y%ai(a%ai(r%ai(z%ai!]%ai!^%ai~O(z%POP%ciR%ci[%cij%cin%cir%ci!Q%ci!S%ci!l%ci!p%ci#R%ci#n%ci#o%ci#p%ci#q%ci#r%ci#s%ci#t%ci#u%ci#v%ci#x%ci#z%ci#{%ci$O%ci'y%ci(a%ci(r%ci(y%ci!]%ci!^%ci~O$O$oy!]$oy!^$oy~P#BwO$O#cy!]#cy!^#cy~P#BwO!g#vO!]'eq!k'eq~O!]/pO!k)Oy~O!Y'gq!]'gq~P#/sOr:|O!g#vO(r'pO~O[;QO!Y;PO~P#/sO!Y;PO~Og(_!R!](_!R~P!1WOa%[y!_%[y'z%[y!]%[y~P#/sO!]1TO!^)Xy~O!]5uO!^)Uq~O(T;XO~O!_1oO%i;[O~O!k;_O~O%i;dO~P&8fOP$|qR$|q[$|qj$|qr$|q!S$|q!l$|q!p$|q#R$|q#n$|q#o$|q#p$|q#q$|q#r$|q#s$|q#t$|q#u$|q#v$|q#x$|q#z$|q#{$|q$O$|q(a$|q(r$|q!]$|q!^$|q~P''VO!Q*OO'y*PO(z%POP'jaR'ja['jaj'jan'jar'ja!S'ja!l'ja!p'ja#R'ja#n'ja#o'ja#p'ja#q'ja#r'ja#s'ja#t'ja#u'ja#v'ja#x'ja#z'ja#{'ja$O'ja(a'ja(r'ja(y'ja!]'ja!^'ja~O!Q*OO'y*POP'laR'la['laj'lan'lar'la!S'la!l'la!p'la#R'la#n'la#o'la#p'la#q'la#r'la#s'la#t'la#u'la#v'la#x'la#z'la#{'la$O'la(a'la(r'la(y'la(z'la!]'la!^'la~OP%OqR%Oq[%Oqj%Oqr%Oq!S%Oq!l%Oq!p%Oq#R%Oq#n%Oq#o%Oq#p%Oq#q%Oq#r%Oq#s%Oq#t%Oq#u%Oq#v%Oq#x%Oq#z%Oq#{%Oq$O%Oq(a%Oq(r%Oq!]%Oq!^%Oq~P''VOg%e!Z!]%e!Z#`%e!Z$O%e!Z~P!1WO!Y;hO~P#/sOr;iO!g#vO(r'pO~O[;kO!Y;hO~P#/sO!]'pq!^'pq~P#BwO!]#h!Z!^#h!Z~P#BwO#k%e!ZP%e!ZR%e!Z[%e!Za%e!Zj%e!Zr%e!Z!S%e!Z!]%e!Z!l%e!Z!p%e!Z#R%e!Z#n%e!Z#o%e!Z#p%e!Z#q%e!Z#r%e!Z#s%e!Z#t%e!Z#u%e!Z#v%e!Z#x%e!Z#z%e!Z#{%e!Z'z%e!Z(a%e!Z(r%e!Z!k%e!Z!Y%e!Z'w%e!Z#`%e!Zv%e!Z!_%e!Z%i%e!Z!g%e!Z~P#/sOr;tO!g#vO(r'pO~O!Y;uO~P#/sOr;|O!g#vO(r'pO~O!Y;}O~P#/sOP%e!ZR%e!Z[%e!Zj%e!Zr%e!Z!S%e!Z!l%e!Z!p%e!Z#R%e!Z#n%e!Z#o%e!Z#p%e!Z#q%e!Z#r%e!Z#s%e!Z#t%e!Z#u%e!Z#v%e!Z#x%e!Z#z%e!Z#{%e!Z$O%e!Z(a%e!Z(r%e!Z!]%e!Z!^%e!Z~P''VOr<QO!g#vO(r'pO~Ov(fX~P1qO!Q%rO~P!)[O(U!lO~P!)[O!YfX!]fX#`fX~P%2OOP]XR]X[]Xj]Xr]X!Q]X!S]X!]]X!]fX!l]X!p]X#R]X#S]X#`]X#`fX#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X(a]X(r]X(y]X(z]X~O!gfX!k]X!kfX(rfX~P'LTOP<UOQ<UOSfOd>ROe!iOpkOr<UOskOtkOzkO|<UO!O<UO!SWO!WkO!XkO!_XO!i<XO!lZO!o<UO!p<UO!q<UO!s<YO!u<]O!x!hO$W!kO$n>PO(T)]O(VTO(YUO(aVO(o[O~O!]<iO!^$qa~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<tO!S${O!_$|O!i>WO!l$xO#j<zO$W%`O$t<vO$v<xO$y%aO(T(vO(VTO(YUO(a$uO(y$}O(z%PO~Ol)dO~P(!yOr!eX(r!eX~P#!iOr(jX(r(jX~P##[O!^]X!^fX~P'LTO!YfX!Y$zX!]fX!]$zX#`fX~P!0SO#k<^O~O!g#vO#k<^O~O#`<nO~Oj<bO~O#`=OO!](wX!^(wX~O#`<nO!](uX!^(uX~O#k=PO~Og=RO~P!1WO#k=XO~O#k=YO~Og=RO(T&ZO~O!g#vO#k=ZO~O!g#vO#k=PO~O$O=[O~P#BwO#k=]O~O#k=^O~O#k=cO~O#k=dO~O#k=eO~O#k=fO~O$O=gO~P!1WO$O=hO~P!1WOl=sO~P7eOk#S#T#U#W#X#[#i#j#u$n$t$v$y%]%^%h%i%j%q%s%v%w%y%{~(OT#o!X'|(U#ps#n#qr!Q'}$]'}(T$_(e~",goto:"$9Y)]PPPPPP)^PP)aP)rP+W/]PPPP6mPP7TPP=QPPP@tPA^PA^PPPA^PCfPA^PA^PA^PCjPCoPD^PIWPPPI[PPPPI[L_PPPLeMVPI[PI[PP! eI[PPPI[PI[P!#lI[P!'S!(X!(bP!)U!)Y!)U!,gPPPPPPP!-W!(XPP!-h!/YP!2iI[I[!2n!5z!:h!:h!>gPPP!>oI[PPPPPPPPP!BOP!C]PPI[!DnPI[PI[I[I[I[I[PI[!FQP!I[P!LbP!Lf!Lp!Lt!LtP!IXP!Lx!LxP#!OP#!SI[PI[#!Y#%_CjA^PA^PA^A^P#&lA^A^#)OA^#+vA^#.SA^A^#.r#1W#1W#1]#1f#1W#1qPP#1WPA^#2ZA^#6YA^A^6mPPP#:_PPP#:x#:xP#:xP#;`#:xPP#;fP#;]P#;]#;y#;]#<e#<k#<n)aP#<q)aP#<z#<z#<zP)aP)aP)aP)aPP)aP#=Q#=TP#=T)aP#=XP#=[P)aP)aP)aP)aP)aP)a)aPP#=b#=h#=s#=y#>P#>V#>]#>k#>q#>{#?R#?]#?c#?s#?y#@k#@}#AT#AZ#Ai#BO#Cs#DR#DY#Et#FS#Gt#HS#HY#H`#Hf#Hp#Hv#H|#IW#Ij#IpPPPPPPPPPPP#IvPPPPPPP#Jk#Mx$ b$ i$ qPPP$']P$'f$*_$0x$0{$1O$1}$2Q$2X$2aP$2g$2jP$3W$3[$4S$5b$5g$5}PP$6S$6Y$6^$6a$6e$6i$7e$7|$8e$8i$8l$8o$8y$8|$9Q$9UR!|RoqOXst!Z#d%m&r&t&u&w,s,x2[2_Y!vQ'`-e1o5{Q%tvQ%|yQ&T|Q&j!VS'W!e-]Q'f!iS'l!r!yU*k$|*Z*oQ+o%}S+|&V&WQ,d&dQ-c'_Q-m'gQ-u'mQ0[*qQ1b,OQ1y,eR<{<Y%SdOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%m%t&R&k&n&r&t&u&w&{'T'b'r(T(V(](d(x(z)O)}*i+X+],p,s,x-i-q.P.V.t.{/n0]0l0r1S1r2S2T2V2X2[2_2a3Q3W3l4z6T6e6f6i6|8t9T9_S#q]<V!r)_$Z$n'X)s-U-X/V2p4T5w6s:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>SU+P%]<s<tQ+t&PQ,f&gQ,m&oQ0x+gQ0}+iQ1Y+uQ2R,kQ3`.gQ5`0|Q5f1TQ6[1zQ7Y3dQ8`5gR9e7['QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%m%t&R&k&n&o&r&t&u&w&{'T'X'b'r(T(V(](d(x(z)O)s)}*i+X+]+g,p,s,x-U-X-i-q.P.V.g.t.{/V/n0]0l0r1S1r2S2T2V2X2[2_2a2p3Q3W3d3l4T4z5w6T6e6f6i6s6|7[8t9T9_:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>S!S!nQ!r!v!y!z$|'W'_'`'l'm'n*k*o*q*r-]-c-e-u0[0_1o5{5}%[$ti#v$b$c$d$x${%O%Q%^%_%c)y*R*T*V*Y*a*g*w*x+f+i,S,V.f/P/d/m/x/y/{0`0b0i0j0o1f1i1q3c4^4_4j4o5Q5[5_6S7W7v8Q8V8[8q9b9p9y:P:`:r;Q;[;d;k<l<m<o<p<q<r<u<v<w<x<y<z=S=T=U=V=X=Y=]=^=_=`=a=b=c=d=g=h>P>X>Y>]>^Q&X|Q'U!eS'[%i-`Q+t&PQ,P&WQ,f&gQ0n+SQ1Y+uQ1_+{Q2Q,jQ2R,kQ5f1TQ5o1aQ6[1zQ6_1|Q6`2PQ8`5gQ8c5lQ8|6bQ:X8dQ:f8yQ;V:YR<}*ZrnOXst!V!Z#d%m&i&r&t&u&w,s,x2[2_R,h&k&z^OPXYstuvwz!Z!`!g!j!o#S#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%m%t&R&k&n&o&r&t&u&w&{'T'b'r(V(](d(x(z)O)s)}*i+X+]+g,p,s,x-U-X-i-q.P.V.g.t.{/V/n0]0l0r1S1r2S2T2V2X2[2_2a2p3Q3W3d3l4T4z5w6T6e6f6i6s6|7[8t9T9_:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>R>S[#]WZ#W#Z'X(T!b%jm#h#i#l$x%e%h(^(h(i(j*Y*^*b+Z+[+^,o-V.T.Z.[.]._/m/p2d3[3]4a6r7TQ%wxQ%{yW&Q|&V&W,OQ&_!TQ'c!hQ'e!iQ(q#sS+n%|%}Q+r&PQ,_&bQ,c&dS-l'f'gQ.i(rQ1R+oQ1X+uQ1Z+vQ1^+zQ1t,`S1x,d,eQ2|-mQ5e1TQ5i1WQ5n1`Q6Z1yQ8_5gQ8b5kQ8f5pQ:T8^R;T:U!U$zi$d%O%Q%^%_%c*R*T*a*w*x/P/x0`0b0i0j0o4_5Q8V9p>P>X>Y!^%yy!i!u%{%|%}'V'e'f'g'k'u*j+n+o-Y-l-m-t0R0U1R2u2|3T4r4s4v7}9{Q+h%wQ,T&[Q,W&]Q,b&dQ.h(qQ1s,_U1w,c,d,eQ3e.iQ6U1tS6Y1x1yQ8x6Z#f>T#v$b$c$x${)y*V*Y*g+f+i,S,V.f/d/m/y/{1f1i1q3c4^4j4o5[5_6S7W7v8Q8[8q9b9y:P:`:r;Q;[;d;k<o<q<u<w<y=S=U=X=]=_=a=c=g>]>^o>U<l<m<p<r<v<x<z=T=V=Y=^=`=b=d=hW%Ti%V*y>PS&[!Q&iQ&]!RQ&^!SU*}%[%d=sR,R&Y%]%Si#v$b$c$d$x${%O%Q%^%_%c)y*R*T*V*Y*a*g*w*x+f+i,S,V.f/P/d/m/x/y/{0`0b0i0j0o1f1i1q3c4^4_4j4o5Q5[5_6S7W7v8Q8V8[8q9b9p9y:P:`:r;Q;[;d;k<l<m<o<p<q<r<u<v<w<x<y<z=S=T=U=V=X=Y=]=^=_=`=a=b=c=d=g=h>P>X>Y>]>^T)z$u){V+P%]<s<tW'[!e%i*Z-`S(}#y#zQ+c%rQ+y&SS.b(m(nQ1j,XQ5T0kR8i5u'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%m%t&R&k&n&o&r&t&u&w&{'T'X'b'r(T(V(](d(x(z)O)s)}*i+X+]+g,p,s,x-U-X-i-q.P.V.g.t.{/V/n0]0l0r1S1r2S2T2V2X2[2_2a2p3Q3W3d3l4T4z5w6T6e6f6i6s6|7[8t9T9_:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>S$i$^c#Y#e%q%s%u(S(Y(t(y)R)S)T)U)V)W)X)Y)Z)[)^)`)b)g)q+d+x-Z-x-}.S.U.s.v.z.|.}/O/b0p2k2n3O3V3k3p3q3r3s3t3u3v3w3x3y3z3{3|4P4Q4X5X5c6u6{7Q7a7b7k7l8k9X9]9g9m9n:o;W;`<W=vT#TV#U'RkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%m%t&R&k&n&o&r&t&u&w&{'T'X'b'r(T(V(](d(x(z)O)s)}*i+X+]+g,p,s,x-U-X-i-q.P.V.g.t.{/V/n0]0l0r1S1r2S2T2V2X2[2_2a2p3Q3W3d3l4T4z5w6T6e6f6i6s6|7[8t9T9_:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>SQ'Y!eR2q-]!W!nQ!e!r!v!y!z$|'W'_'`'l'm'n*Z*k*o*q*r-]-c-e-u0[0_1o5{5}R1l,ZnqOXst!Z#d%m&r&t&u&w,s,x2[2_Q&y!^Q'v!xS(s#u<^Q+l%zQ,]&_Q,^&aQ-j'dQ-w'oS.r(x=PS0q+X=ZQ1P+mQ1n,[Q2c,zQ2e,{Q2m-WQ2z-kQ2}-oS5Y0r=eQ5a1QS5d1S=fQ6t2oQ6x2{Q6}3SQ8]5bQ9Y6vQ9Z6yQ9^7OR:l9V$d$]c#Y#e%s%u(S(Y(t(y)R)S)T)U)V)W)X)Y)Z)[)^)`)b)g)q+d+x-Z-x-}.S.U.s.v.z.}/O/b0p2k2n3O3V3k3p3q3r3s3t3u3v3w3x3y3z3{3|4P4Q4X5X5c6u6{7Q7a7b7k7l8k9X9]9g9m9n:o;W;`<W=vS(o#p'iQ)P#zS+b%q.|S.c(n(pR3^.d'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%m%t&R&k&n&o&r&t&u&w&{'T'X'b'r(T(V(](d(x(z)O)s)}*i+X+]+g,p,s,x-U-X-i-q.P.V.g.t.{/V/n0]0l0r1S1r2S2T2V2X2[2_2a2p3Q3W3d3l4T4z5w6T6e6f6i6s6|7[8t9T9_:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>SS#q]<VQ&t!XQ&u!YQ&w![Q&x!]R2Z,vQ'a!hQ+e%wQ-h'cS.e(q+hQ2x-gW3b.h.i0w0yQ6w2yW7U3_3a3e5^U9a7V7X7ZU:q9c9d9fS;b:p:sQ;p;cR;x;qU!wQ'`-eT5y1o5{!Q_OXZ`st!V!Z#d#h%e%m&i&k&r&t&u&w(j,s,x.[2[2_]!pQ!r'`-e1o5{T#q]<V%^{OPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%m%t&R&k&n&o&r&t&u&w&{'T'b'r(T(V(](d(x(z)O)}*i+X+]+g,p,s,x-i-q.P.V.g.t.{/n0]0l0r1S1r2S2T2V2X2[2_2a3Q3W3d3l4z6T6e6f6i6|7[8t9T9_S(}#y#zS.b(m(n!s=l$Z$n'X)s-U-X/V2p4T5w6s:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>SU$fd)_,mS(p#p'iU*v%R(w4OU0m+O.n7gQ5^0xQ7V3`Q9d7YR:s9em!tQ!r!v!y!z'`'l'm'n-e-u1o5{5}Q't!uS(f#g2US-s'k'wQ/s*]Q0R*jQ3U-vQ4f/tQ4r0TQ4s0UQ4x0^Q7r4`S7}4t4vS8R4y4{Q9r7sQ9v7yQ9{8OQ:Q8TS:{9w9xS;g:|;PS;s;h;iS;{;t;uS<P;|;}R<S<QQ#wbQ's!uS(e#g2US(g#m+WQ+Y%fQ+j%xQ+p&OU-r'k't'wQ.W(fU/r*]*`/wQ0S*jQ0V*lQ1O+kQ1u,aS3R-s-vQ3Z.`S4e/s/tQ4n0PS4q0R0^Q4u0WQ6W1vQ7P3US7q4`4bQ7u4fU7|4r4x4{Q8P4wQ8v6XS9q7r7sQ9u7yQ9}8RQ:O8SQ:c8wQ:y9rS:z9v9xQ;S:QQ;^:dS;f:{;PS;r;g;hS;z;s;uS<O;{;}Q<R<PQ<T<SQ=o=jQ={=tR=|=uV!wQ'`-e%^aOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%m%t&R&k&n&o&r&t&u&w&{'T'b'r(T(V(](d(x(z)O)}*i+X+]+g,p,s,x-i-q.P.V.g.t.{/n0]0l0r1S1r2S2T2V2X2[2_2a3Q3W3d3l4z6T6e6f6i6|7[8t9T9_S#wz!j!r=i$Z$n'X)s-U-X/V2p4T5w6s:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>SR=o>R%^bOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%m%t&R&k&n&o&r&t&u&w&{'T'b'r(T(V(](d(x(z)O)}*i+X+]+g,p,s,x-i-q.P.V.g.t.{/n0]0l0r1S1r2S2T2V2X2[2_2a3Q3W3d3l4z6T6e6f6i6|7[8t9T9_Q%fj!^%xy!i!u%{%|%}'V'e'f'g'k'u*j+n+o-Y-l-m-t0R0U1R2u2|3T4r4s4v7}9{S&Oz!jQ+k%yQ,a&dW1v,b,c,d,eU6X1w1x1yS8w6Y6ZQ:d8x!r=j$Z$n'X)s-U-X/V2p4T5w6s:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>SQ=t>QR=u>R%QeOPXYstuvw!Z!`!g!o#S#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%m%t&R&k&n&r&t&u&w&{'T'b'r(V(](d(x(z)O)}*i+X+]+g,p,s,x-i-q.P.V.g.t.{/n0]0l0r1S1r2S2T2V2X2[2_2a3Q3W3d3l4z6T6e6f6i6|7[8t9T9_Y#bWZ#W#Z(T!b%jm#h#i#l$x%e%h(^(h(i(j*Y*^*b+Z+[+^,o-V.T.Z.[.]._/m/p2d3[3]4a6r7TQ,n&o!p=k$Z$n)s-U-X/V2p4T5w6s:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>SR=n'XU']!e%i*ZR2s-`%SdOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%m%t&R&k&n&r&t&u&w&{'T'b'r(T(V(](d(x(z)O)}*i+X+],p,s,x-i-q.P.V.t.{/n0]0l0r1S1r2S2T2V2X2[2_2a3Q3W3l4z6T6e6f6i6|8t9T9_!r)_$Z$n'X)s-U-X/V2p4T5w6s:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>SQ,m&oQ0x+gQ3`.gQ7Y3dR9e7[!b$Tc#Y%q(S(Y(t(y)Z)[)`)g+x-x-}.S.U.s.v/b0p3O3V3k3{5X5c6{7Q7a9]:o<W!P<d)^)q-Z.|2k2n3p3y3z4P4X6u7b7k7l8k9X9g9m9n;W;`=v!f$Vc#Y%q(S(Y(t(y)W)X)Z)[)`)g+x-x-}.S.U.s.v/b0p3O3V3k3{5X5c6{7Q7a9]:o<W!T<f)^)q-Z.|2k2n3p3v3w3y3z4P4X6u7b7k7l8k9X9g9m9n;W;`=v!^$Zc#Y%q(S(Y(t(y)`)g+x-x-}.S.U.s.v/b0p3O3V3k3{5X5c6{7Q7a9]:o<WQ4_/kz>S)^)q-Z.|2k2n3p4P4X6u7b7k7l8k9X9g9m9n;W;`=vQ>X>ZR>Y>['QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%m%t&R&k&n&o&r&t&u&w&{'T'X'b'r(T(V(](d(x(z)O)s)}*i+X+]+g,p,s,x-U-X-i-q.P.V.g.t.{/V/n0]0l0r1S1r2S2T2V2X2[2_2a2p3Q3W3d3l4T4z5w6T6e6f6i6s6|7[8t9T9_:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>SS$oh$pR4U/U'XgOPWXYZhstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n$p%m%t&R&k&n&o&r&t&u&w&{'T'X'b'r(T(V(](d(x(z)O)s)}*i+X+]+g,p,s,x-U-X-i-q.P.V.g.t.{/U/V/n0]0l0r1S1r2S2T2V2X2[2_2a2p3Q3W3d3l4T4z5w6T6e6f6i6s6|7[8t9T9_:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>ST$kf$qQ$ifS)j$l)nR)v$qT$jf$qT)l$l)n'XhOPWXYZhstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n$p%m%t&R&k&n&o&r&t&u&w&{'T'X'b'r(T(V(](d(x(z)O)s)}*i+X+]+g,p,s,x-U-X-i-q.P.V.g.t.{/U/V/n0]0l0r1S1r2S2T2V2X2[2_2a2p3Q3W3d3l4T4z5w6T6e6f6i6s6|7[8t9T9_:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>ST$oh$pQ$rhR)u$p%^jOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%m%t&R&k&n&o&r&t&u&w&{'T'b'r(T(V(](d(x(z)O)}*i+X+]+g,p,s,x-i-q.P.V.g.t.{/n0]0l0r1S1r2S2T2V2X2[2_2a3Q3W3d3l4z6T6e6f6i6|7[8t9T9_!s>Q$Z$n'X)s-U-X/V2p4T5w6s:Z:m<U<X<Y<]<^<_<`<a<b<c<d<e<f<g<h<i<k<n<{=O=P=R=Z=[=e=f>S#glOPXZst!Z!`!o#S#d#o#{$n%m&k&n&o&r&t&u&w&{'T'b)O)s*i+]+g,p,s,x-i.g/V/n0]0l1r2S2T2V2X2[2_2a3d4T4z6T6e6f6i7[8t9T!U%Ri$d%O%Q%^%_%c*R*T*a*w*x/P/x0`0b0i0j0o4_5Q8V9p>P>X>Y#f(w#v$b$c$x${)y*V*Y*g+f+i,S,V.f/d/m/y/{1f1i1q3c4^4j4o5[5_6S7W7v8Q8[8q9b9y:P:`:r;Q;[;d;k<o<q<u<w<y=S=U=X=]=_=a=c=g>]>^Q+T%aQ/c*Oo4O<l<m<p<r<v<x<z=T=V=Y=^=`=b=d=h!U$yi$d%O%Q%^%_%c*R*T*a*w*x/P/x0`0b0i0j0o4_5Q8V9p>P>X>YQ*c$zU*l$|*Z*oQ+U%bQ0W*m#f=q#v$b$c$x${)y*V*Y*g+f+i,S,V.f/d/m/y/{1f1i1q3c4^4j4o5[5_6S7W7v8Q8[8q9b9y:P:`:r;Q;[;d;k<o<q<u<w<y=S=U=X=]=_=a=c=g>]>^n=r<l<m<p<r<v<x<z=T=V=Y=^=`=b=d=hQ=w>TQ=x>UQ=y>VR=z>W!U%Ri$d%O%Q%^%_%c*R*T*a*w*x/P/x0`0b0i0j0o4_5Q8V9p>P>X>Y#f(w#v$b$c$x${)y*V*Y*g+f+i,S,V.f/d/m/y/{1f1i1q3c4^4j4o5[5_6S7W7v8Q8[8q9b9y:P:`:r;Q;[;d;k<o<q<u<w<y=S=U=X=]=_=a=c=g>]>^o4O<l<m<p<r<v<x<z=T=V=Y=^=`=b=d=hnoOXst!Z#d%m&r&t&u&w,s,x2[2_S*f${*YQ-R'OQ-S'QR4i/y%[%Si#v$b$c$d$x${%O%Q%^%_%c)y*R*T*V*Y*a*g*w*x+f+i,S,V.f/P/d/m/x/y/{0`0b0i0j0o1f1i1q3c4^4_4j4o5Q5[5_6S7W7v8Q8V8[8q9b9p9y:P:`:r;Q;[;d;k<l<m<o<p<q<r<u<v<w<x<y<z=S=T=U=V=X=Y=]=^=_=`=a=b=c=d=g=h>P>X>Y>]>^Q,U&]Q1h,WQ5s1gR8h5tV*n$|*Z*oU*n$|*Z*oT5z1o5{S0P*i/nQ4w0]T8S4z:]Q+j%xQ0V*lQ1O+kQ1u,aQ6W1vQ8v6XQ:c8wR;^:d!U%Oi$d%O%Q%^%_%c*R*T*a*w*x/P/x0`0b0i0j0o4_5Q8V9p>P>X>Yx*R$v)e*S*u+V/v0d0e4R4g5R5S5W7p8U:R:x=p=}>OS0`*t0a#f<o#v$b$c$x${)y*V*Y*g+f+i,S,V.f/d/m/y/{1f1i1q3c4^4j4o5[5_6S7W7v8Q8[8q9b9y:P:`:r;Q;[;d;k<o<q<u<w<y=S=U=X=]=_=a=c=g>]>^n<p<l<m<p<r<v<x<z=T=V=Y=^=`=b=d=h!d=S(u)c*[*e.j.m.q/_/k/|0v1e3h4[4h4l5r7]7`7w7z8X8Z9t9|:S:};R;e;j;v>Z>[`=T3}7c7f7j9h:t:w;yS=_.l3iT=`7e9k!U%Qi$d%O%Q%^%_%c*R*T*a*w*x/P/x0`0b0i0j0o4_5Q8V9p>P>X>Y|*T$v)e*U*t+V/g/v0d0e4R4g4|5R5S5W7p8U:R:x=p=}>OS0b*u0c#f<q#v$b$c$x${)y*V*Y*g+f+i,S,V.f/d/m/y/{1f1i1q3c4^4j4o5[5_6S7W7v8Q8[8q9b9y:P:`:r;Q;[;d;k<o<q<u<w<y=S=U=X=]=_=a=c=g>]>^n<r<l<m<p<r<v<x<z=T=V=Y=^=`=b=d=h!h=U(u)c*[*e.k.l.q/_/k/|0v1e3f3h4[4h4l5r7]7^7`7w7z8X8Z9t9|:S:};R;e;j;v>Z>[d=V3}7d7e7j9h9i:t:u:w;yS=a.m3jT=b7f9lrnOXst!V!Z#d%m&i&r&t&u&w,s,x2[2_Q&f!UR,p&ornOXst!V!Z#d%m&i&r&t&u&w,s,x2[2_R&f!UQ,Y&^R1d,RsnOXst!V!Z#d%m&i&r&t&u&w,s,x2[2_Q1p,_S6R1s1tU8p6P6Q6US:_8r8sS;Y:^:aQ;m;ZR;w;nQ&m!VR,i&iR6_1|R:f8yW&Q|&V&W,OR1Z+vQ&r!WR,s&sR,y&xT2],x2_R,}&yQ,|&yR2f,}Q'y!{R-y'ySsOtQ#dXT%ps#dQ#OTR'{#OQ#RUR'}#RQ){$uR/`){Q#UVR(Q#UQ#XWU(W#X(X.QQ(X#YR.Q(YQ-^'YR2r-^Q.u(yS3m.u3nR3n.vQ-e'`R2v-eY!rQ'`-e1o5{R'j!rQ/Q)eR4S/QU#_W%h*YU(_#_(`.RQ(`#`R.R(ZQ-a']R2t-at`OXst!V!Z#d%m&i&k&r&t&u&w,s,x2[2_S#hZ%eU#r`#h.[R.[(jQ(k#jQ.X(gW.a(k.X3X7RQ3X.YR7R3YQ)n$lR/W)nQ$phR)t$pQ$`cU)a$`-|<jQ-|<WR<j)qQ/q*]W4c/q4d7t9sU4d/r/s/tS7t4e4fR9s7u$e*Q$v(u)c)e*[*e*t*u+Q+R+V.l.m.o.p.q/_/g/i/k/v/|0d0e0v1e3f3g3h3}4R4[4g4h4l4|5O5R5S5W5r7]7^7_7`7e7f7h7i7j7p7w7z8U8X8Z9h9i9j9t9|:R:S:t:u:v:w:x:};R;e;j;v;y=p=}>O>Z>[Q/z*eU4k/z4m7xQ4m/|R7x4lS*o$|*ZR0Y*ox*S$v)e*t*u+V/v0d0e4R4g5R5S5W7p8U:R:x=p=}>O!d.j(u)c*[*e.l.m.q/_/k/|0v1e3h4[4h4l5r7]7`7w7z8X8Z9t9|:S:};R;e;j;v>Z>[U/h*S.j7ca7c3}7e7f7j9h:t:w;yQ0a*tQ3i.lU4}0a3i9kR9k7e|*U$v)e*t*u+V/g/v0d0e4R4g4|5R5S5W7p8U:R:x=p=}>O!h.k(u)c*[*e.l.m.q/_/k/|0v1e3f3h4[4h4l5r7]7^7`7w7z8X8Z9t9|:S:};R;e;j;v>Z>[U/j*U.k7de7d3}7e7f7j9h9i:t:u:w;yQ0c*uQ3j.mU5P0c3j9lR9l7fQ*z%UR0g*zQ5]0vR8Y5]Q+_%kR0u+_Q5v1jS8j5v:[R:[8kQ,[&_R1m,[Q5{1oR8m5{Q1{,fS6]1{8zR8z6_Q1U+rW5h1U5j8a:VQ5j1XQ8a5iR:V8bQ+w&QR1[+wQ2_,xR6m2_YrOXst#dQ&v!ZQ+a%mQ,r&rQ,t&tQ,u&uQ,w&wQ2Y,sS2],x2_R6l2[Q%opQ&z!_Q&}!aQ'P!bQ'R!cQ'q!uQ+`%lQ+l%zQ,Q&XQ,h&mQ-P&|W-p'k's't'wQ-w'oQ0X*nQ1P+mQ1c,PS2O,i,lQ2g-OQ2h-RQ2i-SQ2}-oW3P-r-s-v-xQ5a1QQ5m1_Q5q1eQ6V1uQ6a2QQ6k2ZU6z3O3R3UQ6}3SQ8]5bQ8e5oQ8g5rQ8l5zQ8u6WQ8{6`S9[6{7PQ9^7OQ:W8cQ:b8vQ:g8|Q:n9]Q;U:XQ;]:cQ;a:oQ;l;VR;o;^Q%zyQ'd!iQ'o!uU+m%{%|%}Q-W'VU-k'e'f'gS-o'k'uQ0Q*jS1Q+n+oQ2o-YS2{-l-mQ3S-tS4p0R0UQ5b1RQ6v2uQ6y2|Q7O3TU7{4r4s4vQ9z7}R;O9{S$wi>PR*{%VU%Ui%V>PR0f*yQ$viS(u#v+iS)c$b$cQ)e$dQ*[$xS*e${*YQ*t%OQ*u%QQ+Q%^Q+R%_Q+V%cQ.l<oQ.m<qQ.o<uQ.p<wQ.q<yQ/_)yQ/g*RQ/i*TQ/k*VQ/v*aS/|*g/mQ0d*wQ0e*xl0v+f,V.f1i1q3c6S7W8q9b:`:r;[;dQ1e,SQ3f=SQ3g=UQ3h=XS3}<l<mQ4R/PS4[/d4^Q4g/xQ4h/yQ4l/{Q4|0`Q5O0bQ5R0iQ5S0jQ5W0oQ5r1fQ7]=]Q7^=_Q7_=aQ7`=cQ7e<pQ7f<rQ7h<vQ7i<xQ7j<zQ7p4_Q7w4jQ7z4oQ8U5QQ8X5[Q8Z5_Q9h=YQ9i=TQ9j=VQ9t7vQ9|8QQ:R8VQ:S8[Q:t=^Q:u=`Q:v=bQ:w=dQ:x9pQ:}9yQ;R:PQ;e=gQ;j;QQ;v;kQ;y=hQ=p>PQ=}>XQ>O>YQ>Z>]R>[>^Q+O%]Q.n<sR7g<tnpOXst!Z#d%m&r&t&u&w,s,x2[2_Q!fPS#fZ#oQ&|!`W'h!o*i0]4zQ(P#SQ)Q#{Q)r$nS,l&k&nQ,q&oQ-O&{S-T'T/nQ-g'bQ.x)OQ/[)sQ0s+]Q0y+gQ2W,pQ2y-iQ3a.gQ4W/VQ5U0lQ6Q1rQ6c2SQ6d2TQ6h2VQ6j2XQ6o2aQ7Z3dQ7m4TQ8s6TQ9P6eQ9Q6fQ9S6iQ9f7[Q:a8tR:k9T#[cOPXZst!Z!`!o#d#o#{%m&k&n&o&r&t&u&w&{'T'b)O*i+]+g,p,s,x-i.g/n0]0l1r2S2T2V2X2[2_2a3d4z6T6e6f6i7[8t9TQ#YWQ#eYQ%quQ%svS%uw!gS(S#W(VQ(Y#ZQ(t#uQ(y#xQ)R$OQ)S$PQ)T$QQ)U$RQ)V$SQ)W$TQ)X$UQ)Y$VQ)Z$WQ)[$XQ)^$ZQ)`$_Q)b$aQ)g$eW)q$n)s/V4TQ+d%tQ+x&RS-Z'X2pQ-x'rS-}(T.PQ.S(]Q.U(dQ.s(xQ.v(zQ.z<UQ.|<XQ.}<YQ/O<]Q/b)}Q0p+XQ2k-UQ2n-XQ3O-qQ3V.VQ3k.tQ3p<^Q3q<_Q3r<`Q3s<aQ3t<bQ3u<cQ3v<dQ3w<eQ3x<fQ3y<gQ3z<hQ3{.{Q3|<kQ4P<nQ4Q<{Q4X<iQ5X0rQ5c1SQ6u=OQ6{3QQ7Q3WQ7a3lQ7b=PQ7k=RQ7l=ZQ8k5wQ9X6sQ9]6|Q9g=[Q9m=eQ9n=fQ:o9_Q;W:ZQ;`:mQ<W#SR=v>SR#[WR'Z!el!tQ!r!v!y!z'`'l'm'n-e-u1o5{5}S'V!e-]U*j$|*Z*oS-Y'W'_S0U*k*qQ0^*rQ2u-cQ4v0[R4{0_R({#xQ!fQT-d'`-e]!qQ!r'`-e1o5{Q#p]R'i<VR)f$dY!uQ'`-e1o5{Q'k!rS'u!v!yS'w!z5}S-t'l'mQ-v'nR3T-uT#kZ%eS#jZ%eS%km,oU(g#h#i#lS.Y(h(iQ.^(jQ0t+^Q3Y.ZU3Z.[.]._S7S3[3]R9`7Td#^W#W#Z%h(T(^*Y+Z.T/mr#gZm#h#i#l%e(h(i(j+^.Z.[.]._3[3]7TS*]$x*bQ/t*^Q2U,oQ2l-VQ4`/pQ6q2dQ7s4aQ9W6rT=m'X+[V#aW%h*YU#`W%h*YS(U#W(^U(Z#Z+Z/mS-['X+[T.O(T.TV'^!e%i*ZQ$lfR)x$qT)m$l)nR4V/UT*_$x*bT*h${*YQ0w+fQ1g,VQ3_.fQ5t1iQ6P1qQ7X3cQ8r6SQ9c7WQ:^8qQ:p9bQ;Z:`Q;c:rQ;n;[R;q;dnqOXst!Z#d%m&r&t&u&w,s,x2[2_Q&l!VR,h&itmOXst!U!V!Z#d%m&i&r&t&u&w,s,x2[2_R,o&oT%lm,oR1k,XR,g&gQ&U|S+}&V&WR1^,OR+s&PT&p!W&sT&q!W&sT2^,x2_",nodeNames:"⚠ ArithOp ArithOp ?. JSXStartTag LineComment BlockComment Script Hashbang ExportDeclaration export Star as VariableName String Escape from ; default FunctionDeclaration async function VariableDefinition > < TypeParamList in out const TypeDefinition extends ThisType this LiteralType ArithOp Number BooleanLiteral TemplateType InterpolationEnd Interpolation InterpolationStart NullType null VoidType void TypeofType typeof MemberExpression . PropertyName [ TemplateString Escape Interpolation super RegExp ] ArrayExpression Spread , } { ObjectExpression Property async get set PropertyDefinition Block : NewTarget new NewExpression ) ( ArgList UnaryExpression delete LogicOp BitOp YieldExpression yield AwaitExpression await ParenthesizedExpression ClassExpression class ClassBody MethodDeclaration Decorator @ MemberExpression PrivatePropertyName CallExpression TypeArgList CompareOp < declare Privacy static abstract override PrivatePropertyDefinition PropertyDeclaration readonly accessor Optional TypeAnnotation Equals StaticBlock FunctionExpression ArrowFunction ParamList ParamList ArrayPattern ObjectPattern PatternProperty Privacy readonly Arrow MemberExpression BinaryExpression ArithOp ArithOp ArithOp ArithOp BitOp CompareOp instanceof satisfies CompareOp BitOp BitOp BitOp LogicOp LogicOp ConditionalExpression LogicOp LogicOp AssignmentExpression UpdateOp PostfixExpression CallExpression InstantiationExpression TaggedTemplateExpression DynamicImport import ImportMeta JSXElement JSXSelfCloseEndTag JSXSelfClosingTag JSXIdentifier JSXBuiltin JSXIdentifier JSXNamespacedName JSXMemberExpression JSXSpreadAttribute JSXAttribute JSXAttributeValue JSXEscape JSXEndTag JSXOpenTag JSXFragmentTag JSXText JSXEscape JSXStartCloseTag JSXCloseTag PrefixCast < ArrowFunction TypeParamList SequenceExpression InstantiationExpression KeyofType keyof UniqueType unique ImportType InferredType infer TypeName ParenthesizedType FunctionSignature ParamList NewSignature IndexedType TupleType Label ArrayType ReadonlyType ObjectType MethodType PropertyType IndexSignature PropertyDefinition CallSignature TypePredicate asserts is NewSignature new UnionType LogicOp IntersectionType LogicOp ConditionalType ParameterizedType ClassDeclaration abstract implements type VariableDeclaration let var using TypeAliasDeclaration InterfaceDeclaration interface EnumDeclaration enum EnumBody NamespaceDeclaration namespace module AmbientDeclaration declare GlobalDeclaration global ClassDeclaration ClassBody AmbientFunctionDeclaration ExportGroup VariableName VariableName ImportDeclaration defer ImportGroup ForStatement for ForSpec ForInSpec ForOfSpec of WhileStatement while WithStatement with DoStatement do IfStatement if else SwitchStatement switch SwitchBody CaseLabel case DefaultLabel TryStatement try CatchClause catch FinallyClause finally ReturnStatement return ThrowStatement throw BreakStatement break ContinueStatement continue DebuggerStatement debugger LabeledStatement ExpressionStatement SingleExpression SingleClassItem",maxTerm:380,context:vd,nodeProps:[["isolate",-8,5,6,14,37,39,51,53,55,""],["group",-26,9,17,19,68,207,211,215,216,218,221,224,234,237,243,245,247,249,252,258,264,266,268,270,272,274,275,"Statement",-34,13,14,32,35,36,42,51,54,55,57,62,70,72,76,80,82,84,85,110,111,120,121,136,139,141,142,143,144,145,147,148,167,169,171,"Expression",-23,31,33,37,41,43,45,173,175,177,178,180,181,182,184,185,186,188,189,190,201,203,205,206,"Type",-3,88,103,109,"ClassItem"],["openedBy",23,"<",38,"InterpolationStart",56,"[",60,"{",73,"(",160,"JSXStartCloseTag"],["closedBy",-2,24,168,">",40,"InterpolationEnd",50,"]",61,"}",74,")",165,"JSXEndTag"]],propSources:[Cd],skippedNodes:[0,5,6,278],repeatNodeCount:37,tokenData:"$Fq07[R!bOX%ZXY+gYZ-yZ[+g[]%Z]^.c^p%Zpq+gqr/mrs3cst:_tuEruvJSvwLkwx! Yxy!'iyz!(sz{!)}{|!,q|}!.O}!O!,q!O!P!/Y!P!Q!9j!Q!R#:O!R![#<_![!]#I_!]!^#Jk!^!_#Ku!_!`$![!`!a$$v!a!b$*T!b!c$,r!c!}Er!}#O$-|#O#P$/W#P#Q$4o#Q#R$5y#R#SEr#S#T$7W#T#o$8b#o#p$<r#p#q$=h#q#r$>x#r#s$@U#s$f%Z$f$g+g$g#BYEr#BY#BZ$A`#BZ$ISEr$IS$I_$A`$I_$I|Er$I|$I}$Dk$I}$JO$Dk$JO$JTEr$JT$JU$A`$JU$KVEr$KV$KW$A`$KW&FUEr&FU&FV$A`&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$A`?HUOEr(n%d_$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&j&hT$i&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c&j&zP;=`<%l&c'|'U]$i&j(Z!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!b(SU(Z!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!b(iP;=`<%l'}'|(oP;=`<%l&}'[(y]$i&j(WpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(rp)wU(WpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)rp*^P;=`<%l)r'[*dP;=`<%l(r#S*nX(Wp(Z!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g#S+^P;=`<%l*g(n+dP;=`<%l%Z07[+rq$i&j(Wp(Z!b'|0/lOX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p$f%Z$f$g+g$g#BY%Z#BY#BZ+g#BZ$IS%Z$IS$I_+g$I_$JT%Z$JT$JU+g$JU$KV%Z$KV$KW+g$KW&FU%Z&FU&FV+g&FV;'S%Z;'S;=`+a<%l?HT%Z?HT?HU+g?HUO%Z07[.ST(X#S$i&j'}0/lO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c07[.n_$i&j(Wp(Z!b'}0/lOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z)3p/x`$i&j!p),Q(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW1V`#v(Ch$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`2X!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW2d_#v(Ch$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'At3l_(V':f$i&j(Z!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k(^4r_$i&j(Z!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k&z5vX$i&jOr5qrs6cs!^5q!^!_6y!_#o5q#o#p6y#p;'S5q;'S;=`7h<%lO5q&z6jT$d`$i&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c`6|TOr6yrs7]s;'S6y;'S;=`7b<%lO6y`7bO$d``7eP;=`<%l6y&z7kP;=`<%l5q(^7w]$d`$i&j(Z!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!r8uZ(Z!bOY8pYZ6yZr8prs9hsw8pwx6yx#O8p#O#P6y#P;'S8p;'S;=`:R<%lO8p!r9oU$d`(Z!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!r:UP;=`<%l8p(^:[P;=`<%l4k%9[:hh$i&j(Wp(Z!bOY%ZYZ&cZq%Zqr<Srs&}st%ZtuCruw%Zwx(rx!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr(r<__WS$i&j(Wp(Z!bOY<SYZ&cZr<Srs=^sw<Swx@nx!^<S!^!_Bm!_#O<S#O#P>`#P#o<S#o#pBm#p;'S<S;'S;=`Cl<%lO<S(Q=g]WS$i&j(Z!bOY=^YZ&cZw=^wx>`x!^=^!^!_?q!_#O=^#O#P>`#P#o=^#o#p?q#p;'S=^;'S;=`@h<%lO=^&n>gXWS$i&jOY>`YZ&cZ!^>`!^!_?S!_#o>`#o#p?S#p;'S>`;'S;=`?k<%lO>`S?XSWSOY?SZ;'S?S;'S;=`?e<%lO?SS?hP;=`<%l?S&n?nP;=`<%l>`!f?xWWS(Z!bOY?qZw?qwx?Sx#O?q#O#P?S#P;'S?q;'S;=`@b<%lO?q!f@eP;=`<%l?q(Q@kP;=`<%l=^'`@w]WS$i&j(WpOY@nYZ&cZr@nrs>`s!^@n!^!_Ap!_#O@n#O#P>`#P#o@n#o#pAp#p;'S@n;'S;=`Bg<%lO@ntAwWWS(WpOYApZrAprs?Ss#OAp#O#P?S#P;'SAp;'S;=`Ba<%lOAptBdP;=`<%lAp'`BjP;=`<%l@n#WBvYWS(Wp(Z!bOYBmZrBmrs?qswBmwxApx#OBm#O#P?S#P;'SBm;'S;=`Cf<%lOBm#WCiP;=`<%lBm(rCoP;=`<%l<S%9[C}i$i&j(o%1l(Wp(Z!bOY%ZYZ&cZr%Zrs&}st%ZtuCruw%Zwx(rx!Q%Z!Q![Cr![!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr%9[EoP;=`<%lCr07[FRk$i&j(Wp(Z!b$]#t(T,2j(e$I[OY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr+dHRk$i&j(Wp(Z!b$]#tOY%ZYZ&cZr%Zrs&}st%ZtuGvuw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Gv![!^%Z!^!_*g!_!c%Z!c!}Gv!}#O%Z#O#P&c#P#R%Z#R#SGv#S#T%Z#T#oGv#o#p*g#p$g%Z$g;'SGv;'S;=`Iv<%lOGv+dIyP;=`<%lGv07[JPP;=`<%lEr(KWJ_`$i&j(Wp(Z!b#p(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KWKl_$i&j$Q(Ch(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z,#xLva(z+JY$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sv%ZvwM{wx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KWNW`$i&j#z(Ch(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'At! c_(Y';W$i&j(WpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b'l!!i_$i&j(WpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b&z!#mX$i&jOw!#hwx6cx!^!#h!^!_!$Y!_#o!#h#o#p!$Y#p;'S!#h;'S;=`!$r<%lO!#h`!$]TOw!$Ywx7]x;'S!$Y;'S;=`!$l<%lO!$Y`!$oP;=`<%l!$Y&z!$uP;=`<%l!#h'l!%R]$d`$i&j(WpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(r!Q!&PZ(WpOY!%zYZ!$YZr!%zrs!$Ysw!%zwx!&rx#O!%z#O#P!$Y#P;'S!%z;'S;=`!']<%lO!%z!Q!&yU$d`(WpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)r!Q!'`P;=`<%l!%z'l!'fP;=`<%l!!b/5|!'t_!l/.^$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#&U!)O_!k!Lf$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z-!n!*[b$i&j(Wp(Z!b(U%&f#q(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rxz%Zz{!+d{!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW!+o`$i&j(Wp(Z!b#n(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z+;x!,|`$i&j(Wp(Z!br+4YOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z,$U!.Z_!]+Jf$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[!/ec$i&j(Wp(Z!b!Q.2^OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!0p!P!Q%Z!Q![!3Y![!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#%|!0ya$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!2O!P!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#%|!2Z_![!L^$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!3eg$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!3Y![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S!3Y#S#X%Z#X#Y!4|#Y#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!5Vg$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx{%Z{|!6n|}%Z}!O!6n!O!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!6wc$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!8_c$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[!9uf$i&j(Wp(Z!b#o(ChOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcxz!;Zz{#-}{!P!;Z!P!Q#/d!Q!^!;Z!^!_#(i!_!`#7S!`!a#8i!a!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z?O!;fb$i&j(Wp(Z!b!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z>^!<w`$i&j(Z!b!X7`OY!<nYZ&cZw!<nwx!=yx!P!<n!P!Q!Eq!Q!^!<n!^!_!Gr!_!}!<n!}#O!KS#O#P!Dy#P#o!<n#o#p!Gr#p;'S!<n;'S;=`!L]<%lO!<n<z!>Q^$i&j!X7`OY!=yYZ&cZ!P!=y!P!Q!>|!Q!^!=y!^!_!@c!_!}!=y!}#O!CW#O#P!Dy#P#o!=y#o#p!@c#p;'S!=y;'S;=`!Ek<%lO!=y<z!?Td$i&j!X7`O!^&c!_#W&c#W#X!>|#X#Z&c#Z#[!>|#[#]&c#]#^!>|#^#a&c#a#b!>|#b#g&c#g#h!>|#h#i&c#i#j!>|#j#k!>|#k#m&c#m#n!>|#n#o&c#p;'S&c;'S;=`&w<%lO&c7`!@hX!X7`OY!@cZ!P!@c!P!Q!AT!Q!}!@c!}#O!Ar#O#P!Bq#P;'S!@c;'S;=`!CQ<%lO!@c7`!AYW!X7`#W#X!AT#Z#[!AT#]#^!AT#a#b!AT#g#h!AT#i#j!AT#j#k!AT#m#n!AT7`!AuVOY!ArZ#O!Ar#O#P!B[#P#Q!@c#Q;'S!Ar;'S;=`!Bk<%lO!Ar7`!B_SOY!ArZ;'S!Ar;'S;=`!Bk<%lO!Ar7`!BnP;=`<%l!Ar7`!BtSOY!@cZ;'S!@c;'S;=`!CQ<%lO!@c7`!CTP;=`<%l!@c<z!C][$i&jOY!CWYZ&cZ!^!CW!^!_!Ar!_#O!CW#O#P!DR#P#Q!=y#Q#o!CW#o#p!Ar#p;'S!CW;'S;=`!Ds<%lO!CW<z!DWX$i&jOY!CWYZ&cZ!^!CW!^!_!Ar!_#o!CW#o#p!Ar#p;'S!CW;'S;=`!Ds<%lO!CW<z!DvP;=`<%l!CW<z!EOX$i&jOY!=yYZ&cZ!^!=y!^!_!@c!_#o!=y#o#p!@c#p;'S!=y;'S;=`!Ek<%lO!=y<z!EnP;=`<%l!=y>^!Ezl$i&j(Z!b!X7`OY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#W&}#W#X!Eq#X#Z&}#Z#[!Eq#[#]&}#]#^!Eq#^#a&}#a#b!Eq#b#g&}#g#h!Eq#h#i&}#i#j!Eq#j#k!Eq#k#m&}#m#n!Eq#n#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}8r!GyZ(Z!b!X7`OY!GrZw!Grwx!@cx!P!Gr!P!Q!Hl!Q!}!Gr!}#O!JU#O#P!Bq#P;'S!Gr;'S;=`!J|<%lO!Gr8r!Hse(Z!b!X7`OY'}Zw'}x#O'}#P#W'}#W#X!Hl#X#Z'}#Z#[!Hl#[#]'}#]#^!Hl#^#a'}#a#b!Hl#b#g'}#g#h!Hl#h#i'}#i#j!Hl#j#k!Hl#k#m'}#m#n!Hl#n;'S'};'S;=`(f<%lO'}8r!JZX(Z!bOY!JUZw!JUwx!Arx#O!JU#O#P!B[#P#Q!Gr#Q;'S!JU;'S;=`!Jv<%lO!JU8r!JyP;=`<%l!JU8r!KPP;=`<%l!Gr>^!KZ^$i&j(Z!bOY!KSYZ&cZw!KSwx!CWx!^!KS!^!_!JU!_#O!KS#O#P!DR#P#Q!<n#Q#o!KS#o#p!JU#p;'S!KS;'S;=`!LV<%lO!KS>^!LYP;=`<%l!KS>^!L`P;=`<%l!<n=l!Ll`$i&j(Wp!X7`OY!LcYZ&cZr!Lcrs!=ys!P!Lc!P!Q!Mn!Q!^!Lc!^!_# o!_!}!Lc!}#O#%P#O#P!Dy#P#o!Lc#o#p# o#p;'S!Lc;'S;=`#&Y<%lO!Lc=l!Mwl$i&j(Wp!X7`OY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#W(r#W#X!Mn#X#Z(r#Z#[!Mn#[#](r#]#^!Mn#^#a(r#a#b!Mn#b#g(r#g#h!Mn#h#i(r#i#j!Mn#j#k!Mn#k#m(r#m#n!Mn#n#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(r8Q# vZ(Wp!X7`OY# oZr# ors!@cs!P# o!P!Q#!i!Q!}# o!}#O#$R#O#P!Bq#P;'S# o;'S;=`#$y<%lO# o8Q#!pe(Wp!X7`OY)rZr)rs#O)r#P#W)r#W#X#!i#X#Z)r#Z#[#!i#[#])r#]#^#!i#^#a)r#a#b#!i#b#g)r#g#h#!i#h#i)r#i#j#!i#j#k#!i#k#m)r#m#n#!i#n;'S)r;'S;=`*Z<%lO)r8Q#$WX(WpOY#$RZr#$Rrs!Ars#O#$R#O#P!B[#P#Q# o#Q;'S#$R;'S;=`#$s<%lO#$R8Q#$vP;=`<%l#$R8Q#$|P;=`<%l# o=l#%W^$i&j(WpOY#%PYZ&cZr#%Prs!CWs!^#%P!^!_#$R!_#O#%P#O#P!DR#P#Q!Lc#Q#o#%P#o#p#$R#p;'S#%P;'S;=`#&S<%lO#%P=l#&VP;=`<%l#%P=l#&]P;=`<%l!Lc?O#&kn$i&j(Wp(Z!b!X7`OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#W%Z#W#X#&`#X#Z%Z#Z#[#&`#[#]%Z#]#^#&`#^#a%Z#a#b#&`#b#g%Z#g#h#&`#h#i%Z#i#j#&`#j#k#&`#k#m%Z#m#n#&`#n#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z9d#(r](Wp(Z!b!X7`OY#(iZr#(irs!Grsw#(iwx# ox!P#(i!P!Q#)k!Q!}#(i!}#O#+`#O#P!Bq#P;'S#(i;'S;=`#,`<%lO#(i9d#)th(Wp(Z!b!X7`OY*gZr*grs'}sw*gwx)rx#O*g#P#W*g#W#X#)k#X#Z*g#Z#[#)k#[#]*g#]#^#)k#^#a*g#a#b#)k#b#g*g#g#h#)k#h#i*g#i#j#)k#j#k#)k#k#m*g#m#n#)k#n;'S*g;'S;=`+Z<%lO*g9d#+gZ(Wp(Z!bOY#+`Zr#+`rs!JUsw#+`wx#$Rx#O#+`#O#P!B[#P#Q#(i#Q;'S#+`;'S;=`#,Y<%lO#+`9d#,]P;=`<%l#+`9d#,cP;=`<%l#(i?O#,o`$i&j(Wp(Z!bOY#,fYZ&cZr#,frs!KSsw#,fwx#%Px!^#,f!^!_#+`!_#O#,f#O#P!DR#P#Q!;Z#Q#o#,f#o#p#+`#p;'S#,f;'S;=`#-q<%lO#,f?O#-tP;=`<%l#,f?O#-zP;=`<%l!;Z07[#.[b$i&j(Wp(Z!b(O0/l!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z07[#/o_$i&j(Wp(Z!bT0/lOY#/dYZ&cZr#/drs#0nsw#/dwx#4Ox!^#/d!^!_#5}!_#O#/d#O#P#1p#P#o#/d#o#p#5}#p;'S#/d;'S;=`#6|<%lO#/d06j#0w]$i&j(Z!bT0/lOY#0nYZ&cZw#0nwx#1px!^#0n!^!_#3R!_#O#0n#O#P#1p#P#o#0n#o#p#3R#p;'S#0n;'S;=`#3x<%lO#0n05W#1wX$i&jT0/lOY#1pYZ&cZ!^#1p!^!_#2d!_#o#1p#o#p#2d#p;'S#1p;'S;=`#2{<%lO#1p0/l#2iST0/lOY#2dZ;'S#2d;'S;=`#2u<%lO#2d0/l#2xP;=`<%l#2d05W#3OP;=`<%l#1p01O#3YW(Z!bT0/lOY#3RZw#3Rwx#2dx#O#3R#O#P#2d#P;'S#3R;'S;=`#3r<%lO#3R01O#3uP;=`<%l#3R06j#3{P;=`<%l#0n05x#4X]$i&j(WpT0/lOY#4OYZ&cZr#4Ors#1ps!^#4O!^!_#5Q!_#O#4O#O#P#1p#P#o#4O#o#p#5Q#p;'S#4O;'S;=`#5w<%lO#4O00^#5XW(WpT0/lOY#5QZr#5Qrs#2ds#O#5Q#O#P#2d#P;'S#5Q;'S;=`#5q<%lO#5Q00^#5tP;=`<%l#5Q05x#5zP;=`<%l#4O01p#6WY(Wp(Z!bT0/lOY#5}Zr#5}rs#3Rsw#5}wx#5Qx#O#5}#O#P#2d#P;'S#5};'S;=`#6v<%lO#5}01p#6yP;=`<%l#5}07[#7PP;=`<%l#/d)3h#7ab$i&j$Q(Ch(Wp(Z!b!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;ZAt#8vb$Z#t$i&j(Wp(Z!b!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z'Ad#:Zp$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#<_![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#<_#S#U%Z#U#V#?i#V#X%Z#X#Y!4|#Y#b%Z#b#c#>_#c#d#Bq#d#l%Z#l#m#Es#m#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#<jk$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#<_![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#<_#S#X%Z#X#Y!4|#Y#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#>j_$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#?rd$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#AQ!R!S#AQ!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#AQ#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#A]f$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#AQ!R!S#AQ!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#AQ#S#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Bzc$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#DV!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#DV#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Dbe$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#DV!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#DV#S#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#E|g$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#Ge![!^%Z!^!_*g!_!c%Z!c!i#Ge!i#O%Z#O#P&c#P#R%Z#R#S#Ge#S#T%Z#T#Z#Ge#Z#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Gpi$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#Ge![!^%Z!^!_*g!_!c%Z!c!i#Ge!i#O%Z#O#P&c#P#R%Z#R#S#Ge#S#T%Z#T#Z#Ge#Z#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z*)x#Il_!g$b$i&j$O)Lv(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z)[#Jv_al$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z04f#LS^h#)`#R-<U(Wp(Z!b$n7`OY*gZr*grs'}sw*gwx)rx!P*g!P!Q#MO!Q!^*g!^!_#Mt!_!`$ f!`#O*g#P;'S*g;'S;=`+Z<%lO*g(n#MXX$k&j(Wp(Z!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g(El#M}Z#r(Ch(Wp(Z!bOY*gZr*grs'}sw*gwx)rx!_*g!_!`#Np!`#O*g#P;'S*g;'S;=`+Z<%lO*g(El#NyX$Q(Ch(Wp(Z!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g(El$ oX#s(Ch(Wp(Z!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g*)x$!ga#`*!Y$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`!a$#l!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(K[$#w_#k(Cl$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z*)x$%Vag!*r#s(Ch$f#|$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`$&[!`!a$'f!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$&g_#s(Ch$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$'qa#r(Ch$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`!a$(v!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$)R`#r(Ch$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(Kd$*`a(r(Ct$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!a%Z!a!b$+e!b#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$+p`$i&j#{(Ch(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#`$,}_!|$Ip$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z04f$.X_!S0,v$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(n$/]Z$i&jO!^$0O!^!_$0f!_#i$0O#i#j$0k#j#l$0O#l#m$2^#m#o$0O#o#p$0f#p;'S$0O;'S;=`$4i<%lO$0O(n$0VT_#S$i&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c#S$0kO_#S(n$0p[$i&jO!Q&c!Q![$1f![!^&c!_!c&c!c!i$1f!i#T&c#T#Z$1f#Z#o&c#o#p$3|#p;'S&c;'S;=`&w<%lO&c(n$1kZ$i&jO!Q&c!Q![$2^![!^&c!_!c&c!c!i$2^!i#T&c#T#Z$2^#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$2cZ$i&jO!Q&c!Q![$3U![!^&c!_!c&c!c!i$3U!i#T&c#T#Z$3U#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$3ZZ$i&jO!Q&c!Q![$0O![!^&c!_!c&c!c!i$0O!i#T&c#T#Z$0O#Z#o&c#p;'S&c;'S;=`&w<%lO&c#S$4PR!Q![$4Y!c!i$4Y#T#Z$4Y#S$4]S!Q![$4Y!c!i$4Y#T#Z$4Y#q#r$0f(n$4lP;=`<%l$0O#1[$4z_!Y#)l$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$6U`#x(Ch$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z+;p$7c_$i&j(Wp(Z!b(a+4QOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[$8qk$i&j(Wp(Z!b(T,2j$_#t(e$I[OY%ZYZ&cZr%Zrs&}st%Ztu$8buw%Zwx(rx}%Z}!O$:f!O!Q%Z!Q![$8b![!^%Z!^!_*g!_!c%Z!c!}$8b!}#O%Z#O#P&c#P#R%Z#R#S$8b#S#T%Z#T#o$8b#o#p*g#p$g%Z$g;'S$8b;'S;=`$<l<%lO$8b+d$:qk$i&j(Wp(Z!b$_#tOY%ZYZ&cZr%Zrs&}st%Ztu$:fuw%Zwx(rx}%Z}!O$:f!O!Q%Z!Q![$:f![!^%Z!^!_*g!_!c%Z!c!}$:f!}#O%Z#O#P&c#P#R%Z#R#S$:f#S#T%Z#T#o$:f#o#p*g#p$g%Z$g;'S$:f;'S;=`$<f<%lO$:f+d$<iP;=`<%l$:f07[$<oP;=`<%l$8b#Jf$<{X!_#Hb(Wp(Z!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g,#x$=sa(y+JY$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p#q$+e#q;'S%Z;'S;=`+a<%lO%Z)>v$?V_!^(CdvBr$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z?O$@a_!q7`$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[$Aq|$i&j(Wp(Z!b'|0/l$]#t(T,2j(e$I[OX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$f%Z$f$g+g$g#BYEr#BY#BZ$A`#BZ$ISEr$IS$I_$A`$I_$JTEr$JT$JU$A`$JU$KVEr$KV$KW$A`$KW&FUEr&FU&FV$A`&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$A`?HUOEr07[$D|k$i&j(Wp(Z!b'}0/l$]#t(T,2j(e$I[OY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr",tokenizers:[Pd,Td,Zd,Ad,2,3,4,5,6,7,8,9,10,11,12,13,14,$d,new nu("$S~RRtu[#O#Pg#S#T#|~_P#o#pb~gOx~~jVO#i!P#i#j!U#j#l!P#l#m!q#m;'S!P;'S;=`#v<%lO!P~!UO!U~~!XS!Q![!e!c!i!e#T#Z!e#o#p#Z~!hR!Q![!q!c!i!q#T#Z!q~!tR!Q![!}!c!i!}#T#Z!}~#QR!Q![!P!c!i!P#T#Z!P~#^R!Q![#g!c!i#g#T#Z#g~#jS!Q![#g!c!i#g#T#Z#g#q#r!P~#yP;=`<%l!P~$RO(c~~",141,340),new nu("j~RQYZXz{^~^O(Q~~aP!P!Qd~iO(R~~",25,323)],topRules:{Script:[0,7],SingleExpression:[1,276],SingleClassItem:[2,277]},dialects:{jsx:0,ts:15175},dynamicPrecedences:{80:1,82:1,94:1,169:1,199:1},specialized:[{term:327,get:t=>Md[t]||-1},{term:343,get:t=>Rd[t]||-1},{term:95,get:t=>_d[t]||-1}],tokenPrec:15201}),Yd=[tc("function ${name}(${params}) {\n\t${}\n}",{label:"function",detail:"definition",type:"keyword"}),tc("for (let ${index} = 0; ${index} < ${bound}; ${index}++) {\n\t${}\n}",{label:"for",detail:"loop",type:"keyword"}),tc("for (let ${name} of ${collection}) {\n\t${}\n}",{label:"for",detail:"of loop",type:"keyword"}),tc("do {\n\t${}\n} while (${})",{label:"do",detail:"loop",type:"keyword"}),tc("while (${}) {\n\t${}\n}",{label:"while",detail:"loop",type:"keyword"}),tc("try {\n\t${}\n} catch (${error}) {\n\t${}\n}",{label:"try",detail:"/ catch block",type:"keyword"}),tc("if (${}) {\n\t${}\n}",{label:"if",detail:"block",type:"keyword"}),tc("if (${}) {\n\t${}\n} else {\n\t${}\n}",{label:"if",detail:"/ else block",type:"keyword"}),tc("class ${name} {\n\tconstructor(${params}) {\n\t\t${}\n\t}\n}",{label:"class",detail:"definition",type:"keyword"}),tc('import {${names}} from "${module}"\n${}',{label:"import",detail:"named",type:"keyword"}),tc('import ${name} from "${module}"\n${}',{label:"import",detail:"default",type:"keyword"})],Ed=Yd.concat([tc("interface ${name} {\n\t${}\n}",{label:"interface",detail:"definition",type:"keyword"}),tc("type ${name} = ${type}",{label:"type",detail:"definition",type:"keyword"}),tc("enum ${name} {\n\t${}\n}",{label:"enum",detail:"definition",type:"keyword"})]),Vd=new lo,qd=new Set(["Script","Block","FunctionExpression","FunctionDeclaration","ArrowFunction","MethodDeclaration","ForStatement"]);function Ld(t){return(e,i)=>{let n=e.node.getChild("VariableDefinition");return n&&i(n,t),!0}}const Dd=["FunctionDeclaration"],Wd={FunctionDeclaration:Ld("function"),ClassDeclaration:Ld("class"),ClassExpression:()=>!0,EnumDeclaration:Ld("constant"),TypeAliasDeclaration:Ld("type"),NamespaceDeclaration:Ld("namespace"),VariableDefinition(t,e){t.matchContext(Dd)||e(t,"variable")},TypeDefinition(t,e){e(t,"type")},__proto__:null};function jd(t,e){let i=Vd.get(e);if(i)return i;let n=[],r=!0;function s(e,i){let r=t.sliceString(e.from,e.to);n.push({label:r,type:i})}return e.cursor(Ws.IncludeAnonymous).iterate(e=>{if(r)r=!1;else if(e.name){let t=Wd[e.name];if(t&&t(e,s)||qd.has(e.name))return!1}else if(e.to-e.from>8192){for(let i of jd(t,e.node))n.push(i);return!1}}),Vd.set(e,n),n}const Bd=/^[\w$\xa1-\uffff][\w$\d\xa1-\uffff]*$/,Id=["TemplateString","String","RegExp","LineComment","BlockComment","VariableDefinition","TypeDefinition","Label","PropertyDefinition","PropertyName","PrivatePropertyDefinition","PrivatePropertyName","JSXText","JSXAttributeValue","JSXOpenTag","JSXCloseTag","JSXSelfClosingTag",".","?."];function Gd(t){let e=oa(t.state).resolveInner(t.pos,-1);if(Id.indexOf(e.name)>-1)return null;let i="VariableName"==e.name||e.to-e.from<20&&Bd.test(t.state.sliceDoc(e.from,e.to));if(!i&&!t.explicit)return null;let n=[];for(let i=e;i;i=i.parent)qd.has(i.name)&&(n=n.concat(jd(t.state.doc,i)));return{options:n,from:i?e.from:t.pos,validFor:Bd}}const Nd=sa.define({name:"javascript",parser:zd.configure({props:[ka.add({IfStatement:Ca({except:/^\s*({|else\b)/}),TryStatement:Ca({except:/^\s*({|catch\b|finally\b)/}),LabeledStatement:t=>t.baseIndent,SwitchBody:t=>{let e=t.textAfter,i=/^\s*\}/.test(e),n=/^\s*(case|default)\b/.test(e);return t.baseIndent+(i?0:n?1:2)*t.unit},Block:Xa({closing:"}"}),ArrowFunction:t=>t.baseIndent+t.unit,"TemplateString BlockComment":()=>null,"Statement Property":Ca({except:/^\s*{/}),JSXElement(t){let e=/^\s*<\//.test(t.textAfter);return t.lineIndent(t.node.from)+(e?0:t.unit)},JSXEscape(t){let e=/\s*\}/.test(t.textAfter);return t.lineIndent(t.node.from)+(e?0:t.unit)},"JSXOpenTag JSXSelfClosingTag":t=>t.column(t.node.from)+t.unit}),Ra.add({"Block ClassBody SwitchBody EnumBody ObjectExpression ArrayExpression ObjectType":_a,BlockComment:t=>({from:t.from+2,to:t.to-2})})]}),languageData:{closeBrackets:{brackets:["(","[","{","'",'"',"`"]},commentTokens:{line:"//",block:{open:"/*",close:"*/"}},indentOnInput:/^\s*(?:case |default:|\{|\}|<\/)$/,wordChars:"$"}}),Ud={test:t=>/^JSX/.test(t.name),facet:ea({commentTokens:{block:{open:"{/*",close:"*/}"}}})},Hd=Nd.configure({dialect:"ts"},"typescript"),Fd=Nd.configure({dialect:"jsx",props:[ia.add(t=>t.isTop?[Ud]:void 0)]}),Kd=Nd.configure({dialect:"jsx ts",props:[ia.add(t=>t.isTop?[Ud]:void 0)]},"typescript");let Jd=t=>({label:t,type:"keyword"});const tf="break case const continue default delete export extends false finally in instanceof let new return static super switch this throw true typeof var yield".split(" ").map(Jd),ef=tf.concat(["declare","implements","private","protected","public"].map(Jd));function nf(t={}){let e=t.jsx?t.typescript?Kd:Fd:t.typescript?Hd:Nd,i=t.typescript?Ed.concat(ef):Yd.concat(tf);return new ga(e,[Nd.data.of({autocomplete:(n=Id,r=zh(i),t=>{for(let e=oa(t.state).resolveInner(t.pos,-1);e;e=e.parent){if(n.indexOf(e.name)>-1)return null;if(e.type.isTop)break}return r(t)})}),Nd.data.of({autocomplete:Gd}),t.jsx?of:[]]);var n,r}function rf(t,e,i=t.length){for(let n=null==e?void 0:e.firstChild;n;n=n.nextSibling)if("JSXIdentifier"==n.name||"JSXBuiltin"==n.name||"JSXNamespacedName"==n.name||"JSXMemberExpression"==n.name)return t.sliceString(n.from,Math.min(n.to,i));return""}const sf="object"==typeof navigator&&/Android\b/.test(navigator.userAgent),of=Fr.inputHandler.of((t,e,i,n,r)=>{if((sf?t.composing:t.compositionStarted)||t.state.readOnly||e!=i||">"!=n&&"/"!=n||!Nd.isActiveAt(t.state,e,-1))return!1;let s=r(),{state:o}=s,a=o.changeByRange(t=>{var e;let i,{head:r}=t,s=oa(o).resolveInner(r-1,-1);if("JSXStartTag"==s.name&&(s=s.parent),o.doc.sliceString(r-1,r)!=n||"JSXAttributeValue"==s.name&&s.to>r);else{if(">"==n&&"JSXFragmentTag"==s.name)return{range:t,changes:{from:r,insert:"</>"}};if("/"==n&&"JSXStartCloseTag"==s.name){let t=s.parent,n=t.parent;if(n&&t.from==r-2&&((i=rf(o.doc,n.firstChild,r))||"JSXFragmentTag"==(null===(e=n.firstChild)||void 0===e?void 0:e.name))){let t=`${i}>`;return{range:_.cursor(r+t.length,-1),changes:{from:r,insert:t}}}}else if(">"==n){let e=function(t){for(;;){if("JSXOpenTag"==t.name||"JSXSelfClosingTag"==t.name||"JSXFragmentTag"==t.name)return t;if("JSXEscape"==t.name||!t.parent)return null;t=t.parent}}(s);if(e&&"JSXOpenTag"==e.name&&!/^\/?>|^<\//.test(o.doc.sliceString(r,r+2))&&(i=rf(o.doc,e,r)))return{range:t,changes:{from:r,insert:`</${i}>`}}}}return{range:t}});return!a.changes.empty&&(t.dispatch([s,o.update(a,{userEvent:"input.complete",scrollIntoView:!0})]),!0)}),af=["_blank","_self","_top","_parent"],lf=["ascii","utf-8","utf-16","latin1","latin1"],hf=["get","post","put","delete"],cf=["application/x-www-form-urlencoded","multipart/form-data","text/plain"],Of=["true","false"],uf={},df={a:{attrs:{href:null,ping:null,type:null,media:null,target:af,hreflang:null}},abbr:uf,address:uf,area:{attrs:{alt:null,coords:null,href:null,target:null,ping:null,media:null,hreflang:null,type:null,shape:["default","rect","circle","poly"]}},article:uf,aside:uf,audio:{attrs:{src:null,mediagroup:null,crossorigin:["anonymous","use-credentials"],preload:["none","metadata","auto"],autoplay:["autoplay"],loop:["loop"],controls:["controls"]}},b:uf,base:{attrs:{href:null,target:af}},bdi:uf,bdo:uf,blockquote:{attrs:{cite:null}},body:uf,br:uf,button:{attrs:{form:null,formaction:null,name:null,value:null,autofocus:["autofocus"],disabled:["autofocus"],formenctype:cf,formmethod:hf,formnovalidate:["novalidate"],formtarget:af,type:["submit","reset","button"]}},canvas:{attrs:{width:null,height:null}},caption:uf,center:uf,cite:uf,code:uf,col:{attrs:{span:null}},colgroup:{attrs:{span:null}},command:{attrs:{type:["command","checkbox","radio"],label:null,icon:null,radiogroup:null,command:null,title:null,disabled:["disabled"],checked:["checked"]}},data:{attrs:{value:null}},datagrid:{attrs:{disabled:["disabled"],multiple:["multiple"]}},datalist:{attrs:{data:null}},dd:uf,del:{attrs:{cite:null,datetime:null}},details:{attrs:{open:["open"]}},dfn:uf,div:uf,dl:uf,dt:uf,em:uf,embed:{attrs:{src:null,type:null,width:null,height:null}},eventsource:{attrs:{src:null}},fieldset:{attrs:{disabled:["disabled"],form:null,name:null}},figcaption:uf,figure:uf,footer:uf,form:{attrs:{action:null,name:null,"accept-charset":lf,autocomplete:["on","off"],enctype:cf,method:hf,novalidate:["novalidate"],target:af}},h1:uf,h2:uf,h3:uf,h4:uf,h5:uf,h6:uf,head:{children:["title","base","link","style","meta","script","noscript","command"]},header:uf,hgroup:uf,hr:uf,html:{attrs:{manifest:null}},i:uf,iframe:{attrs:{src:null,srcdoc:null,name:null,width:null,height:null,sandbox:["allow-top-navigation","allow-same-origin","allow-forms","allow-scripts"],seamless:["seamless"]}},img:{attrs:{alt:null,src:null,ismap:null,usemap:null,width:null,height:null,crossorigin:["anonymous","use-credentials"]}},input:{attrs:{alt:null,dirname:null,form:null,formaction:null,height:null,list:null,max:null,maxlength:null,min:null,name:null,pattern:null,placeholder:null,size:null,src:null,step:null,value:null,width:null,accept:["audio/*","video/*","image/*"],autocomplete:["on","off"],autofocus:["autofocus"],checked:["checked"],disabled:["disabled"],formenctype:cf,formmethod:hf,formnovalidate:["novalidate"],formtarget:af,multiple:["multiple"],readonly:["readonly"],required:["required"],type:["hidden","text","search","tel","url","email","password","datetime","date","month","week","time","datetime-local","number","range","color","checkbox","radio","file","submit","image","reset","button"]}},ins:{attrs:{cite:null,datetime:null}},kbd:uf,keygen:{attrs:{challenge:null,form:null,name:null,autofocus:["autofocus"],disabled:["disabled"],keytype:["RSA"]}},label:{attrs:{for:null,form:null}},legend:uf,li:{attrs:{value:null}},link:{attrs:{href:null,type:null,hreflang:null,media:null,sizes:["all","16x16","16x16 32x32","16x16 32x32 64x64"]}},map:{attrs:{name:null}},mark:uf,menu:{attrs:{label:null,type:["list","context","toolbar"]}},meta:{attrs:{content:null,charset:lf,name:["viewport","application-name","author","description","generator","keywords"],"http-equiv":["content-language","content-type","default-style","refresh"]}},meter:{attrs:{value:null,min:null,low:null,high:null,max:null,optimum:null}},nav:uf,noscript:uf,object:{attrs:{data:null,type:null,name:null,usemap:null,form:null,width:null,height:null,typemustmatch:["typemustmatch"]}},ol:{attrs:{reversed:["reversed"],start:null,type:["1","a","A","i","I"]},children:["li","script","template","ul","ol"]},optgroup:{attrs:{disabled:["disabled"],label:null}},option:{attrs:{disabled:["disabled"],label:null,selected:["selected"],value:null}},output:{attrs:{for:null,form:null,name:null}},p:uf,param:{attrs:{name:null,value:null}},pre:uf,progress:{attrs:{value:null,max:null}},q:{attrs:{cite:null}},rp:uf,rt:uf,ruby:uf,samp:uf,script:{attrs:{type:["text/javascript"],src:null,async:["async"],defer:["defer"],charset:lf}},section:uf,select:{attrs:{form:null,name:null,size:null,autofocus:["autofocus"],disabled:["disabled"],multiple:["multiple"]}},slot:{attrs:{name:null}},small:uf,source:{attrs:{src:null,type:null,media:null}},span:uf,strong:uf,style:{attrs:{type:["text/css"],media:null,scoped:null}},sub:uf,summary:uf,sup:uf,table:uf,tbody:uf,td:{attrs:{colspan:null,rowspan:null,headers:null}},template:uf,textarea:{attrs:{dirname:null,form:null,maxlength:null,name:null,placeholder:null,rows:null,cols:null,autofocus:["autofocus"],disabled:["disabled"],readonly:["readonly"],required:["required"],wrap:["soft","hard"]}},tfoot:uf,th:{attrs:{colspan:null,rowspan:null,headers:null,scope:["row","col","rowgroup","colgroup"]}},thead:uf,time:{attrs:{datetime:null}},title:uf,tr:uf,track:{attrs:{src:null,label:null,default:null,kind:["subtitles","captions","descriptions","chapters","metadata"],srclang:null}},ul:{children:["li","script","template","ul","ol"]},var:uf,video:{attrs:{src:null,poster:null,width:null,height:null,crossorigin:["anonymous","use-credentials"],preload:["auto","metadata","none"],autoplay:["autoplay"],mediagroup:["movie"],muted:["muted"],controls:["controls"]}},wbr:uf},ff={accesskey:null,class:null,contenteditable:Of,contextmenu:null,dir:["ltr","rtl","auto"],draggable:["true","false","auto"],dropzone:["copy","move","link","string:","file:"],hidden:["hidden"],id:null,inert:["inert"],itemid:null,itemprop:null,itemref:null,itemscope:["itemscope"],itemtype:null,lang:["ar","bn","de","en-GB","en-US","es","fr","hi","id","ja","pa","pt","ru","tr","zh"],spellcheck:Of,autocorrect:Of,autocapitalize:Of,style:null,tabindex:null,title:null,translate:["yes","no"],rel:["stylesheet","alternate","author","bookmark","help","license","next","nofollow","noreferrer","prefetch","prev","search","tag"],role:"alert application article banner button cell checkbox complementary contentinfo dialog document feed figure form grid gridcell heading img list listbox listitem main navigation region row rowgroup search switch tab table tabpanel textbox timer".split(" "),"aria-activedescendant":null,"aria-atomic":Of,"aria-autocomplete":["inline","list","both","none"],"aria-busy":Of,"aria-checked":["true","false","mixed","undefined"],"aria-controls":null,"aria-describedby":null,"aria-disabled":Of,"aria-dropeffect":null,"aria-expanded":["true","false","undefined"],"aria-flowto":null,"aria-grabbed":["true","false","undefined"],"aria-haspopup":Of,"aria-hidden":Of,"aria-invalid":["true","false","grammar","spelling"],"aria-label":null,"aria-labelledby":null,"aria-level":null,"aria-live":["off","polite","assertive"],"aria-multiline":Of,"aria-multiselectable":Of,"aria-owns":null,"aria-posinset":null,"aria-pressed":["true","false","mixed","undefined"],"aria-readonly":Of,"aria-relevant":null,"aria-required":Of,"aria-selected":["true","false","undefined"],"aria-setsize":null,"aria-sort":["ascending","descending","none","other"],"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null,"aria-valuetext":null},pf="beforeunload copy cut dragstart dragover dragleave dragenter dragend drag paste focus blur change click load mousedown mouseenter mouseleave mouseup keydown keyup resize scroll unload".split(" ").map(t=>"on"+t);for(let t of pf)ff[t]=null;class gf{constructor(t,e){this.tags={...df,...t},this.globalAttrs={...ff,...e},this.allTags=Object.keys(this.tags),this.globalAttrNames=Object.keys(this.globalAttrs)}}function mf(t,e,i=t.length){if(!e)return"";let n=e.firstChild,r=n&&n.getChild("TagName");return r?t.sliceString(r.from,Math.min(r.to,i)):""}function Qf(t,e=!1){for(;t;t=t.parent)if("Element"==t.name){if(!e)return t;e=!1}return null}function Sf(t,e,i){let n=i.tags[mf(t,Qf(e))];return(null==n?void 0:n.children)||i.allTags}function xf(t,e){let i=[];for(let n=Qf(e);n&&!n.type.isTop;n=Qf(n.parent)){let r=mf(t,n);if(r&&"CloseTag"==n.lastChild.name)break;r&&i.indexOf(r)<0&&("EndTag"==e.name||e.from>=n.firstChild.to)&&i.push(r)}return i}gf.default=new gf;const yf=/^[:\-\.\w\u00b7-\uffff]*$/;function wf(t,e,i,n,r){let s=/\s*>/.test(t.sliceDoc(r,r+5))?"":">",o=Qf(i,!0);return{from:n,to:r,options:Sf(t.doc,o,e).map(t=>({label:t,type:"type"})).concat(xf(t.doc,i).map((t,e)=>({label:"/"+t,apply:"/"+t+s,type:"type",boost:99-e}))),validFor:/^\/?[:\-\.\w\u00b7-\uffff]*$/}}function bf(t,e,i,n){let r=/\s*>/.test(t.sliceDoc(n,n+5))?"":">";return{from:i,to:n,options:xf(t.doc,e).map((t,e)=>({label:t,apply:t+r,type:"type",boost:99-e})),validFor:yf}}function kf(t,e){let{state:i,pos:n}=e,r=oa(i).resolveInner(n,-1),s=r.resolve(n);for(let t,e=n;s==r&&(t=r.childBefore(e));){let i=t.lastChild;if(!i||!i.type.isError||i.from<i.to)break;s=r=t,e=i.from}return"TagName"==r.name?r.parent&&/CloseTag$/.test(r.parent.name)?bf(i,r,r.from,n):wf(i,t,r,r.from,n):"StartTag"==r.name?wf(i,t,r,n,n):"StartCloseTag"==r.name||"IncompleteCloseTag"==r.name?bf(i,r,n,n):"OpenTag"==r.name||"SelfClosingTag"==r.name||"AttributeName"==r.name?function(t,e,i,n,r){let s=Qf(i),o=s?e.tags[mf(t.doc,s)]:null,a=o&&o.attrs?Object.keys(o.attrs):[];return{from:n,to:r,options:(o&&!1===o.globalAttrs?a:a.length?a.concat(e.globalAttrNames):e.globalAttrNames).map(t=>({label:t,type:"property"})),validFor:yf}}(i,t,r,"AttributeName"==r.name?r.from:n,n):"Is"==r.name||"AttributeValue"==r.name||"UnquotedAttributeValue"==r.name?function(t,e,i,n,r){var s;let o,a=null===(s=i.parent)||void 0===s?void 0:s.getChild("AttributeName"),l=[];if(a){let s=t.sliceDoc(a.from,a.to),h=e.globalAttrs[s];if(!h){let n=Qf(i),r=n?e.tags[mf(t.doc,n)]:null;h=(null==r?void 0:r.attrs)&&r.attrs[s]}if(h){let e=t.sliceDoc(n,r).toLowerCase(),i='"',s='"';/^['"]/.test(e)?(o='"'==e[0]?/^[^"]*$/:/^[^']*$/,i="",s=t.sliceDoc(r,r+1)==e[0]?"":e[0],e=e.slice(1),n++):o=/^[^\s<>='"]*$/;for(let t of h)l.push({label:t,apply:i+t+s,type:"constant"})}}return{from:n,to:r,options:l,validFor:o}}(i,t,r,"Is"==r.name?n:r.from,n):!e.explicit||"Element"!=s.name&&"Text"!=s.name&&"Document"!=s.name?null:function(t,e,i,n){let r=[],s=0;for(let n of Sf(t.doc,i,e))r.push({label:"<"+n,type:"type"});for(let e of xf(t.doc,i))r.push({label:"</"+e+">",type:"type",boost:99-s++});return{from:n,to:n,options:r,validFor:/^<\/?[:\-\.\w\u00b7-\uffff]*$/}}(i,t,r,n)}function vf(t){let{extraTags:e,extraGlobalAttributes:i}=t,n=i||e?new gf(e,i):gf.default;return t=>kf(n,t)}const $f=Nd.parser.configure({top:"SingleExpression"}),Pf=[{tag:"script",attrs:t=>"text/typescript"==t.type||"ts"==t.lang,parser:Hd.parser},{tag:"script",attrs:t=>"text/babel"==t.type||"text/jsx"==t.type,parser:Fd.parser},{tag:"script",attrs:t=>"text/typescript-jsx"==t.type,parser:Kd.parser},{tag:"script",attrs:t=>/^(importmap|speculationrules|application\/(.+\+)?json)$/i.test(t.type),parser:$f},{tag:"script",attrs:t=>!t.type||/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i.test(t.type),parser:Nd.parser},{tag:"style",attrs:t=>(!t.lang||"css"==t.lang)&&(!t.type||/^(text\/)?(x-)?(stylesheet|css)$/i.test(t.type)),parser:bd.parser}],Tf=[{name:"style",parser:bd.parser.configure({top:"Styles"})}].concat(pf.map(t=>({name:t,parser:Nd.parser}))),Zf=sa.define({name:"html",parser:Du.configure({props:[ka.add({Element(t){let e=/^(\s*)(<\/)?/.exec(t.textAfter);return t.node.to<=t.pos+e[0].length?t.continue():t.lineIndent(t.node.from)+(e[2]?0:t.unit)},"OpenTag CloseTag SelfClosingTag":t=>t.column(t.node.from)+t.unit,Document(t){if(t.pos+/\s*/.exec(t.textAfter)[0].length<t.node.to)return t.continue();let e,i=null;for(let e=t.node;;){let t=e.lastChild;if(!t||"Element"!=t.name||t.to!=e.to)break;i=e=t}return i&&(!(e=i.lastChild)||"CloseTag"!=e.name&&"SelfClosingTag"!=e.name)?t.lineIndent(i.from)+t.unit:null}}),Ra.add({Element(t){let e=t.firstChild,i=t.lastChild;return e&&"OpenTag"==e.name?{from:e.to,to:"CloseTag"==i.name?i.from:t.to}:null}}),Fa.add({"OpenTag CloseTag":t=>t.getChild("TagName")})]}),languageData:{commentTokens:{block:{open:"\x3c!--",close:"--\x3e"}},indentOnInput:/^\s*<\/\w+\W$/,wordChars:"-_"}}),Xf=Zf.configure({wrap:Iu(Pf,Tf)});function Af(t={}){let e,i="";!1===t.matchClosingTags&&(i="noMatch"),!0===t.selfClosingTags&&(i=(i?i+" ":"")+"selfClosing"),(t.nestedLanguages&&t.nestedLanguages.length||t.nestedAttributes&&t.nestedAttributes.length)&&(e=Iu((t.nestedLanguages||[]).concat(Pf),(t.nestedAttributes||[]).concat(Tf)));let n=e?Zf.configure({wrap:e,dialect:i}):i?Xf.configure({dialect:i}):Xf;return new ga(n,[Xf.data.of({autocomplete:vf(t)}),!1!==t.autoCloseTags?Mf:[],nf().support,new ga(bd,bd.data.of({autocomplete:wd})).support])}const Cf=new Set("area base br col command embed frame hr img input keygen link meta param source track wbr menuitem".split(" ")),Mf=Fr.inputHandler.of((t,e,i,n,r)=>{if(t.composing||t.state.readOnly||e!=i||">"!=n&&"/"!=n||!Xf.isActiveAt(t.state,e,-1))return!1;let s=r(),{state:o}=s,a=o.changeByRange(t=>{var e,i,r;let s,a=o.doc.sliceString(t.from-1,t.to)==n,{head:l}=t,h=oa(o).resolveInner(l,-1);if(a&&">"==n&&"EndTag"==h.name){let n=h.parent;if("CloseTag"!=(null===(i=null===(e=n.parent)||void 0===e?void 0:e.lastChild)||void 0===i?void 0:i.name)&&(s=mf(o.doc,n.parent,l))&&!Cf.has(s))return{range:t,changes:{from:l,to:l+(">"===o.doc.sliceString(l,l+1)?1:0),insert:`</${s}>`}}}else if(a&&"/"==n&&"IncompleteCloseTag"==h.name){let t=h.parent;if(h.from==l-2&&"CloseTag"!=(null===(r=t.lastChild)||void 0===r?void 0:r.name)&&(s=mf(o.doc,t,l))&&!Cf.has(s)){let t=l+(">"===o.doc.sliceString(l,l+1)?1:0),e=`${s}>`;return{range:_.cursor(l+e.length,-1),changes:{from:l,to:t,insert:e}}}}return{range:t}});return!a.changes.empty&&(t.dispatch([s,o.update(a,{userEvent:"input.complete",scrollIntoView:!0})]),!0)}),Rf=ea({commentTokens:{block:{open:"\x3c!--",close:"--\x3e"}}}),_f=new zs,zf=vO.configure({props:[Ra.add(t=>!t.is("Block")||t.is("Document")||null!=Yf(t)||function(t){return"OrderedList"==t.name||"BulletList"==t.name}(t)?void 0:(t,e)=>({from:e.doc.lineAt(t.from).to,to:t.to})),_f.add(Yf),ka.add({Document:()=>null}),ta.add({Document:Rf})]});function Yf(t){let e=/^(?:ATX|Setext)Heading(\d)$/.exec(t.name);return e?+e[1]:void 0}function Ef(t,e){let i=t;for(;;){let t,n=i.nextSibling;if(!n||null!=(t=Yf(n.type))&&t<=e)break;i=n}return i.to}const Vf=Ma.of((t,e,i)=>{for(let n=oa(t).resolveInner(i,-1);n&&!(n.from<e);n=n.parent){let t=n.type.prop(_f);if(null==t)continue;let e=Ef(n,t);if(e>i)return{from:i,to:e}}return null});function qf(t){return new na(Rf,t,[],"markdown")}const Lf=qf(zf),Df=qf(zf.configure([WO,IO,BO,GO,{props:[Ra.add({Table:(t,e)=>({from:e.doc.lineAt(t.from).to,to:t.to})})]}]));class Wf{constructor(t,e,i,n,r,s,o){this.node=t,this.from=e,this.to=i,this.spaceBefore=n,this.spaceAfter=r,this.type=s,this.item=o}blank(t,e=!0){let i=this.spaceBefore+("Blockquote"==this.node.name?">":"");if(null!=t){for(;i.length<t;)i+=" ";return i}for(let t=this.to-this.from-i.length-this.spaceAfter.length;t>0;t--)i+=" ";return i+(e?this.spaceAfter:"")}marker(t,e){let i="OrderedList"==this.node.name?String(+Bf(this.item,t)[2]+e):"";return this.spaceBefore+i+this.type+this.spaceAfter}}function jf(t,e){let i=[],n=[];for(let e=t;e;e=e.parent){if("FencedCode"==e.name)return n;"ListItem"!=e.name&&"Blockquote"!=e.name||i.push(e)}for(let t=i.length-1;t>=0;t--){let r,s=i[t],o=e.lineAt(s.from),a=s.from-o.from;if("Blockquote"==s.name&&(r=/^ *>( ?)/.exec(o.text.slice(a))))n.push(new Wf(s,a,a+r[0].length,"",r[1],">",null));else if("ListItem"==s.name&&"OrderedList"==s.parent.name&&(r=/^( *)\d+([.)])( *)/.exec(o.text.slice(a)))){let t=r[3],e=r[0].length;t.length>=4&&(t=t.slice(0,t.length-4),e-=4),n.push(new Wf(s.parent,a,a+e,r[1],t,r[2],s))}else if("ListItem"==s.name&&"BulletList"==s.parent.name&&(r=/^( *)([-+*])( {1,4}\[[ xX]\])?( +)/.exec(o.text.slice(a)))){let t=r[4],e=r[0].length;t.length>4&&(t=t.slice(0,t.length-4),e-=4);let i=r[2];r[3]&&(i+=r[3].replace(/[xX]/," ")),n.push(new Wf(s.parent,a,a+e,r[1],t,i,s))}}return n}function Bf(t,e){return/^(\s*)(\d+)(?=[.)])/.exec(e.sliceString(t.from,t.from+10))}function If(t,e,i,n=0){for(let r=-1,s=t;;){if("ListItem"==s.name){let t=Bf(s,e),o=+t[2];if(r>=0){if(o!=r+1)return;i.push({from:s.from+t[1].length,to:s.from+t[0].length,insert:String(r+2+n)})}r=o}let t=s.nextSibling;if(!t)break;s=t}}function Gf(t,e){let i=/^[ \t]*/.exec(t)[0].length;if(!i||"\t"!=e.facet(Sa))return t;let n="";for(let e=Vt(t,4,i);e>0;)e>=4?(n+="\t",e-=4):(n+=" ",e--);return n+t.slice(i)}function Nf(t){return"QuoteMark"==t.name||"ListMark"==t.name}function Uf(t,e,i){let n="";for(let e=0,r=t.length-2;e<=r;e++)n+=t[e].blank(e<r?Vt(i.text,4,t[e+1].from)-n.length:null,e<r);return Gf(n,e)}const Hf=[{key:"Enter",run:({state:t,dispatch:e})=>{let i=oa(t),{doc:n}=t,r=null,s=t.changeByRange(e=>{if(!e.empty||!Df.isActiveAt(t,e.from,-1)&&!Df.isActiveAt(t,e.from,1))return r={range:e};let s=e.from,o=n.lineAt(s),a=jf(i.resolveInner(s,-1),n);for(;a.length&&a[a.length-1].from>s-o.from;)a.pop();if(!a.length)return r={range:e};let l=a[a.length-1];if(l.to-l.spaceAfter.length>s-o.from)return r={range:e};let h=s>=l.to-l.spaceAfter.length&&!/\S/.test(o.text.slice(l.to));if(l.item&&h){let e=l.node.firstChild,i=l.node.getChild("ListItem","ListItem");if(e.to>=s||i&&i.to<s||o.from>0&&!/[^\s>]/.test(n.lineAt(o.from-1).text)){let t,e=a.length>1?a[a.length-2]:null,i="";e&&e.item?(t=o.from+e.from,i=e.marker(n,1)):t=o.from+(e?e.to:0);let r=[{from:t,to:s,insert:i}];return"OrderedList"==l.node.name&&If(l.item,n,r,-2),e&&"OrderedList"==e.node.name&&If(e.item,n,r),{range:_.cursor(t+i.length),changes:r}}{let e=Uf(a,t,o);return{range:_.cursor(s+e.length+1),changes:{from:o.from,insert:e+t.lineBreak}}}}if("Blockquote"==l.node.name&&h&&o.from){let i=n.lineAt(o.from-1),r=/>\s*$/.exec(i.text);if(r&&r.index==l.from){let n=t.changes([{from:i.from+r.index,to:i.to},{from:o.from+l.from,to:o.to}]);return{range:e.map(n),changes:n}}}let c=[];"OrderedList"==l.node.name&&If(l.item,n,c);let O=l.item&&l.item.from<o.from,u="";if(!O||/^[\s\d.)\-+*>]*/.exec(o.text)[0].length>=l.to)for(let t=0,e=a.length-1;t<=e;t++)u+=t!=e||O?a[t].blank(t<e?Vt(o.text,4,a[t+1].from)-u.length:null):a[t].marker(n,1);let d=s;for(;d>o.from&&/\s/.test(o.text.charAt(d-o.from-1));)d--;return u=Gf(u,t),function(t,e){if("OrderedList"!=t.name&&"BulletList"!=t.name)return!1;let i=t.firstChild,n=t.getChild("ListItem","ListItem");if(!n)return!1;let r=e.lineAt(i.to),s=e.lineAt(n.from),o=/^[\s>]*$/.test(r.text);return r.number+(o?0:1)<s.number}(l.node,t.doc)&&(u=Uf(a,t,o)+t.lineBreak+u),c.push({from:d,to:s,insert:t.lineBreak+u}),{range:_.cursor(d+u.length+1),changes:c}});return!r&&(e(t.update(s,{scrollIntoView:!0,userEvent:"input"})),!0)}},{key:"Backspace",run:({state:t,dispatch:e})=>{let i=oa(t),n=null,r=t.changeByRange(e=>{let r=e.from,{doc:s}=t;if(e.empty&&Df.isActiveAt(t,e.from)){let e=s.lineAt(r),n=jf(function(t,e){let i=t.resolveInner(e,-1),n=e;Nf(i)&&(n=i.from,i=i.parent);for(let t;t=i.childBefore(n);)if(Nf(t))n=t.from;else{if("OrderedList"!=t.name&&"BulletList"!=t.name)break;i=t.lastChild,n=i.to}return i}(i,r),s);if(n.length){let i=n[n.length-1],s=i.to-i.spaceAfter.length+(i.spaceAfter?1:0);if(r-e.from>s&&!/\S/.test(e.text.slice(s,r-e.from)))return{range:_.cursor(e.from+s),changes:{from:e.from+s,to:r}};if(r-e.from==s&&(!i.item||e.from<=i.item.from||!/\S/.test(e.text.slice(0,i.to)))){let n=e.from+i.from;if(i.item&&i.node.from<i.item.from&&/\S/.test(e.text.slice(i.from,i.to))){let r=i.blank(Vt(e.text,4,i.to)-Vt(e.text,4,i.from));return n==e.from&&(r=Gf(r,t)),{range:_.cursor(n+r.length),changes:{from:n,to:e.from+i.to,insert:r}}}if(n<r)return{range:_.cursor(n),changes:{from:n,to:r}}}}}return n={range:e}});return!n&&(e(t.update(r,{scrollIntoView:!0,userEvent:"delete"})),!0)}}],Ff=Af({matchClosingTags:!1});function Kf(t={}){let{codeLanguages:e,defaultCodeLanguage:i,addKeymap:n=!0,base:{parser:r}=Lf,completeHTMLTags:s=!0,htmlTagLanguage:o=Ff}=t;if(!(r instanceof Kc))throw new RangeError("Base parser provided to `markdown` should be a Markdown parser");let a,l=t.extensions?[t.extensions]:[],h=[o.support,Vf];i instanceof ga?(h.push(i.support),a=i.language):i&&(a=i);let c=e||a?(O=e,u=a,t=>{if(t&&O){let e=null;if(t=/\S*/.exec(t)[0],e="function"==typeof O?O(t):ma.matchLanguageName(O,t,!0),e instanceof ma)return e.support?e.support.language.parser:ha.getSkippingParser(e.load());if(e)return e.parser}return u?u.parser:null}):void 0;var O,u;l.push(function(t){let{codeParser:e,htmlParser:i}=t,n=uo((t,n)=>{let r=t.type.id;if(!e||r!=xc.CodeBlock&&r!=xc.FencedCode){if(i&&(r==xc.HTMLBlock||r==xc.HTMLTag||r==xc.CommentBlock))return{parser:i,overlay:$O(t.node,t.from,t.to)}}else{let i="";if(r==xc.FencedCode){let e=t.node.getChild(xc.CodeInfo);e&&(i=n.read(e.from,e.to))}let s=e(i);if(s)return{parser:s,overlay:t=>t.type.id==xc.CodeText}}return null});return{wrap:n}}({codeParser:c,htmlParser:o.language.parser})),n&&h.push(G.high(ss.of(Hf)));let d=qf(r.configure(l));return s&&h.push(d.data.of({autocomplete:Jf})),new ga(d,h)}function Jf(t){let{state:e,pos:i}=t,n=/<[:\-\.\w\u00b7-\uffff]*$/.exec(e.sliceDoc(i-25,i));if(!n)return null;let r=oa(e).resolveInner(i,-1);for(;r&&!r.type.isTop;){if("CodeBlock"==r.name||"FencedCode"==r.name||"ProcessingInstructionBlock"==r.name||"CommentBlock"==r.name||"Link"==r.name||"Image"==r.name)return null;r=r.parent}return{from:i-n[0].length,to:i,options:ep(),validFor:/^<[:\-\.\w\u00b7-\uffff]*$/}}let tp=null;function ep(){if(tp)return tp;let t=(e=new Rh(yt.create({extensions:Ff}),0,!0),kf(gf.default,e));var e;return tp=t?t.options:[]}const ip=window.sharedSocket;let np;console.log("Editor: Using shared socket");let rp=!1,sp=null,op=null;const ap={};let lp=0;function hp(){if(np)try{document.querySelectorAll(".remote-cursor-overlay").forEach(t=>t.remove());for(const[t,e]of Object.entries(ap))if(e&&"number"==typeof e.position)try{const i=e.position,n=np.state.doc.length;if(i<0||i>n){console.warn(`Invalid cursor position ${i} for user ${t}, skipping`);continue}const r=np.coordsAtPos(i);if(r){const i=np.dom.parentElement,n=i.getBoundingClientRect(),s=r.left-n.left,o=r.top-n.top,a=document.createElement("div");a.className="remote-cursor-overlay",a.style.position="absolute",a.style.left=s+"px",a.style.top=o+"px",a.style.width="2px",a.style.height="20px",a.style.backgroundColor=e.color,a.style.pointerEvents="none",a.style.zIndex="1000",a.setAttribute("data-user-id",t),a.setAttribute("data-username",e.username);const l=document.createElement("div");l.className="remote-cursor-label",l.textContent=e.username,l.style.position="absolute",l.style.top="-25px",l.style.left="0px",l.style.fontSize="12px",l.style.backgroundColor=e.color,l.style.color="white",l.style.padding="2px 4px",l.style.borderRadius="3px",l.style.whiteSpace="nowrap",l.style.pointerEvents="none",a.appendChild(l),"relative"!==i.style.position&&(i.style.position="relative"),i.appendChild(a)}}catch(e){console.error(`Error creating cursor overlay for user ${t}:`,e)}}catch(t){console.error("Error updating cursor overlays:",t)}}let cp=!1,Op=[],up=[],dp=null;console.log("Editor module: Initializing track changes variables"),window.editorModule={setCurrentDocumentId:t=>{console.log("Editor module: Setting current document ID to",t),sp=t},getCurrentDocumentId:()=>sp,getEditorContent:()=>np?np.state.doc.toString():"",setTrackChangesMode:t=>{console.log("Editor module: Setting track changes mode to",t),cp=t},highlightPendingChanges:t=>{console.log("Editor module: Highlighting pending changes",t.length),t.length>0&&console.log("Sample change:",JSON.stringify(t[0])),Op=t,np&&np.dispatch({})}};const fp=document.getElementById("connection-status");let pp=!1;function gp(){ip.connected&&pp?(fp.textContent="Connected",fp.className="connected"):ip.connected&&!pp?(fp.textContent="Authenticating...",fp.className="connecting"):(fp.textContent="Disconnected",fp.className="disconnected")}ip.on("connect",()=>{console.log("Socket connected"),gp()}),ip.on("disconnect",()=>{console.log("Socket disconnected"),pp=!1,gp()}),ip.on("authentication-success",()=>{console.log("Authentication successful"),pp=!0,gp()}),ip.on("authentication-failed",()=>{console.log("Authentication failed"),pp=!1,gp()}),gp();const mp=[function(t={}){return[$s.of(t),ms(),Zs]}(),Cs,Os,function(t={}){return[yl,xl.of(t),Fr.domEventHandlers({beforeinput(t,e){let i="historyUndo"==t.inputType?bl:"historyRedo"==t.inputType?kl:null;return!!i&&(t.preventDefault(),i(e))}})]}(),yt.transactionFilter.of(t=>{if(!t.docChanged||!t.isUserEvent("input.type")&&!t.isUserEvent("input.complete"))return t;let e=t.startState.languageDataAt("indentOnInput",t.startState.selection.main.head);if(!e.length)return t;let i=t.newDoc,{head:n}=t.newSelection.main,r=i.lineAt(n);if(n>r.from+200)return t;let s=i.sliceString(r.from,n);if(!e.some(t=>t.test(s)))return t;let{state:o}=t,a=-1,l=[];for(let{head:t}of o.selection.ranges){let e=o.doc.lineAt(t);if(e.from==a)continue;a=e.from;let i=wa(o,e.from);if(null==i)continue;let n=/^\s*/.exec(e.text)[0],r=ya(o,i);n!=r&&l.push({from:e.from,to:e.from+n.length,insert:r})}return l.length?[t,{changes:l,sequential:!0}]:t}),function(t){let e,i=[La];return t instanceof za&&(t.module&&i.push(Fr.styleModule.of(t.module)),e=t.themeType),e?i.push(Ya.computeN([Fr.darkTheme],i=>i.facet(Fr.darkTheme)==("dark"==e)?[t]:[])):i.push(Ya.of(t)),i}(Da),function(t={}){return[Ba.of(t),Ha]}(),[cc,sc],ss.of([...Mh,...El,...Oc])];class Qp{constructor(t){this.cursor=t,this.dom=null}eq(t){return t&&t.cursor&&this.cursor.userId===t.cursor.userId&&this.cursor.position===t.cursor.position}toDOM(){const t=document.createElement("div");t.className="remote-cursor",t.style.borderLeftWidth="2px",t.style.borderLeftStyle="solid",t.style.borderLeftColor=this.cursor.color,t.style.height="1.2em";const e=document.createElement("div");return e.className="cursor-label",e.textContent=this.cursor.username,e.style.backgroundColor=this.cursor.color,t.appendChild(e),this.dom=t,t}ignoreEvent(){return!1}get estimatedHeight(){return 0}destroy(){this.dom&&(this.dom=null)}update(){return!1}compare(){return!0}get isHidden(){return!1}}function Sp(t=""){const e=Hi.fromClass(class{decorations;constructor(t){this.decorations=ti.none,this.updateDecorations(t)}update(t){this.updateDecorations(t.view)}updateDecorations(t){try{const e=[];Object.keys(ap).forEach(i=>{try{const n=ap[i];if(!n||void 0===n.position||null===n.position||isNaN(n.position))return void console.log(`Skipping cursor for user ${i}: invalid cursor data`,n);console.log(`Creating cursor decoration for user ${i} at position ${n.position}`);const r=new Qp(n),s=Math.min(Math.max(0,n.position),t.state.doc.length);console.log(`Final cursor position for user ${i}: ${s} (doc length: ${t.state.doc.length})`);try{const t=np.state.doc.length;let r=Math.floor(s);r<0&&(r=0),r>t&&(r=t),console.log(`Creating decoration at position ${r} (doc length: ${t})`);const o=np.state.doc.lineAt(r),a=ti.line({class:"remote-cursor-line",attributes:{style:`border-left: 3px solid ${n.color}; padding-left: 5px;`,"data-user":n.username}}).range(o.from);e.push(a),console.log(`Successfully created cursor decoration for user ${i} at position ${r}`)}catch(t){console.error(`Error creating decoration range for user ${i}:`,t),console.error(`Position: ${s}, Type: ${typeof s}, Floor: ${Math.floor(s)}, Doc length: ${np.state.doc.length}`);try{const t=ti.widget({widget:r,side:1}).range(0);e.push(t),console.log(`Fallback decoration created for user ${i} at position 0`)}catch(t){console.error(`Fallback decoration also failed for user ${i}:`,t)}}}catch(t){console.error("Error creating cursor for user:",i,t),console.error("Cursor data:",ap[i])}}),this.decorations=ti.set(e)}catch(t){console.error("Error updating decorations:",t),this.decorations=ti.none}}},{decorations:t=>t.decorations}),i=Hi.fromClass(class{decorations;constructor(t){this.decorations=ti.none,this.updateDecorations(t)}update(t){this.updateDecorations(t.view)}updateDecorations(t){try{if(console.log("Updating pending change decorations, count:",Op?Op.length:0),!Op||0===Op.length)return console.log("No pending changes to highlight"),void(this.decorations=ti.none);const e=[];console.log("Document length:",t.state.doc.length);const i=[];Op.forEach(e=>{try{const n=ti.mark({class:"cm-pending-change",attributes:{title:`Change by ${e.userName}`}}),r=void 0!==e.fromPos?e.fromPos:e.from_pos,s=void 0!==e.toPos?e.toPos:e.to_pos;console.log(`Change positions - fromPos: ${r}, toPos: ${s}, text: "${e.insertedText}", id: ${e.id}`);const o=Math.min(r||0,t.state.doc.length),a=Math.min(s||0,t.state.doc.length);o<a?i.push({deco:n,from:o,to:a}):o===a&&e.insertedText&&i.push({deco:n,from:o,to:o+e.insertedText.length})}catch(t){console.error("Error creating decoration for change:",e,t)}}),i.sort((t,e)=>t.from-e.from),i.forEach(({deco:t,from:i,to:n})=>{e.push(t.range(i,n))}),this.decorations=ti.set(e)}catch(t){console.error("Error updating pending change decorations:",t),this.decorations=ti.none}}},{decorations:t=>t.decorations}),n=yt.create({doc:t,extensions:[mp,Kf(),e,i,Fr.updateListener.of(t=>{if(t.docChanged&&!rp&&sp){const e=t.state.doc.toString(),i=[];t.changes.iterChanges((t,e,n,r,s)=>{i.push({fromA:t,toA:e,fromB:n,toB:r,inserted:s.toString()})}),cp?(console.log("Adding changes to buffer:",i.length),up.push(...i),dp&&clearTimeout(dp),dp=setTimeout(()=>{(function(){if(0===up.length)return;console.log("Processing change buffer, size:",up.length);const t=function(t){if(!t||t.length<=1)return t;console.log("Merging changes, count before:",t.length);const e=[...t].sort((t,e)=>t.fromA-e.fromA),i=[];let n=e[0];for(let t=1;t<e.length;t++){const r=e[t];r.fromA-n.toA<=10&&n.userId&&r.userId&&r.userId===n.userId?n={fromA:Math.min(n.fromA,r.fromA),toA:Math.max(n.toA,r.toA),fromB:Math.min(n.fromB,r.fromB),toB:Math.max(n.toB,r.toB),inserted:n.inserted+r.inserted,userId:n.userId}:(i.push(n),n=r)}return i.push(n),console.log("Merging changes, count after:",i.length),i}(up),e=np.state.doc.toString();ip.emit("update-content",{documentId:sp,content:e,changes:t,trackChangesMode:cp}),up=[]})(),dp=null},1e3)):ip.emit("update-content",{documentId:sp,content:e,changes:i,trackChangesMode:cp})}if(t.selectionSet&&sp){const e=Date.now();if(e-lp>100){const i=t.state.selection.main.head,n=t.state.doc.lineAt(i),r=n.number-1,s=i-n.from;ip.emit("cursor-update",{documentId:sp,position:{line:r,character:s}}),lp=e}}rp=!1})]});np=new Fr({state:n,parent:document.getElementById("editor")}),np.dom.addEventListener("click",()=>{sp&&np&&setTimeout(()=>{const t=np.state.selection.main.head,e=np.state.doc.lineAt(t),i=e.number-1,n=t-e.from;ip.emit("cursor-update",{documentId:sp,position:{line:i,character:n}})},10)})}function xp(t){console.log("Updating editor content, length:",t.length),np?(rp=!0,np.dispatch({changes:{from:0,to:np.state.doc.length,insert:t}}),console.log("Editor content updated"),setTimeout(()=>{hp()},100)):console.warn("Cannot update editor content: editor not initialized")}function yp(t){console.log("updateActiveUsersList called with:",t);const e=document.getElementById("user-list");e?(e.innerHTML="",t&&Array.isArray(t)?(t.forEach(t=>{console.log("Adding user to list:",t);const i=document.createElement("li");i.className="user-item",i.innerHTML=`\n      <div class="user-avatar" style="background-color: ${t.color}">\n        ${t.displayName.charAt(0).toUpperCase()}\n      </div>\n      <span class="user-name">${t.displayName}</span>\n    `,e.appendChild(i)}),console.log(`Updated active users list: ${t.length} users`)):console.log("No users or invalid users array")):console.error("user-list element not found!")}ip.on("authentication-success",({user:t})=>{op=t}),ip.on("init-document",({content:t,documentId:e})=>{console.log("Editor received init-document event:",{documentId:e,contentLength:t.length}),sp=e,np?(console.log("Updating editor content"),xp(t)):(console.log("Initializing editor with content"),Sp(t))}),ip.on("content-updated",({content:t})=>{xp(t)}),ip.on("remote-cursor",t=>{if(t.position&&"object"==typeof t.position&&void 0!==t.position.line&&void 0!==t.position.character&&np)try{const e=np.state.doc,i=Math.max(0,Math.min(t.position.line,e.lines-1)),n=e.line(i+1),r=Math.max(0,Math.min(t.position.character,n.length)),s=n.from+r,o=Math.max(0,Math.min(s,e.length));ap[t.userId]={...t,position:o}}catch(e){console.error("Error converting cursor position:",e),ap[t.userId]={...t,position:0}}else"number"==typeof t.position?ap[t.userId]=t:ap[t.userId]={...t,position:0};np&&hp()}),ip.on("user-joined",t=>{ap[t.userId]={username:t.username,color:t.color,position:0}}),ip.on("user-left",({userId:t})=>{delete ap[t],np&&hp()}),ip.on("request-cursor-position",()=>{if(console.log("Cursor position requested by server"),np&&sp){const t=np.state.selection.main.head,e=np.state.doc.lineAt(t),i=e.number-1,n=t-e.from;console.log("Sending requested cursor position:",i,n),ip.emit("cursor-update",{documentId:sp,position:{line:i,character:n}})}}),ip.on("active-users-updated",t=>{console.log("Web client received active-users-updated:",t),console.log("Number of users:",t?t.length:"users is null/undefined"),console.log("Users array details:",JSON.stringify(t,null,2)),yp(t)}),ip.on("document-users",t=>{console.log("Web client received document-users:",t),t&&t.users&&(console.log("Document users:",JSON.stringify(t.users,null,2)),yp(t.users))}),ip.on("preview-updated",({html:t})=>{document.getElementById("preview-content").innerHTML=t}),document.getElementById("renderBtn").addEventListener("click",()=>{sp?fetch("/api/render",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({documentId:sp})}).then(t=>t.json()).then(t=>{t.html?document.getElementById("preview-content").innerHTML=t.html:t.error&&(document.getElementById("preview-content").innerHTML=`<div class="error">${t.error}</div>`)}).catch(t=>{console.error("Error rendering document:",t),document.getElementById("preview-content").innerHTML=`<div class="error">Error rendering document: ${t.message}</div>`}):alert("Please select a document first")})})();