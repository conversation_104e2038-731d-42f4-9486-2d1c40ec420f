<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Collaborative Quarto Editor</title>
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
  <div class="container">
    <header>
      <h1>Collaborative Quarto Editor</h1>
      <div class="user-info">
        <span id="current-user">Loading...</span>
        <button id="logoutBtn" class="btn-logout"><i class="fas fa-sign-out-alt"></i> Logout</button>
      </div>
    </header>

    <div class="main-content">
      <div class="sidebar">
        <div class="project-explorer-container" id="projectExplorer">
          <!-- Project explorer will be rendered here -->
        </div>

        <div class="active-users">
          <div class="sidebar-header">
            <h3>Active Users</h3>
          </div>
          <ul id="user-list" class="user-list"></ul>
        </div>
      </div>

      <div class="editor-section">
        <div class="editor-header">
          <h2 id="document-title">Select a document</h2>
          <div class="editor-actions">
            <button id="renderBtn" class="btn-render"><i class="fas fa-play"></i> Render</button>
            <button id="versionHistoryBtn" class="btn-history"><i class="fas fa-history"></i> History</button>
            <button id="vscodeSyncBtn" class="btn-sync"><i class="fas fa-sync-alt"></i> Sync to VS Code</button>
            <div class="track-changes-toggle">
              <label for="trackChangesSwitch">Track Changes</label>
              <label class="switch">
                <input type="checkbox" id="trackChangesSwitch">
                <span class="slider round"></span>
              </label>
            </div>
            <button id="pendingChangesBtn" class="btn-changes"><i class="fas fa-tasks"></i> Changes</button>
          </div>
        </div>
        <div class="editor-container" id="editor"></div>
      </div>

      <div class="preview-container" id="preview">
        <h2>Preview</h2>
        <div id="preview-content"></div>
      </div>
    </div>

    <footer>
      <div class="status">
        <span id="connection-status">Disconnected</span>
      </div>
    </footer>

    <!-- New Document Modal -->
    <div id="new-document-modal" class="modal">
      <div class="modal-content">
        <span class="close">&times;</span>
        <h2>Create New Document</h2>
        <form id="new-document-form">
          <div class="form-group">
            <label for="document-name">Document Name</label>
            <input type="text" id="document-name" required>
          </div>
          <button type="submit" class="btn-create">Create</button>
        </form>
      </div>
    </div>

    <!-- Version History Modal -->
    <div id="version-history-modal" class="modal">
      <div class="modal-content version-history-content">
        <span class="close version-close">&times;</span>
        <h2>Version History</h2>
        <div class="version-actions">
          <div class="form-group">
            <input type="text" id="version-name" placeholder="Version name (optional)">
            <button id="saveVersionBtn" class="btn-save-version">Save Current Version</button>
          </div>
        </div>
        <div class="version-list-container">
          <ul id="version-list" class="version-list"></ul>
        </div>
      </div>
    </div>

    <!-- Pending Changes Modal -->
    <div id="pending-changes-modal" class="modal">
      <div class="modal-content pending-changes-content">
        <span class="close pending-close">&times;</span>
        <h2>Pending Changes</h2>
        <div class="pending-changes-container">
          <ul id="pending-changes-list" class="pending-changes-list"></ul>
        </div>
      </div>
    </div>
  </div>

  <script src="/socket.io/socket.io.js"></script>
  <script src="js/socket.js"></script>
  <script src="dist/bundle.js"></script>
  <script src="js/projectCollaboration.js"></script>
  <script src="js/projectExplorer.js"></script>
  <script src="js/fileExplorer.js"></script>
  <script src="js/versionHistory.js"></script>
  <script src="js/trackChanges.js"></script>
  <script src="js/vscodeSync.js"></script>
</body>
</html>
