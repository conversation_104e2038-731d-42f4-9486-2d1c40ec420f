document.addEventListener('DOMContentLoaded', () => {
  const loginForm = document.getElementById('login-form');
  const registerForm = document.getElementById('register-form');
  const errorMessage = document.getElementById('error-message');

  // Handle login form submission
  if (loginForm) {
    loginForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;

      try {
        const response = await fetch('/api/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (response.ok) {
          console.log('Login successful, received sessionId:', data.sessionId ? 'present' : 'missing');
          // Store sessionId in localStorage as a backup
          if (data.sessionId) {
            localStorage.setItem('sessionId', data.sessionId);
          }
          // Redirect to main page on successful login
          window.location.href = '/';
        } else {
          // Display error message
          showError(data.error || 'Login failed');
        }
      } catch (error) {
        showError('An error occurred. Please try again.');
        console.error('Login error:', error);
      }
    });
  }

  // Handle register form submission
  if (registerForm) {
    registerForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const username = document.getElementById('username').value;
      const displayName = document.getElementById('displayName').value;
      const password = document.getElementById('password').value;
      const confirmPassword = document.getElementById('confirmPassword').value;

      // Validate passwords match
      if (password !== confirmPassword) {
        return showError('Passwords do not match');
      }

      try {
        const response = await fetch('/api/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ username, displayName, password })
        });

        const data = await response.json();

        if (response.ok) {
          console.log('Registration successful, received sessionId:', data.sessionId ? 'present' : 'missing');
          // Store sessionId in localStorage as a backup
          if (data.sessionId) {
            localStorage.setItem('sessionId', data.sessionId);
          }
          // Redirect to main page on successful registration
          window.location.href = '/';
        } else {
          // Display error message
          showError(data.error || 'Registration failed');
        }
      } catch (error) {
        showError('An error occurred. Please try again.');
        console.error('Registration error:', error);
      }
    });
  }

  // Helper function to show error messages
  function showError(message) {
    errorMessage.textContent = message;
    errorMessage.classList.add('visible');
  }
});
