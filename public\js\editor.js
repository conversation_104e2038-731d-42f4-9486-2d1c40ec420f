import { EditorState } from '@codemirror/state';
import {
  EditorView,
  keymap,
  highlightActiveLine,
  Decoration,
  ViewPlugin,
} from '@codemirror/view';
import { defaultKeymap } from '@codemirror/commands';
import { markdown } from '@codemirror/lang-markdown';
import {
  syntaxHighlighting,
  defaultHighlightStyle,
  indentOnInput,
} from '@codemirror/language';
import { history, historyKeymap } from '@codemirror/commands';
import { bracketMatching } from '@codemirror/language';
import { closeBrackets, closeBracketsKeymap } from '@codemirror/autocomplete';
import { lineNumbers, highlightActiveLineGutter } from '@codemirror/view';

// Use the shared socket from socket.js
const socket = window.sharedSocket;
console.log('Editor: Using shared socket');
let editorView;
let ignoreNextUpdate = false;
let currentDocumentId = null;
let currentUser = null;

// Remote cursors state
const remoteCursors = {};

// Cursor update throttling
let lastCursorUpdate = 0;
const CURSOR_UPDATE_THROTTLE = 100; // ms

// Function to update cursor overlays
function updateCursorOverlays() {
  if (!editorView) return;

  try {
    // Clear existing cursor overlays
    const existingCursors = document.querySelectorAll('.remote-cursor-overlay');
    existingCursors.forEach((cursor) => cursor.remove());

    // Create new cursor overlays
    for (const [userId, cursor] of Object.entries(remoteCursors)) {
      if (cursor && typeof cursor.position === 'number') {
        try {
          const position = cursor.position;
          const docLength = editorView.state.doc.length;

          // Validate position
          if (position < 0 || position > docLength) {
            console.warn(
              `Invalid cursor position ${position} for user ${userId}, skipping`
            );
            continue;
          }

          // Get cursor coordinates
          const coords = editorView.coordsAtPos(position);
          if (coords) {
            // Get editor container bounds for relative positioning
            const editorContainer = editorView.dom.parentElement;
            const containerRect = editorContainer.getBoundingClientRect();

            // Calculate relative position
            const relativeLeft = coords.left - containerRect.left;
            const relativeTop = coords.top - containerRect.top;

            // Create cursor overlay element
            const cursorElement = document.createElement('div');
            cursorElement.className = 'remote-cursor-overlay';
            cursorElement.style.position = 'absolute';
            cursorElement.style.left = relativeLeft + 'px';
            cursorElement.style.top = relativeTop + 'px';
            cursorElement.style.width = '2px';
            cursorElement.style.height = '20px';
            cursorElement.style.backgroundColor = cursor.color;
            cursorElement.style.pointerEvents = 'none';
            cursorElement.style.zIndex = '1000';
            cursorElement.setAttribute('data-user-id', userId);
            cursorElement.setAttribute('data-username', cursor.username);

            // Add cursor label
            const label = document.createElement('div');
            label.className = 'remote-cursor-label';
            label.textContent = cursor.username;
            label.style.position = 'absolute';
            label.style.top = '-25px';
            label.style.left = '0px';
            label.style.fontSize = '12px';
            label.style.backgroundColor = cursor.color;
            label.style.color = 'white';
            label.style.padding = '2px 4px';
            label.style.borderRadius = '3px';
            label.style.whiteSpace = 'nowrap';
            label.style.pointerEvents = 'none';

            cursorElement.appendChild(label);

            // Add to editor container (reuse the editorContainer variable)
            // Make sure the container has relative positioning
            if (editorContainer.style.position !== 'relative') {
              editorContainer.style.position = 'relative';
            }

            editorContainer.appendChild(cursorElement);
          }
        } catch (error) {
          console.error(
            `Error creating cursor overlay for user ${userId}:`,
            error
          );
        }
      }
    }
  } catch (error) {
    console.error('Error updating cursor overlays:', error);
  }
}

// Track changes state
let trackChangesMode = false;
let pendingChanges = [];

// Change buffer for merging changes
let changeBuffer = [];
let changeBufferTimeout = null;
const CHANGE_BUFFER_DELAY = 1000; // 1 second delay for merging changes
const CHANGE_PROXIMITY_THRESHOLD = 10; // Characters proximity for merging changes

// Function to merge changes that are close to each other
function mergeChanges(changes) {
  if (!changes || changes.length <= 1) return changes;

  console.log('Merging changes, count before:', changes.length);

  // Sort changes by position
  const sortedChanges = [...changes].sort((a, b) => a.fromA - b.fromA);

  const mergedChanges = [];
  let currentChange = sortedChanges[0];

  for (let i = 1; i < sortedChanges.length; i++) {
    const nextChange = sortedChanges[i];

    // Check if changes are close enough to merge
    // Don't try to merge changes if they don't have userId (safety check)
    if (
      nextChange.fromA - currentChange.toA <= CHANGE_PROXIMITY_THRESHOLD &&
      currentChange.userId &&
      nextChange.userId &&
      nextChange.userId === currentChange.userId
    ) {
      // Merge the changes
      const mergedChange = {
        fromA: Math.min(currentChange.fromA, nextChange.fromA),
        toA: Math.max(currentChange.toA, nextChange.toA),
        fromB: Math.min(currentChange.fromB, nextChange.fromB),
        toB: Math.max(currentChange.toB, nextChange.toB),
        inserted: currentChange.inserted + nextChange.inserted,
        userId: currentChange.userId,
      };

      currentChange = mergedChange;
    } else {
      // If not close enough, add the current change to results and move to next
      mergedChanges.push(currentChange);
      currentChange = nextChange;
    }
  }

  // Add the last change
  mergedChanges.push(currentChange);

  console.log('Merging changes, count after:', mergedChanges.length);
  return mergedChanges;
}

// Function to process the change buffer
function processChangeBuffer() {
  if (changeBuffer.length === 0) return;

  console.log('Processing change buffer, size:', changeBuffer.length);

  // Merge changes
  const mergedChanges = mergeChanges(changeBuffer);

  // Get the current content
  const content = editorView.state.doc.toString();

  // Send merged changes to server
  socket.emit('update-content', {
    documentId: currentDocumentId,
    content,
    changes: mergedChanges,
    trackChangesMode,
  });

  // Clear the buffer
  changeBuffer = [];
}

// Log initialization of track changes variables
console.log('Editor module: Initializing track changes variables');

// Export for fileExplorer.js and trackChanges.js
window.editorModule = {
  setCurrentDocumentId: (docId) => {
    console.log('Editor module: Setting current document ID to', docId);
    currentDocumentId = docId;
  },
  getCurrentDocumentId: () => currentDocumentId,
  getEditorContent: () => {
    if (editorView) {
      return editorView.state.doc.toString();
    }
    return '';
  },
  setTrackChangesMode: (mode) => {
    console.log('Editor module: Setting track changes mode to', mode);
    trackChangesMode = mode;
  },
  highlightPendingChanges: (changes) => {
    console.log('Editor module: Highlighting pending changes', changes.length);
    // Log the first few changes for debugging
    if (changes.length > 0) {
      console.log('Sample change:', JSON.stringify(changes[0]));
    }
    pendingChanges = changes;
    if (editorView) {
      // Force a redraw to update decorations
      editorView.dispatch({});
    }
  },
};

// Connection status
const connectionStatus = document.getElementById('connection-status');
let isAuthenticated = false;

function updateConnectionStatus() {
  if (socket.connected && isAuthenticated) {
    connectionStatus.textContent = 'Connected';
    connectionStatus.className = 'connected';
  } else if (socket.connected && !isAuthenticated) {
    connectionStatus.textContent = 'Authenticating...';
    connectionStatus.className = 'connecting';
  } else {
    connectionStatus.textContent = 'Disconnected';
    connectionStatus.className = 'disconnected';
  }
}

socket.on('connect', () => {
  console.log('Socket connected');
  updateConnectionStatus();
});

socket.on('disconnect', () => {
  console.log('Socket disconnected');
  isAuthenticated = false;
  updateConnectionStatus();
});

socket.on('authentication-success', () => {
  console.log('Authentication successful');
  isAuthenticated = true;
  updateConnectionStatus();
});

socket.on('authentication-failed', () => {
  console.log('Authentication failed');
  isAuthenticated = false;
  updateConnectionStatus();
});

// Initial status update
updateConnectionStatus();

// Create our own basic setup since @codemirror/basic-setup is not available
const basicSetup = [
  lineNumbers(),
  highlightActiveLineGutter(),
  highlightActiveLine(),
  history(),
  indentOnInput(),
  syntaxHighlighting(defaultHighlightStyle),
  bracketMatching(),
  closeBrackets(),
  keymap.of([...defaultKeymap, ...historyKeymap, ...closeBracketsKeymap]),
];

// Simple cursor widget that implements all required methods
class SimpleCursorWidget {
  constructor(cursor) {
    this.cursor = cursor;
    this.dom = null;
  }

  eq(other) {
    // Simple equality check
    return (
      other &&
      other.cursor &&
      this.cursor.userId === other.cursor.userId &&
      this.cursor.position === other.cursor.position
    );
  }

  toDOM() {
    // Create cursor element
    const cursorElt = document.createElement('div');
    cursorElt.className = 'remote-cursor';
    cursorElt.style.borderLeftWidth = '2px';
    cursorElt.style.borderLeftStyle = 'solid';
    cursorElt.style.borderLeftColor = this.cursor.color;
    cursorElt.style.height = '1.2em';

    // Create label element
    const labelElt = document.createElement('div');
    labelElt.className = 'cursor-label';
    labelElt.textContent = this.cursor.username;
    labelElt.style.backgroundColor = this.cursor.color;
    cursorElt.appendChild(labelElt);

    this.dom = cursorElt;
    return cursorElt;
  }

  // Add all required methods to prevent errors
  ignoreEvent() {
    return false;
  }
  get estimatedHeight() {
    return 0;
  }
  destroy() {
    // Clean up any resources if needed
    if (this.dom) {
      // Remove any event listeners or references
      this.dom = null;
    }
  }

  // Add these methods to be safe
  update() {
    return false;
  }
  compare() {
    return true;
  }
  get isHidden() {
    return false;
  }
}

// Remote cursor plugin
function createRemoteCursorsPlugin() {
  return ViewPlugin.fromClass(
    class {
      decorations;

      constructor(view) {
        this.decorations = Decoration.none;
        this.updateDecorations(view);
      }

      update(update) {
        // Always update decorations to ensure cursors are displayed
        this.updateDecorations(update.view);
      }

      updateDecorations(view) {
        try {
          const decorations = [];

          Object.keys(remoteCursors).forEach((userId) => {
            try {
              const cursor = remoteCursors[userId];
              if (
                !cursor ||
                cursor.position === undefined ||
                cursor.position === null ||
                isNaN(cursor.position)
              ) {
                console.log(
                  `Skipping cursor for user ${userId}: invalid cursor data`,
                  cursor
                );
                return;
              }

              console.log(
                `Creating cursor decoration for user ${userId} at position ${cursor.position}`
              );

              // Add decoration with our custom widget
              const widget = new SimpleCursorWidget(cursor);

              // Ensure position is within document bounds
              const pos = Math.min(
                Math.max(0, cursor.position),
                view.state.doc.length
              );

              console.log(
                `Final cursor position for user ${userId}: ${pos} (doc length: ${view.state.doc.length})`
              );

              // Create widget decoration at the cursor position
              // Widget decorations are point decorations (zero-length)
              try {
                // Ensure pos is a valid integer and within document bounds
                const docLength = editorView.state.doc.length;
                let position = Math.floor(pos);

                // Clamp position to valid range
                if (position < 0) position = 0;
                if (position > docLength) position = docLength;

                console.log(
                  `Creating decoration at position ${position} (doc length: ${docLength})`
                );

                // Try using a line decoration instead of widget/mark decoration
                const line = editorView.state.doc.lineAt(position);
                const decoration = Decoration.line({
                  class: 'remote-cursor-line',
                  attributes: {
                    style: `border-left: 3px solid ${cursor.color}; padding-left: 5px;`,
                    'data-user': cursor.username,
                  },
                }).range(line.from);

                decorations.push(decoration);
                console.log(
                  `Successfully created cursor decoration for user ${userId} at position ${position}`
                );
              } catch (rangeError) {
                console.error(
                  `Error creating decoration range for user ${userId}:`,
                  rangeError
                );
                console.error(
                  `Position: ${pos}, Type: ${typeof pos}, Floor: ${Math.floor(
                    pos
                  )}, Doc length: ${editorView.state.doc.length}`
                );

                // Try alternative approach - create decoration at document start
                try {
                  const altDecoration = Decoration.widget({
                    widget,
                    side: 1,
                  }).range(0);
                  decorations.push(altDecoration);
                  console.log(
                    `Fallback decoration created for user ${userId} at position 0`
                  );
                } catch (fallbackError) {
                  console.error(
                    `Fallback decoration also failed for user ${userId}:`,
                    fallbackError
                  );
                }
              }
            } catch (error) {
              console.error('Error creating cursor for user:', userId, error);
              console.error('Cursor data:', remoteCursors[userId]);
            }
          });

          this.decorations = Decoration.set(decorations);
        } catch (error) {
          console.error('Error updating decorations:', error);
          this.decorations = Decoration.none;
        }
      }
    },
    {
      decorations: (v) => v.decorations,
    }
  );
}

// Create a decoration for pending changes
function createPendingChangesPlugin() {
  return ViewPlugin.fromClass(
    class {
      decorations;

      constructor(view) {
        this.decorations = Decoration.none;
        this.updateDecorations(view);
      }

      update(update) {
        this.updateDecorations(update.view);
      }

      updateDecorations(view) {
        try {
          console.log(
            'Updating pending change decorations, count:',
            pendingChanges ? pendingChanges.length : 0
          );

          if (!pendingChanges || pendingChanges.length === 0) {
            console.log('No pending changes to highlight');
            this.decorations = Decoration.none;
            return;
          }

          const decorations = [];
          console.log('Document length:', view.state.doc.length);

          // First collect all valid decorations with their positions
          const validDecorations = [];

          pendingChanges.forEach((change) => {
            try {
              // Create a mark decoration for the pending change
              const deco = Decoration.mark({
                class: 'cm-pending-change',
                attributes: { title: `Change by ${change.userName}` },
              });

              // Ensure positions are within document bounds
              // Handle both fromPos/toPos and from_pos/to_pos naming conventions
              const fromPos =
                change.fromPos !== undefined ? change.fromPos : change.from_pos;
              const toPos =
                change.toPos !== undefined ? change.toPos : change.to_pos;

              console.log(
                `Change positions - fromPos: ${fromPos}, toPos: ${toPos}, text: "${change.insertedText}", id: ${change.id}`
              );

              const from = Math.min(fromPos || 0, view.state.doc.length);
              const to = Math.min(toPos || 0, view.state.doc.length);

              // Only add decoration if the range is valid
              if (from < to) {
                validDecorations.push({ deco, from, to });
              } else if (from === to && change.insertedText) {
                // For insertions at a point
                validDecorations.push({
                  deco,
                  from,
                  to: from + change.insertedText.length,
                });
              }
            } catch (error) {
              console.error(
                'Error creating decoration for change:',
                change,
                error
              );
            }
          });

          // Sort decorations by from position
          validDecorations.sort((a, b) => a.from - b.from);

          // Now create the ranges in sorted order
          validDecorations.forEach(({ deco, from, to }) => {
            decorations.push(deco.range(from, to));
          });

          this.decorations = Decoration.set(decorations);
        } catch (error) {
          console.error('Error updating pending change decorations:', error);
          this.decorations = Decoration.none;
        }
      }
    },
    {
      decorations: (v) => v.decorations,
    }
  );
}

// Initialize the editor
function initEditor(initialContent = '') {
  const remoteCursorsPlugin = createRemoteCursorsPlugin();
  const pendingChangesPlugin = createPendingChangesPlugin();

  const startState = EditorState.create({
    doc: initialContent,
    extensions: [
      basicSetup,
      markdown(),
      remoteCursorsPlugin,
      pendingChangesPlugin,
      EditorView.updateListener.of((update) => {
        if (update.docChanged && !ignoreNextUpdate && currentDocumentId) {
          const content = update.state.doc.toString();

          // Extract changes
          const changes = [];
          update.changes.iterChanges((fromA, toA, fromB, toB, inserted) => {
            changes.push({
              fromA,
              toA,
              fromB,
              toB,
              inserted: inserted.toString(),
            });
          });

          if (trackChangesMode) {
            // In track changes mode, buffer changes for merging
            console.log('Adding changes to buffer:', changes.length);

            // Add changes to buffer
            changeBuffer.push(...changes);

            // Clear any existing timeout
            if (changeBufferTimeout) {
              clearTimeout(changeBufferTimeout);
            }

            // Set a new timeout to process the buffer
            changeBufferTimeout = setTimeout(() => {
              processChangeBuffer();
              changeBufferTimeout = null;
            }, CHANGE_BUFFER_DELAY);
          } else {
            // In normal mode, send changes immediately
            socket.emit('update-content', {
              documentId: currentDocumentId,
              content,
              changes,
              trackChangesMode,
            });
          }
        }

        // Send cursor position updates (on selection changes, including clicks)
        if (update.selectionSet && currentDocumentId) {
          const now = Date.now();
          if (now - lastCursorUpdate > CURSOR_UPDATE_THROTTLE) {
            const position = update.state.selection.main.head;
            const doc = update.state.doc;
            const line = doc.lineAt(position);
            const lineNumber = line.number - 1; // Convert to 0-based
            const character = position - line.from;

            // Send cursor update
            socket.emit('cursor-update', {
              documentId: currentDocumentId,
              position: {
                line: lineNumber,
                character: character,
              },
            });

            lastCursorUpdate = now;
          }
        }

        ignoreNextUpdate = false;
      }),
    ],
  });

  editorView = new EditorView({
    state: startState,
    parent: document.getElementById('editor'),
  });

  // Add click listener to ensure cursor updates are sent on clicks
  editorView.dom.addEventListener('click', () => {
    if (currentDocumentId && editorView) {
      setTimeout(() => {
        const position = editorView.state.selection.main.head;
        const doc = editorView.state.doc;
        const line = doc.lineAt(position);
        const lineNumber = line.number - 1; // Convert to 0-based
        const character = position - line.from;

        socket.emit('cursor-update', {
          documentId: currentDocumentId,
          position: {
            line: lineNumber,
            character: character,
          },
        });
      }, 10); // Small delay to ensure selection is updated
    }
  });
}

// Handle authentication success
socket.on('authentication-success', ({ user }) => {
  currentUser = user;
});

// Handle initial document loading
socket.on('init-document', ({ content, documentId }) => {
  console.log('Editor received init-document event:', {
    documentId,
    contentLength: content.length,
  });
  currentDocumentId = documentId;

  if (!editorView) {
    console.log('Initializing editor with content');
    initEditor(content);
  } else {
    console.log('Updating editor content');
    updateEditorContent(content);
  }
});

// Handle content updates from other clients
socket.on('content-updated', ({ content }) => {
  updateEditorContent(content);
});

// Handle remote cursor updates
socket.on('remote-cursor', (cursor) => {
  // Convert line/character position to CodeMirror document position
  if (
    cursor.position &&
    typeof cursor.position === 'object' &&
    cursor.position.line !== undefined &&
    cursor.position.character !== undefined &&
    editorView
  ) {
    try {
      const doc = editorView.state.doc;

      // Validate line number (cursor.position.line is 0-based)
      const lineNumber = Math.max(
        0,
        Math.min(cursor.position.line, doc.lines - 1)
      );

      // Get the line (CodeMirror lines are 1-based)
      const line = doc.line(lineNumber + 1);

      // Validate character position
      const character = Math.max(
        0,
        Math.min(cursor.position.character, line.length)
      );
      const docPosition = line.from + character;

      // Validate final position
      const finalPosition = Math.max(0, Math.min(docPosition, doc.length));

      remoteCursors[cursor.userId] = {
        ...cursor,
        position: finalPosition,
      };
    } catch (error) {
      console.error('Error converting cursor position:', error);
      // Fallback: use position 0
      remoteCursors[cursor.userId] = {
        ...cursor,
        position: 0,
      };
    }
  } else {
    // If position is already a number, use as-is
    if (typeof cursor.position === 'number') {
      remoteCursors[cursor.userId] = cursor;
    } else {
      // Set to position 0 as fallback
      remoteCursors[cursor.userId] = {
        ...cursor,
        position: 0,
      };
    }
  }

  if (editorView) {
    updateCursorOverlays();
  }
});

// Handle user joining
socket.on('user-joined', (userData) => {
  remoteCursors[userData.userId] = {
    username: userData.username,
    color: userData.color,
    position: 0,
  };
});

// Handle user leaving
socket.on('user-left', ({ userId }) => {
  delete remoteCursors[userId];
  if (editorView) {
    updateCursorOverlays();
  }
});

// Handle cursor position requests
socket.on('request-cursor-position', () => {
  console.log('Cursor position requested by server');
  // Send current cursor position if we have an active editor and document
  if (editorView && currentDocumentId) {
    const position = editorView.state.selection.main.head;
    const doc = editorView.state.doc;
    const line = doc.lineAt(position);
    const lineNumber = line.number - 1; // Convert to 0-based
    const character = position - line.from;

    console.log('Sending requested cursor position:', lineNumber, character);

    socket.emit('cursor-update', {
      documentId: currentDocumentId,
      position: {
        line: lineNumber,
        character: character,
      },
    });
  }
});

// Handle active users updates
socket.on('active-users-updated', (users) => {
  console.log('Web client received active-users-updated:', users);
  console.log(
    'Number of users:',
    users ? users.length : 'users is null/undefined'
  );
  console.log('Users array details:', JSON.stringify(users, null, 2));
  updateActiveUsersList(users);
});

// Handle document users response
socket.on('document-users', (data) => {
  console.log('Web client received document-users:', data);
  if (data && data.users) {
    console.log('Document users:', JSON.stringify(data.users, null, 2));
    updateActiveUsersList(data.users);
  }
});

// Handle preview updates
socket.on('preview-updated', ({ html }) => {
  document.getElementById('preview-content').innerHTML = html;
});

function updateEditorContent(content) {
  console.log('Updating editor content, length:', content.length);
  if (editorView) {
    ignoreNextUpdate = true;
    editorView.dispatch({
      changes: {
        from: 0,
        to: editorView.state.doc.length,
        insert: content,
      },
    });
    console.log('Editor content updated');

    // Update cursor overlays after content change
    setTimeout(() => {
      updateCursorOverlays();
    }, 100);
  } else {
    console.warn('Cannot update editor content: editor not initialized');
  }
}

// Render button functionality
document.getElementById('renderBtn').addEventListener('click', () => {
  if (!currentDocumentId) {
    alert('Please select a document first');
    return;
  }

  fetch('/api/render', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ documentId: currentDocumentId }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.html) {
        document.getElementById('preview-content').innerHTML = data.html;
      } else if (data.error) {
        document.getElementById(
          'preview-content'
        ).innerHTML = `<div class="error">${data.error}</div>`;
      }
    })
    .catch((error) => {
      console.error('Error rendering document:', error);
      document.getElementById(
        'preview-content'
      ).innerHTML = `<div class="error">Error rendering document: ${error.message}</div>`;
    });
});

// Update active users list in the UI
function updateActiveUsersList(users) {
  console.log('updateActiveUsersList called with:', users);
  const userList = document.getElementById('user-list');
  if (!userList) {
    console.error('user-list element not found!');
    return;
  }

  // Clear existing list
  userList.innerHTML = '';

  if (!users || !Array.isArray(users)) {
    console.log('No users or invalid users array');
    return;
  }

  // Add each user
  users.forEach((user) => {
    console.log('Adding user to list:', user);
    const userItem = document.createElement('li');
    userItem.className = 'user-item';
    userItem.innerHTML = `
      <div class="user-avatar" style="background-color: ${user.color}">
        ${user.displayName.charAt(0).toUpperCase()}
      </div>
      <span class="user-name">${user.displayName}</span>
    `;
    userList.appendChild(userItem);
  });

  console.log(`Updated active users list: ${users.length} users`);
}
