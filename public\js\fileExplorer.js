document.addEventListener('DOMContentLoaded', () => {
  // DOM Elements
  const documentList = document.getElementById('document-list');
  const userList = document.getElementById('user-list');
  const newDocumentBtn = document.getElementById('newDocumentBtn');
  const newDocumentModal = document.getElementById('new-document-modal');
  const newDocumentForm = document.getElementById('new-document-form');
  const closeModalBtn = document.querySelector('.close');
  const documentTitle = document.getElementById('document-title');
  const currentUserElement = document.getElementById('current-user');
  const logoutBtn = document.getElementById('logoutBtn');
  const projectExplorerContainer = document.getElementById('projectExplorer');

  // Current state
  let currentUser = null;
  let currentDocumentId = null;
  let documents = [];
  let activeUsers = [];
  let userColors = {};
  let projectExplorer = null;

  // Use the shared socket from socket.js
  const socket = window.sharedSocket;
  console.log('FileExplorer: Using shared socket');

  // Check authentication status
  checkAuth();

  // Initialize project explorer
  if (window.ProjectExplorer && projectExplorerContainer) {
    projectExplorer = new ProjectExplorer(projectExplorerContainer, {
      onDocumentSelect: (document) => {
        openDocument(document.id);
      },
      onProjectChange: (project) => {
        console.log('Active project changed:', project.name);
      },
    });

    // Make project explorer globally available
    window.projectExplorer = projectExplorer;
  }

  // Event listeners
  if (newDocumentBtn) {
    newDocumentBtn.addEventListener('click', () => {
      newDocumentModal.style.display = 'block';
    });
  }

  if (closeModalBtn) {
    closeModalBtn.addEventListener('click', () => {
      if (newDocumentModal) {
        newDocumentModal.style.display = 'none';
      }
    });
  }

  if (newDocumentModal) {
    window.addEventListener('click', (e) => {
      if (e.target === newDocumentModal) {
        newDocumentModal.style.display = 'none';
      }
    });
  }

  if (newDocumentForm) {
    newDocumentForm.addEventListener('submit', (e) => {
      e.preventDefault();
      createNewDocument();
    });
  }

  if (logoutBtn) {
    logoutBtn.addEventListener('click', logout);
  }

  // Socket event handlers
  socket.on('document-list', (documentsList) => {
    console.log('Received document list:', documentsList);
    documents = documentsList;
    renderDocumentList();
  });

  socket.on('active-users-updated', (users) => {
    console.log('Received active users update:', users);
    activeUsers = users;
    renderUserList();
  });

  socket.on('user-joined', (userData) => {
    console.log('User joined:', userData);
    userColors[userData.userId] = userData.color;
    // The active users list will be updated separately
  });

  socket.on('user-left', ({ userId }) => {
    console.log('User left:', userId);
    // The active users list will be updated separately
  });

  socket.on('authentication-success', (data) => {
    console.log('Authentication success:', data);
  });

  socket.on('authentication-failed', () => {
    console.log('Authentication failed');
  });

  socket.on('error', (error) => {
    console.error('Socket error:', error);
  });

  // Functions
  function checkAuth() {
    console.log('Checking authentication...');
    fetch('/api/user')
      .then((response) => {
        if (!response.ok) {
          console.log('Not authenticated, redirecting to login');
          window.location.href = '/login.html';
          return null;
        }
        return response.json();
      })
      .then((data) => {
        if (data && data.user) {
          console.log('User authenticated:', data.user);
          currentUser = data.user;
          currentUserElement.textContent = currentUser.displayName;

          // Authenticate socket connection
          const sessionId = getCookie('sessionId');
          console.log(
            'Authenticating socket with sessionId:',
            sessionId ? 'present' : 'missing'
          );
          socket.emit('authenticate', { sessionId });
        }
      })
      .catch((error) => {
        console.error('Auth check error:', error);
        window.location.href = '/login.html';
      });
  }

  function renderDocumentList() {
    if (!documentList) {
      console.log('documentList element not found, skipping render');
      return;
    }

    documentList.innerHTML = '';

    documents.forEach((doc) => {
      const li = document.createElement('li');
      li.textContent = doc.name;
      li.dataset.id = doc.id;

      if (doc.id === currentDocumentId) {
        li.classList.add('active');
      }

      li.addEventListener('click', () => {
        openDocument(doc.id);
      });

      documentList.appendChild(li);
    });
  }

  function renderUserList() {
    if (!userList) {
      console.log('userList element not found, skipping render');
      return;
    }

    userList.innerHTML = '';

    if (!activeUsers || !activeUsers.length) {
      const li = document.createElement('li');
      li.textContent = 'No active users';
      userList.appendChild(li);
      return;
    }

    // If activeUsers already contains full user objects, use them directly
    if (
      activeUsers.length > 0 &&
      typeof activeUsers[0] === 'object' &&
      activeUsers[0].displayName
    ) {
      activeUsers.forEach((user) => {
        const li = document.createElement('li');

        const colorSpan = document.createElement('span');
        colorSpan.className = 'user-color';
        colorSpan.style.backgroundColor = user.color || '#ccc';

        const nameSpan = document.createElement('span');
        nameSpan.textContent = user.displayName;

        li.appendChild(colorSpan);
        li.appendChild(nameSpan);
        userList.appendChild(li);
      });
      return;
    }

    // Otherwise, fetch user details from server (for backward compatibility)
    const userIds = activeUsers.map((user) =>
      typeof user === 'object' ? user.id : user
    );
    fetch('/api/users/details', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userIds: userIds }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.users && data.users.length) {
          data.users.forEach((user) => {
            const li = document.createElement('li');

            const colorSpan = document.createElement('span');
            colorSpan.className = 'user-color';
            colorSpan.style.backgroundColor = user.color || '#ccc';

            const nameSpan = document.createElement('span');
            nameSpan.textContent = user.displayName;

            li.appendChild(colorSpan);
            li.appendChild(nameSpan);

            userList.appendChild(li);
          });
        } else {
          const li = document.createElement('li');
          li.textContent = 'No active users';
          userList.appendChild(li);
        }
      })
      .catch((error) => {
        console.error('Error fetching user details:', error);
        const li = document.createElement('li');
        li.textContent = 'Error loading users';
        userList.appendChild(li);
      });
  }

  function openDocument(documentId) {
    currentDocumentId = documentId;

    // Update shared state in editor.js
    if (window.editorModule) {
      window.editorModule.setCurrentDocumentId(documentId);
    }

    // Update UI - highlight selected document in project explorer
    const allDocumentItems = document.querySelectorAll('.document-item');
    allDocumentItems.forEach((item) => item.classList.remove('active'));

    const selectedItem = document.querySelector(
      `.document-item[data-document-id="${documentId}"]`
    );
    if (selectedItem) {
      selectedItem.classList.add('active');
    }

    // Join document via socket
    socket.emit('join-document', { documentId });

    // Request active users for this document
    console.log('Requesting active users for document:', documentId);
    socket.emit('get-document-users', { documentId });

    // Update document title - try to get document info from project explorer first
    let documentName = documentId; // fallback
    if (projectExplorer && projectExplorer.projectStructure) {
      const doc = projectExplorer.projectStructure.documents.find(
        (d) => d.id === documentId
      );
      if (doc) {
        documentName = doc.name;
      }
    } else {
      // Fallback to old documents array
      const document = documents.find((doc) => doc.id === documentId);
      if (document) {
        documentName = document.name;
      }
    }

    documentTitle.textContent = documentName;
  }

  function createNewDocument() {
    const nameInput = document.getElementById('document-name');
    const name = nameInput.value.trim();

    if (!name) return;

    fetch('/api/documents', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({ name, content: '' }),
    })
      .then((response) => response.json())
      .then((data) => {
        // Close modal and reset form
        newDocumentModal.style.display = 'none';
        nameInput.value = '';

        // Open the new document
        documents.push(data.document);
        renderDocumentList();
        openDocument(data.document.id);
      })
      .catch((error) => {
        console.error('Error creating document:', error);
      });
  }

  function logout() {
    fetch('/api/logout', {
      method: 'POST',
    })
      .then(() => {
        window.location.href = '/login.html';
      })
      .catch((error) => {
        console.error('Logout error:', error);
      });
  }

  // Use the getCookie function from socket.js
  function getCookie(name) {
    return window.getCookie ? window.getCookie(name) : null;
  }
});
