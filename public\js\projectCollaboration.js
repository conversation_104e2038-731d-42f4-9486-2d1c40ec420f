/**
 * Project Collaboration - Handle invitations and member management
 */
class ProjectCollaboration {
  constructor() {
    this.pendingInvitations = [];
    this.init();
  }

  init() {
    // Wait a bit for other scripts to load
    setTimeout(() => {
      this.loadPendingInvitations();
      this.setupNotificationArea();
    }, 1000);
  }

  setupNotificationArea() {
    // Create notification area if it doesn't exist
    let notificationArea = document.getElementById('notification-area');
    if (!notificationArea) {
      notificationArea = document.createElement('div');
      notificationArea.id = 'notification-area';
      notificationArea.className = 'notification-area';
      document.body.appendChild(notificationArea);
    }
  }

  async loadPendingInvitations() {
    try {
      const response = await fetch('/api/invitations', {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        this.pendingInvitations = data.invitations;
        this.showInvitationNotifications();
      }
    } catch (error) {
      console.error('Error loading pending invitations:', error);
    }
  }

  showInvitationNotifications() {
    if (this.pendingInvitations.length === 0) return;

    const notificationArea = document.getElementById('notification-area');
    
    this.pendingInvitations.forEach(invitation => {
      const notification = document.createElement('div');
      notification.className = 'invitation-notification';
      notification.innerHTML = `
        <div class="invitation-content">
          <div class="invitation-header">
            <i class="fas fa-user-plus"></i>
            <strong>Project Invitation</strong>
          </div>
          <div class="invitation-details">
            <p><strong>${invitation.invited_by_name}</strong> invited you to join:</p>
            <p class="project-name">${invitation.project_name}</p>
            ${invitation.project_description ? `<p class="project-description">${invitation.project_description}</p>` : ''}
          </div>
          <div class="invitation-actions">
            <button class="btn btn-accept" onclick="projectCollaboration.acceptInvitation('${invitation.id}')">
              <i class="fas fa-check"></i> Accept
            </button>
            <button class="btn btn-decline" onclick="projectCollaboration.declineInvitation('${invitation.id}')">
              <i class="fas fa-times"></i> Decline
            </button>
          </div>
        </div>
        <button class="close-notification" onclick="this.parentElement.remove()">
          <i class="fas fa-times"></i>
        </button>
      `;
      
      notificationArea.appendChild(notification);
    });
  }

  async acceptInvitation(invitationId) {
    try {
      console.log('Accepting invitation:', invitationId);
      console.log('Project explorer available:', !!window.projectExplorer);
      console.log('LoadProjects method available:', window.projectExplorer && typeof window.projectExplorer.loadProjects);

      const response = await fetch(`/api/invitations/${invitationId}/accept`, {
        method: 'POST',
        credentials: 'include'
      });

      console.log('Accept invitation response status:', response.status);

      if (response.ok) {
        // Remove notification
        const notification = document.querySelector(`[onclick*="${invitationId}"]`).closest('.invitation-notification');
        if (notification) {
          notification.remove();
        }

        // Refresh project list
        if (window.projectExplorer && typeof window.projectExplorer.loadProjects === 'function') {
          console.log('Refreshing project list...');
          await window.projectExplorer.loadProjects();
        } else {
          console.log('Project explorer not available, refreshing page...');
          window.location.reload();
        }

        this.showSuccessMessage('Invitation accepted! You now have access to the project.');
      } else {
        const errorData = await response.json();
        console.error('Failed to accept invitation:', errorData);
        this.showErrorMessage(errorData.error || 'Failed to accept invitation');
      }
    } catch (error) {
      console.error('Error accepting invitation:', error);
      this.showErrorMessage('Error accepting invitation');
    }
  }

  async declineInvitation(invitationId) {
    try {
      const response = await fetch(`/api/invitations/${invitationId}/decline`, {
        method: 'POST',
        credentials: 'include'
      });

      if (response.ok) {
        // Remove notification
        const notification = document.querySelector(`[onclick*="${invitationId}"]`).closest('.invitation-notification');
        if (notification) {
          notification.remove();
        }

        this.showSuccessMessage('Invitation declined.');
      } else {
        const errorData = await response.json();
        this.showErrorMessage(errorData.error || 'Failed to decline invitation');
      }
    } catch (error) {
      console.error('Error declining invitation:', error);
      this.showErrorMessage('Error declining invitation');
    }
  }

  async inviteUserToProject(projectId, username) {
    try {
      const response = await fetch(`/api/projects/${projectId}/invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ username })
      });

      if (response.ok) {
        this.showSuccessMessage(`Invitation sent to ${username}`);
        return { success: true };
      } else {
        const errorData = await response.json();
        this.showErrorMessage(errorData.error || 'Failed to send invitation');
        return { success: false, error: errorData.error };
      }
    } catch (error) {
      console.error('Error inviting user:', error);
      this.showErrorMessage('Error sending invitation');
      return { success: false, error: 'Network error' };
    }
  }

  async getProjectMembers(projectId) {
    try {
      const response = await fetch(`/api/projects/${projectId}/members`, {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        return data.members;
      } else {
        console.error('Failed to load project members');
        return [];
      }
    } catch (error) {
      console.error('Error loading project members:', error);
      return [];
    }
  }

  async removeUserFromProject(projectId, userId) {
    try {
      const response = await fetch(`/api/projects/${projectId}/members/${userId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (response.ok) {
        this.showSuccessMessage('User removed from project');
        return { success: true };
      } else {
        const errorData = await response.json();
        this.showErrorMessage(errorData.error || 'Failed to remove user');
        return { success: false, error: errorData.error };
      }
    } catch (error) {
      console.error('Error removing user:', error);
      this.showErrorMessage('Error removing user');
      return { success: false, error: 'Network error' };
    }
  }

  showInviteUserDialog(projectId, projectName) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3>Invite User to ${projectName}</h3>
          <span class="close">&times;</span>
        </div>
        <div class="modal-body">
          <form id="invite-user-form">
            <div class="form-group">
              <label for="username">Username:</label>
              <input type="text" id="username" name="username" required 
                     placeholder="Enter username to invite">
            </div>
            <div class="form-actions">
              <button type="submit" class="btn btn-primary">Send Invitation</button>
              <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">Cancel</button>
            </div>
          </form>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Setup event listeners
    const closeBtn = modal.querySelector('.close');
    closeBtn.addEventListener('click', () => modal.remove());

    const form = modal.querySelector('#invite-user-form');
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      const username = form.username.value.trim();
      if (username) {
        const result = await this.inviteUserToProject(projectId, username);
        if (result.success) {
          modal.remove();
        }
      }
    });

    // Show modal
    modal.style.display = 'block';
  }

  showProjectMembersDialog(projectId, projectName) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3>Members of ${projectName}</h3>
          <span class="close">&times;</span>
        </div>
        <div class="modal-body">
          <div id="members-list">Loading...</div>
          <div class="modal-actions">
            <button class="btn btn-primary" onclick="projectCollaboration.showInviteUserDialog('${projectId}', '${projectName}')">
              <i class="fas fa-user-plus"></i> Invite User
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Setup event listeners
    const closeBtn = modal.querySelector('.close');
    closeBtn.addEventListener('click', () => modal.remove());

    // Load members
    this.loadProjectMembers(projectId, modal);

    // Show modal
    modal.style.display = 'block';
  }

  async loadProjectMembers(projectId, modal) {
    const membersList = modal.querySelector('#members-list');
    
    try {
      const members = await this.getProjectMembers(projectId);
      
      if (members.length === 0) {
        membersList.innerHTML = '<p>No members found.</p>';
        return;
      }

      membersList.innerHTML = members.map(member => `
        <div class="member-item">
          <div class="member-info">
            <div class="member-avatar" style="background-color: ${member.color || '#007acc'}">
              ${member.display_name ? member.display_name.charAt(0).toUpperCase() : member.username.charAt(0).toUpperCase()}
            </div>
            <div class="member-details">
              <div class="member-name">${member.display_name || member.username}</div>
              <div class="member-role">${member.role}</div>
              <div class="member-joined">Joined ${new Date(member.joined_at).toLocaleDateString()}</div>
            </div>
          </div>
          ${member.role !== 'owner' ? `
            <button class="btn btn-danger btn-sm" onclick="projectCollaboration.confirmRemoveUser('${projectId}', '${member.id}', '${member.display_name || member.username}')">
              <i class="fas fa-user-minus"></i>
            </button>
          ` : ''}
        </div>
      `).join('');
    } catch (error) {
      membersList.innerHTML = '<p>Error loading members.</p>';
    }
  }

  confirmRemoveUser(projectId, userId, userName) {
    if (confirm(`Are you sure you want to remove ${userName} from this project?`)) {
      this.removeUserFromProject(projectId, userId).then(result => {
        if (result.success) {
          // Reload the members list
          const modal = document.querySelector('.modal');
          if (modal) {
            this.loadProjectMembers(projectId, modal);
          }
        }
      });
    }
  }

  showSuccessMessage(message) {
    this.showMessage(message, 'success');
  }

  showErrorMessage(message) {
    this.showMessage(message, 'error');
  }

  showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.innerHTML = `
      <span>${message}</span>
      <button onclick="this.parentElement.remove()"><i class="fas fa-times"></i></button>
    `;

    const notificationArea = document.getElementById('notification-area');
    notificationArea.appendChild(messageDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (messageDiv.parentElement) {
        messageDiv.remove();
      }
    }, 5000);
  }
}

// Initialize collaboration system
const projectCollaboration = new ProjectCollaboration();
window.projectCollaboration = projectCollaboration;
