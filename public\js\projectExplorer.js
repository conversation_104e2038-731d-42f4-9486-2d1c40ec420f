/**
 * Project Explorer - File tree with projects and folders
 */
class ProjectExplorer {
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      onDocumentSelect: options.onDocumentSelect || (() => {}),
      onProjectChange: options.onProjectChange || (() => {}),
      ...options
    };

    this.currentProject = null;
    this.projects = [];
    this.projectStructure = { folders: [], documents: [] };

    this.init();
  }

  init() {
    this.render();
    this.loadProjects();
  }

  render() {
    this.container.innerHTML = `
      <div class="project-explorer">
        <div class="project-header">
          <div class="project-selector">
            <select id="projectSelect" class="form-control">
              <option value="">Select a project...</option>
            </select>
            <button id="newProjectBtn" class="btn btn-sm btn-primary" title="New Project">
              <i class="fas fa-plus"></i>
            </button>
          </div>
        </div>
        
        <div class="project-actions" id="projectActions" style="display: none;">
          <button id="newFolderBtn" class="btn btn-sm btn-secondary">
            <i class="fas fa-folder-plus"></i> New Folder
          </button>
          <button id="newDocumentBtn" class="btn btn-sm btn-success">
            <i class="fas fa-file-plus"></i> New Document
          </button>
          <button id="inviteUserBtn" class="btn btn-sm btn-info">
            <i class="fas fa-user-plus"></i> Invite User
          </button>
          <button id="manageMembersBtn" class="btn btn-sm btn-outline-info">
            <i class="fas fa-users"></i> Members
          </button>
        </div>

        <div class="project-tree" id="projectTree">
          <div class="empty-state">
            <i class="fas fa-folder-open"></i>
            <p>Select a project to view its contents</p>
          </div>
        </div>
      </div>
    `;

    this.setupEventListeners();
  }

  setupEventListeners() {
    const projectSelect = document.getElementById('projectSelect');
    const newProjectBtn = document.getElementById('newProjectBtn');
    const newFolderBtn = document.getElementById('newFolderBtn');
    const newDocumentBtn = document.getElementById('newDocumentBtn');
    const inviteUserBtn = document.getElementById('inviteUserBtn');
    const manageMembersBtn = document.getElementById('manageMembersBtn');

    projectSelect.addEventListener('change', (e) => {
      if (e.target.value) {
        this.selectProject(e.target.value);
      }
    });

    newProjectBtn.addEventListener('click', () => {
      this.showNewProjectDialog();
    });

    newFolderBtn.addEventListener('click', () => {
      this.showNewFolderDialog();
    });

    newDocumentBtn.addEventListener('click', () => {
      this.showNewDocumentDialog();
    });

    inviteUserBtn.addEventListener('click', () => {
      if (this.currentProject && window.projectCollaboration) {
        window.projectCollaboration.showInviteUserDialog(this.currentProject.id, this.currentProject.name);
      }
    });

    manageMembersBtn.addEventListener('click', () => {
      if (this.currentProject && window.projectCollaboration) {
        window.projectCollaboration.showProjectMembersDialog(this.currentProject.id, this.currentProject.name);
      }
    });
  }

  async loadProjects() {
    try {
      console.log('Loading projects...');
      const response = await fetch('/api/projects', {
        credentials: 'include'
      });

      console.log('Projects response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Projects loaded:', data);
        this.projects = data.projects;
        this.updateProjectSelector();

        // Auto-select active project if any
        const activeProject = this.projects.find(p => p.isActive);
        if (activeProject) {
          console.log('Auto-selecting active project:', activeProject.name);
          this.selectProject(activeProject.id);
        }
      } else {
        const errorData = await response.text();
        console.error('Failed to load projects:', response.status, errorData);
      }
    } catch (error) {
      console.error('Error loading projects:', error);
    }
  }

  updateProjectSelector() {
    const projectSelect = document.getElementById('projectSelect');
    
    // Clear existing options except the first one
    while (projectSelect.children.length > 1) {
      projectSelect.removeChild(projectSelect.lastChild);
    }

    // Add project options
    this.projects.forEach(project => {
      const option = document.createElement('option');
      option.value = project.id;
      option.textContent = project.name;
      if (project.isActive) {
        option.selected = true;
      }
      projectSelect.appendChild(option);
    });
  }

  async selectProject(projectId) {
    try {
      // Activate the project
      await fetch(`/api/projects/${projectId}/activate`, {
        method: 'PUT',
        credentials: 'include'
      });

      // Load project structure
      const response = await fetch(`/api/projects/${projectId}`, {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        this.currentProject = data.project;
        this.projectStructure = data.structure;
        
        this.renderProjectTree();
        this.showProjectActions();
        
        this.options.onProjectChange(this.currentProject);
      } else {
        console.error('Failed to load project structure');
      }
    } catch (error) {
      console.error('Error selecting project:', error);
    }
  }

  showProjectActions() {
    const projectActions = document.getElementById('projectActions');
    projectActions.style.display = 'block';
  }

  renderProjectTree() {
    const projectTree = document.getElementById('projectTree');
    
    if (!this.currentProject) {
      projectTree.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-folder-open"></i>
          <p>Select a project to view its contents</p>
        </div>
      `;
      return;
    }

    // Build tree structure
    const tree = this.buildTreeStructure();
    
    projectTree.innerHTML = `
      <div class="tree-container">
        <div class="project-title">
          <i class="fas fa-project-diagram"></i>
          <strong>${this.currentProject.name}</strong>
        </div>
        ${this.renderTreeNode(tree)}
      </div>
    `;

    // Add click handlers for documents
    projectTree.querySelectorAll('.document-item').forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        const documentId = item.dataset.documentId;
        const document = this.projectStructure.documents.find(d => d.id === documentId);
        if (document) {
          this.options.onDocumentSelect(document);
        }
      });
    });
  }

  buildTreeStructure() {
    const { folders, documents } = this.projectStructure;
    
    // Create a map of folders by ID for quick lookup
    const folderMap = new Map();
    folders.forEach(folder => {
      folderMap.set(folder.id, { ...folder, children: [], documents: [] });
    });

    // Build folder hierarchy
    const rootFolders = [];
    folders.forEach(folder => {
      const folderNode = folderMap.get(folder.id);
      if (folder.parentId) {
        const parent = folderMap.get(folder.parentId);
        if (parent) {
          parent.children.push(folderNode);
        }
      } else {
        rootFolders.push(folderNode);
      }
    });

    // Add documents to their folders or root
    const rootDocuments = [];
    documents.forEach(document => {
      if (document.folderId) {
        const folder = folderMap.get(document.folderId);
        if (folder) {
          folder.documents.push(document);
        }
      } else {
        rootDocuments.push(document);
      }
    });

    return {
      folders: rootFolders,
      documents: rootDocuments
    };
  }

  renderTreeNode(node, level = 0) {
    const indent = '  '.repeat(level);
    let html = '';

    // Render folders
    if (node.folders) {
      node.folders.forEach(folder => {
        html += `
          <div class="folder-item" style="margin-left: ${level * 20}px;">
            <i class="fas fa-folder"></i>
            <span>${folder.name}</span>
            ${this.renderTreeNode({ folders: folder.children, documents: folder.documents }, level + 1)}
          </div>
        `;
      });
    }

    // Render documents
    if (node.documents) {
      node.documents.forEach(document => {
        html += `
          <div class="document-item" data-document-id="${document.id}" style="margin-left: ${level * 20}px;">
            <i class="fas fa-file-alt"></i>
            <span>${document.name}</span>
            <small class="text-muted">${new Date(document.updatedAt).toLocaleDateString()}</small>
          </div>
        `;
      });
    }

    return html;
  }

  showNewProjectDialog() {
    const name = prompt('Enter project name:');
    if (name) {
      const description = prompt('Enter project description (optional):') || '';
      this.createProject(name, description);
    }
  }

  showNewFolderDialog() {
    if (!this.currentProject) {
      alert('Please select a project first');
      return;
    }

    const name = prompt('Enter folder name:');
    if (name) {
      this.createFolder(name, this.currentProject.id);
    }
  }

  showNewDocumentDialog() {
    if (!this.currentProject) {
      alert('Please select a project first');
      return;
    }

    const name = prompt('Enter document name:');
    if (name) {
      this.createDocument(name, this.currentProject.id);
    }
  }

  async createProject(name, description) {
    try {
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ name, description })
      });

      if (response.ok) {
        await this.loadProjects();
        const data = await response.json();
        this.selectProject(data.project.id);
      } else {
        alert('Failed to create project');
      }
    } catch (error) {
      console.error('Error creating project:', error);
      alert('Error creating project');
    }
  }

  async createFolder(name, projectId, parentId = null) {
    try {
      const response = await fetch('/api/folders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ name, projectId, parentId })
      });

      if (response.ok) {
        // Refresh project structure
        this.selectProject(projectId);
      } else {
        alert('Failed to create folder');
      }
    } catch (error) {
      console.error('Error creating folder:', error);
      alert('Error creating folder');
    }
  }

  async createDocument(name, projectId, folderId = null) {
    try {
      console.log('Creating document:', { name, projectId, folderId });

      const response = await fetch('/api/documents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          name,
          content: `# ${name}\n\nCreated on ${new Date().toLocaleString()}`,
          projectId,
          folderId
        })
      });

      console.log('Document creation response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Document created successfully:', data);
        // Refresh project structure
        await this.selectProject(projectId);
        // Auto-select the new document
        this.options.onDocumentSelect(data.document);
      } else {
        const errorData = await response.text();
        console.error('Document creation failed:', response.status, errorData);
        alert(`Failed to create document: ${errorData}`);
      }
    } catch (error) {
      console.error('Error creating document:', error);
      alert('Error creating document');
    }
  }
}

// Export for use in other modules
window.ProjectExplorer = ProjectExplorer;
