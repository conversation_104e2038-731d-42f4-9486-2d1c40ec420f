// Helper function to get cookie value with localStorage fallback
window.getCookie = function(name) {
  // Try to get from cookie first
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    const cookieValue = parts.pop().split(';').shift();
    if (cookieValue) return cookieValue;
  }

  // Fallback to localStorage
  const localValue = localStorage.getItem(name);
  if (localValue) {
    console.log(`Retrieved ${name} from localStorage:`, localValue);
    return localValue;
  }

  return null;
}

// Initialize Socket.IO with auth
const sessionId = getCookie('sessionId');
console.log('Setting up shared socket.io with sessionId:', sessionId ? 'present' : 'missing');
const socket = io({
  auth: { sessionId }
});

// Export the socket for other modules
window.sharedSocket = socket;
