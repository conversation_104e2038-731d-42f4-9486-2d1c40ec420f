document.addEventListener('DOMContentLoaded', () => {
  // DOM Elements
  const trackChangesSwitch = document.getElementById('trackChangesSwitch');
  const pendingChangesBtn = document.getElementById('pendingChangesBtn');
  const pendingChangesModal = document.getElementById('pending-changes-modal');
  const pendingCloseBtn = document.querySelector('.pending-close');
  const pendingChangesList = document.getElementById('pending-changes-list');

  // Use the shared socket from socket.js
  const socket = window.sharedSocket;
  console.log('TrackChanges: Using shared socket');

  // Current state
  let trackChangesMode = false;
  let currentPendingChanges = [];
  let currentDocumentId = null;

  // Initialize UI
  pendingChangesBtn.style.display = 'none'; // Hide changes button by default

  // Initialize with empty pending changes list
  updatePendingChangesButton();

  // Check if editor module is available
  if (!window.editorModule) {
    console.error('Editor module not available. Track changes functionality may not work properly.');

    // Set up a polling mechanism to wait for the editor module to be available
    const checkEditorModule = setInterval(() => {
      if (window.editorModule) {
        console.log('Editor module now available');
        clearInterval(checkEditorModule);
      }
    }, 500); // Check every 500ms
  } else {
    console.log('Editor module successfully detected');
  }

  // Log initialization
  console.log('Track changes module initialized');

  // Event listeners
  trackChangesSwitch.addEventListener('change', () => {
    trackChangesMode = trackChangesSwitch.checked;
    console.log('Track changes mode:', trackChangesMode ? 'ON' : 'OFF');

    // Update the editor module with the track changes mode
    safelyCallEditorModule('setTrackChangesMode', trackChangesMode);

    // If track changes mode is enabled, request pending changes
    if (trackChangesMode) {
      currentDocumentId = safelyCallEditorModule('getCurrentDocumentId');
      if (currentDocumentId) {
        console.log('Requesting pending changes for document:', currentDocumentId);
        socket.emit('get-pending-changes', { documentId: currentDocumentId });
      }
    } else {
      // If track changes mode is disabled, clear pending changes
      currentPendingChanges = [];
      safelyCallEditorModule('highlightPendingChanges', []);
      updatePendingChangesButton();
    }
  });

  pendingChangesBtn.addEventListener('click', () => {
    currentDocumentId = safelyCallEditorModule('getCurrentDocumentId');
    if (!currentDocumentId) {
      alert('Please select a document first');
      return;
    }

    // Request pending changes for the current document
    socket.emit('get-pending-changes', { documentId: currentDocumentId });
    pendingChangesModal.style.display = 'block';
  });

  pendingCloseBtn.addEventListener('click', () => {
    pendingChangesModal.style.display = 'none';
  });

  window.addEventListener('click', (e) => {
    if (e.target === pendingChangesModal) {
      pendingChangesModal.style.display = 'none';
    }
  });

  // Socket event handlers
  socket.on('init-document', ({ documentId }) => {
    // When joining a document, request pending changes if track changes is enabled
    if (trackChangesMode && documentId) {
      console.log('Joined document, requesting pending changes:', documentId);
      socket.emit('get-pending-changes', { documentId });
    }
  });

  socket.on('pending-changes', ({ changes }) => {
    console.log('Received pending changes:', changes);
    currentPendingChanges = changes || [];
    renderPendingChangesList();

    // Update the editor to highlight pending changes
    if (window.editorModule && typeof window.editorModule.highlightPendingChanges === 'function') {
      window.editorModule.highlightPendingChanges(currentPendingChanges);
    } else {
      console.error('Editor module or highlightPendingChanges function not available');
    }

    // Update the pending changes button to show count
    updatePendingChangesButton();
  });

  socket.on('pending-change', (change) => {
    console.log('Received new pending change:', change);

    // Check if we already have this change (by ID)
    const existingIndex = currentPendingChanges.findIndex(c => c.id === change.id);

    if (existingIndex >= 0) {
      // Update the existing change
      console.log('Updating existing change:', change.id);
      currentPendingChanges[existingIndex] = change;
    } else {
      // Add as a new change
      currentPendingChanges.push(change);
    }

    // Update the editor to highlight pending changes
    safelyCallEditorModule('highlightPendingChanges', currentPendingChanges);

    // Update the pending changes button to show count
    updatePendingChangesButton();

    // If the modal is open, update the list
    if (pendingChangesModal.style.display === 'block') {
      renderPendingChangesList();
    }
  });

  socket.on('change-rejected', ({ changeId }) => {
    console.log('Change rejected:', changeId);

    // Remove from our local list
    currentPendingChanges = currentPendingChanges.filter(change => change.id !== parseInt(changeId));

    // Update the editor to highlight remaining pending changes
    safelyCallEditorModule('highlightPendingChanges', currentPendingChanges);

    // Update the pending changes button to show count
    updatePendingChangesButton();

    // If the modal is open, update the list
    if (pendingChangesModal.style.display === 'block') {
      renderPendingChangesList();
    }
  });

  socket.on('content-updated', (data) => {
    if (data.changeAccepted) {
      console.log('Change accepted:', data.changeId);

      // Remove from our local list
      currentPendingChanges = currentPendingChanges.filter(change => change.id !== parseInt(data.changeId));

      // Update the pending changes button to show count
      updatePendingChangesButton();

      // If the modal is open, update the list
      if (pendingChangesModal.style.display === 'block') {
        renderPendingChangesList();
      }
    }
  });

  // Functions
  function renderPendingChangesList() {
    pendingChangesList.innerHTML = '';

    if (!currentPendingChanges || currentPendingChanges.length === 0) {
      const li = document.createElement('li');
      li.textContent = 'No pending changes';
      pendingChangesList.appendChild(li);
      return;
    }

    currentPendingChanges.forEach(change => {
      const li = document.createElement('li');
      li.className = 'pending-change-item';

      const header = document.createElement('div');
      header.className = 'pending-change-header';

      const userInfo = document.createElement('div');
      userInfo.className = 'pending-change-user';

      const userColor = document.createElement('span');
      userColor.className = 'pending-change-user-color';
      userColor.style.backgroundColor = change.userColor;

      const userName = document.createElement('span');
      userName.textContent = change.userName;

      userInfo.appendChild(userColor);
      userInfo.appendChild(userName);

      const timestamp = document.createElement('span');
      timestamp.className = 'pending-change-time';
      timestamp.textContent = change.createdAt;

      header.appendChild(userInfo);
      header.appendChild(timestamp);

      const content = document.createElement('div');
      content.className = 'pending-change-content';
      content.textContent = change.insertedText || '(deleted text)';

      const actions = document.createElement('div');
      actions.className = 'pending-change-actions';

      const acceptBtn = document.createElement('button');
      acceptBtn.className = 'btn-accept';
      acceptBtn.innerHTML = '<i class="fas fa-check"></i> Accept';
      acceptBtn.addEventListener('click', () => {
        acceptChange(change.id);
      });

      const rejectBtn = document.createElement('button');
      rejectBtn.className = 'btn-reject';
      rejectBtn.innerHTML = '<i class="fas fa-times"></i> Reject';
      rejectBtn.addEventListener('click', () => {
        rejectChange(change.id);
      });

      actions.appendChild(acceptBtn);
      actions.appendChild(rejectBtn);

      li.appendChild(header);
      li.appendChild(content);
      li.appendChild(actions);

      pendingChangesList.appendChild(li);
    });
  }

  function acceptChange(changeId) {
    socket.emit('accept-change', { changeId });
  }

  function rejectChange(changeId) {
    socket.emit('reject-change', { changeId });
  }

  // Helper function to safely call editor module functions
  function safelyCallEditorModule(functionName, ...args) {
    if (window.editorModule && typeof window.editorModule[functionName] === 'function') {
      return window.editorModule[functionName](...args);
    } else {
      console.error(`Editor module function '${functionName}' not available`);
      return null;
    }
  }

  function updatePendingChangesButton() {
    const count = currentPendingChanges.length;
    pendingChangesBtn.innerHTML = count > 0
      ? `<i class="fas fa-tasks"></i> Changes (${count})`
      : `<i class="fas fa-tasks"></i> Changes`;

    // Also update the button visibility based on track changes mode
    pendingChangesBtn.style.display = trackChangesMode ? 'inline-flex' : 'none';

    // Log the current state for debugging
    console.log('Pending changes count:', count, 'Track changes mode:', trackChangesMode);
  }
});
