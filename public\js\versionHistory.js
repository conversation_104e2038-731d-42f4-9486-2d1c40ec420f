document.addEventListener('DOMContentLoaded', () => {
  // DOM Elements
  const versionHistoryBtn = document.getElementById('versionHistoryBtn');
  const versionHistoryModal = document.getElementById('version-history-modal');
  const versionCloseBtn = document.querySelector('.version-close');
  const versionList = document.getElementById('version-list');
  const saveVersionBtn = document.getElementById('saveVersionBtn');
  const versionNameInput = document.getElementById('version-name');

  // Use the shared socket from socket.js
  const socket = window.sharedSocket;
  console.log('VersionHistory: Using shared socket');

  // Current state
  let currentVersions = [];
  let currentDocumentId = null;

  // Event listeners
  versionHistoryBtn.addEventListener('click', () => {
    currentDocumentId = window.editorModule.getCurrentDocumentId();
    if (!currentDocumentId) {
      alert('Please select a document first');
      return;
    }
    
    // Request versions for the current document
    socket.emit('get-document-versions', { documentId: currentDocumentId });
    versionHistoryModal.style.display = 'block';
  });

  versionCloseBtn.addEventListener('click', () => {
    versionHistoryModal.style.display = 'none';
  });

  window.addEventListener('click', (e) => {
    if (e.target === versionHistoryModal) {
      versionHistoryModal.style.display = 'none';
    }
  });

  saveVersionBtn.addEventListener('click', () => {
    const versionName = versionNameInput.value.trim();
    if (!currentDocumentId) {
      alert('Please select a document first');
      return;
    }
    
    socket.emit('save-named-version', { 
      documentId: currentDocumentId, 
      versionName: versionName || `Version ${new Date().toLocaleString()}`
    });
    
    // Clear the input
    versionNameInput.value = '';
  });

  // Socket event handlers
  socket.on('document-versions', ({ versions }) => {
    console.log('Received document versions:', versions);
    currentVersions = versions;
    renderVersionList();
  });

  socket.on('version-restored', (result) => {
    if (result.success) {
      alert('Version restored successfully');
      versionHistoryModal.style.display = 'none';
    } else {
      alert(`Error restoring version: ${result.error}`);
    }
  });

  // Functions
  function renderVersionList() {
    versionList.innerHTML = '';

    if (!currentVersions || currentVersions.length === 0) {
      const li = document.createElement('li');
      li.textContent = 'No version history available';
      versionList.appendChild(li);
      return;
    }

    currentVersions.forEach(version => {
      const li = document.createElement('li');
      li.className = 'version-item';
      
      const versionInfo = document.createElement('div');
      versionInfo.className = 'version-info';
      
      const versionName = document.createElement('span');
      versionName.className = 'version-name';
      versionName.textContent = version.versionName;
      
      const versionMeta = document.createElement('div');
      versionMeta.className = 'version-meta';
      versionMeta.innerHTML = `
        <span class="version-date">${version.createdAt}</span>
        <span class="version-user">by ${version.userName}</span>
      `;
      
      versionInfo.appendChild(versionName);
      versionInfo.appendChild(versionMeta);
      
      const versionActions = document.createElement('div');
      versionActions.className = 'version-actions';
      
      const restoreBtn = document.createElement('button');
      restoreBtn.className = 'btn-restore';
      restoreBtn.innerHTML = '<i class="fas fa-undo"></i> Restore';
      restoreBtn.addEventListener('click', () => {
        if (confirm('Are you sure you want to restore this version? Current changes will be saved as a new version.')) {
          socket.emit('restore-version', { versionId: version.id });
        }
      });
      
      versionActions.appendChild(restoreBtn);
      
      li.appendChild(versionInfo);
      li.appendChild(versionActions);
      
      versionList.appendChild(li);
    });
  }
});
