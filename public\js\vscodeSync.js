document.addEventListener('DOMContentLoaded', () => {
  // DOM Elements
  const vscodeSyncBtn = document.getElementById('vscodeSyncBtn');
  const documentTitle = document.getElementById('document-title');

  // Use the shared socket from socket.js
  const socket = window.sharedSocket;
  console.log('VSCodeSync: Using shared socket');

  // State
  let currentDocumentId = null;
  let isDocumentSynced = false;

  // Event listeners
  vscodeSyncBtn.addEventListener('click', () => {
    currentDocumentId = safelyCallEditorModule('getCurrentDocumentId');

    if (!currentDocumentId) {
      alert('Please select a document first');
      return;
    }

    console.log('Requesting VS Code sync for document:', currentDocumentId);

    // Show loading state
    vscodeSyncBtn.disabled = true;
    vscodeSyncBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Syncing...';

    // Request sync via socket
    socket.emit('vscode-sync', { documentId: currentDocumentId });
  });

  // Socket event handlers
  socket.on('vscode-sync-result', (result) => {
    console.log('VS Code sync result:', result);

    // Reset button state
    vscodeSyncBtn.disabled = false;

    if (result.success) {
      // Update button to show synced state
      vscodeSyncBtn.innerHTML = '<i class="fas fa-link"></i> Synced with VS Code';
      vscodeSyncBtn.classList.add('synced');
      isDocumentSynced = true;

      // Show success message
      alert('Successfully synced to VS Code!');
    } else if (result.isSynced) {
      // The file is synced but VS Code didn't open
      vscodeSyncBtn.innerHTML = '<i class="fas fa-link"></i> Synced with VS Code';
      vscodeSyncBtn.classList.add('synced');
      isDocumentSynced = true;

      // Show partial success message
      alert(`File is synced but couldn't open VS Code: ${result.error || 'Unknown error'}`);
    } else {
      // Reset button state
      vscodeSyncBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Sync to VS Code';
      vscodeSyncBtn.classList.remove('synced');
      isDocumentSynced = false;

      // Show error message
      alert(`Failed to sync to VS Code: ${result.error || 'Unknown error'}`);
    }
  });

  // Handle document synced status updates
  socket.on('document-vscode-synced', ({ documentId, isSynced }) => {
    console.log('Document VS Code sync status updated:', documentId, isSynced);

    // Only update UI if this is the current document
    if (documentId === currentDocumentId) {
      isDocumentSynced = isSynced;

      if (isSynced) {
        vscodeSyncBtn.innerHTML = '<i class="fas fa-link"></i> Synced with VS Code';
        vscodeSyncBtn.classList.add('synced');
      } else {
        vscodeSyncBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Sync to VS Code';
        vscodeSyncBtn.classList.remove('synced');
      }
    }
  });

  // Handle content updates from VS Code
  socket.on('content-updated', (data) => {
    if (data.fromVSCode) {
      console.log('Content updated from VS Code');

      // Show notification
      const notification = document.createElement('div');
      notification.className = 'vscode-update-notification';
      notification.innerHTML = '<i class="fas fa-info-circle"></i> Document updated from VS Code';

      // Add to document body
      document.body.appendChild(notification);

      // Remove after 3 seconds
      setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 500);
      }, 3000);
    }
  });

  // Handle document selection
  socket.on('init-document', ({ documentId }) => {
    console.log('Document selected:', documentId);
    currentDocumentId = documentId;

    // Reset sync status for new document
    isDocumentSynced = false;
    vscodeSyncBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Sync to VS Code';
    vscodeSyncBtn.classList.remove('synced');
  });

  // Helper function to safely call editor module functions
  function safelyCallEditorModule(functionName, args = []) {
    if (window.editorModule && typeof window.editorModule[functionName] === 'function') {
      return window.editorModule[functionName](...args);
    }
    console.warn(`Editor module function ${functionName} not available`);
    return null;
  }
});
