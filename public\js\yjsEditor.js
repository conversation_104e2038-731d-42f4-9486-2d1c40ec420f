import * as Y from 'yjs';
import { yCollab } from 'y-codemirror.next';
import { WebsocketProvider } from 'y-websocket';
import { EditorState } from '@codemirror/state';
import { EditorView, keymap, highlightActiveLine } from '@codemirror/view';
import { defaultKeymap } from '@codemirror/commands';
import { markdown } from '@codemirror/lang-markdown';
import { syntaxHighlighting, defaultHighlightStyle, indentOnInput } from '@codemirror/language';
import { history, historyKeymap } from '@codemirror/commands';
import { bracketMatching } from '@codemirror/language';
import { closeBrackets, closeBracketsKeymap } from '@codemirror/autocomplete';
import { lineNumbers, highlightActiveLineGutter } from '@codemirror/view';

/**
 * CRDT-based collaborative editor using Yjs
 */
class YjsCollaborativeEditor {
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      serverUrl: options.serverUrl || 'ws://localhost:3000',
      documentId: options.documentId,
      userId: options.userId,
      userName: options.userName,
      userColor: options.userColor || '#007acc',
      ...options
    };

    // Yjs document and provider
    this.ydoc = null;
    this.ytext = null;
    this.provider = null;
    this.editorView = null;
    this.awareness = null;

    // Callbacks
    this.onContentChange = options.onContentChange || (() => {});
    this.onUserJoin = options.onUserJoin || (() => {});
    this.onUserLeave = options.onUserLeave || (() => {});
    this.onConnectionChange = options.onConnectionChange || (() => {});

    this.isInitialized = false;
  }

  /**
   * Initialize the collaborative editor
   */
  async initialize(initialContent = '') {
    try {
      console.log('Initializing Yjs collaborative editor');

      // Create Yjs document
      this.ydoc = new Y.Doc();
      this.ytext = this.ydoc.getText('content');

      // Set up WebSocket provider for real-time collaboration
      this.provider = new WebsocketProvider(
        this.options.serverUrl.replace('http://', 'ws://').replace('https://', 'wss://'),
        `document-${this.options.documentId}`,
        this.ydoc
      );

      // Set up awareness (user presence)
      this.awareness = this.provider.awareness;
      this.awareness.setLocalStateField('user', {
        name: this.options.userName,
        color: this.options.userColor,
        userId: this.options.userId
      });

      // Set up event listeners
      this.setupEventListeners();

      // Initialize CodeMirror with Yjs collaboration
      this.initializeEditor(initialContent);

      // Wait for initial sync
      await this.waitForSync();

      this.isInitialized = true;
      console.log('Yjs collaborative editor initialized successfully');

      return true;
    } catch (error) {
      console.error('Failed to initialize Yjs collaborative editor:', error);
      return false;
    }
  }

  /**
   * Initialize CodeMirror editor with Yjs collaboration
   */
  initializeEditor(initialContent) {
    // Set initial content if the document is empty
    if (this.ytext.length === 0 && initialContent) {
      this.ytext.insert(0, initialContent);
    }

    // Create CodeMirror extensions
    const extensions = [
      lineNumbers(),
      highlightActiveLineGutter(),
      highlightActiveLine(),
      history(),
      indentOnInput(),
      bracketMatching(),
      closeBrackets(),
      syntaxHighlighting(defaultHighlightStyle, { fallback: true }),
      markdown(),
      keymap.of([
        ...closeBracketsKeymap,
        ...defaultKeymap,
        ...historyKeymap
      ]),
      // Yjs collaboration extension
      yCollab(this.ytext, this.awareness, {
        undoManager: new Y.UndoManager(this.ytext)
      }),
      // Content change listener
      EditorView.updateListener.of((update) => {
        if (update.docChanged) {
          this.onContentChange(this.getContent());
        }
      })
    ];

    // Create editor state
    const state = EditorState.create({
      doc: this.ytext.toString(),
      extensions
    });

    // Create editor view
    this.editorView = new EditorView({
      state,
      parent: this.container
    });
  }

  /**
   * Set up event listeners for collaboration
   */
  setupEventListeners() {
    // Connection status
    this.provider.on('status', (event) => {
      console.log('Yjs provider status:', event.status);
      this.onConnectionChange(event.status === 'connected');
    });

    // User awareness changes
    this.awareness.on('change', (changes) => {
      // Handle user joins/leaves
      changes.added.forEach(clientId => {
        const user = this.awareness.getStates().get(clientId)?.user;
        if (user && clientId !== this.awareness.clientID) {
          console.log('User joined:', user);
          this.onUserJoin(user);
        }
      });

      changes.removed.forEach(clientId => {
        console.log('User left:', clientId);
        this.onUserLeave({ userId: clientId });
      });
    });

    // Document updates
    this.ytext.observe((event) => {
      console.log('Document updated via Yjs:', event);
    });
  }

  /**
   * Wait for initial synchronization
   */
  waitForSync(timeout = 5000) {
    return new Promise((resolve, reject) => {
      if (this.provider.wsconnected) {
        resolve();
        return;
      }

      const timeoutId = setTimeout(() => {
        reject(new Error('Sync timeout'));
      }, timeout);

      this.provider.once('sync', () => {
        clearTimeout(timeoutId);
        resolve();
      });
    });
  }

  /**
   * Get current document content
   */
  getContent() {
    return this.ytext.toString();
  }

  /**
   * Set document content (use carefully - this will overwrite everything)
   */
  setContent(content) {
    if (!this.isInitialized) {
      console.warn('Editor not initialized');
      return;
    }

    // Clear existing content and insert new content
    this.ytext.delete(0, this.ytext.length);
    this.ytext.insert(0, content);
  }

  /**
   * Get connected users
   */
  getConnectedUsers() {
    if (!this.awareness) return [];

    const users = [];
    this.awareness.getStates().forEach((state, clientId) => {
      if (state.user && clientId !== this.awareness.clientID) {
        users.push({
          ...state.user,
          clientId
        });
      }
    });

    return users;
  }

  /**
   * Focus the editor
   */
  focus() {
    if (this.editorView) {
      this.editorView.focus();
    }
  }

  /**
   * Get editor statistics
   */
  getStats() {
    return {
      isConnected: this.provider?.wsconnected || false,
      connectedUsers: this.getConnectedUsers().length,
      documentLength: this.ytext?.length || 0,
      isInitialized: this.isInitialized
    };
  }

  /**
   * Destroy the editor and clean up resources
   */
  destroy() {
    console.log('Destroying Yjs collaborative editor');

    if (this.editorView) {
      this.editorView.destroy();
      this.editorView = null;
    }

    if (this.provider) {
      this.provider.destroy();
      this.provider = null;
    }

    if (this.ydoc) {
      this.ydoc.destroy();
      this.ydoc = null;
    }

    this.ytext = null;
    this.awareness = null;
    this.isInitialized = false;
  }
}

export { YjsCollaborativeEditor };
