<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Collaborative Quarto Editor (CRDT)</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .yjs-editor-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .yjs-toolbar {
            background: #f5f5f5;
            padding: 10px;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .yjs-status {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .yjs-status.connected {
            color: #28a745;
        }
        
        .yjs-status.disconnected {
            color: #dc3545;
        }
        
        .yjs-users {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .yjs-user {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 2px 8px;
            border-radius: 12px;
            background: rgba(0, 0, 0, 0.1);
            font-size: 12px;
        }
        
        .yjs-user-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .yjs-editor {
            flex: 1;
            overflow: auto;
        }
        
        .yjs-stats {
            font-size: 12px;
            color: #666;
        }
        
        .yjs-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-yjs {
            padding: 5px 10px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn-yjs:hover {
            background: #f0f0f0;
        }
        
        .btn-yjs.primary {
            background: #007acc;
            color: white;
            border-color: #007acc;
        }
        
        .btn-yjs.primary:hover {
            background: #005a9e;
        }
    </style>
</head>
<body>
    <div class="yjs-editor-container">
        <div class="yjs-toolbar">
            <div class="yjs-status disconnected" id="connectionStatus">
                <i class="fas fa-circle"></i>
                <span>Disconnected</span>
            </div>
            
            <div class="yjs-stats" id="statsDisplay">
                Document: 0 chars | Users: 0
            </div>
            
            <div class="yjs-users" id="usersList">
                <!-- Connected users will appear here -->
            </div>
            
            <div class="yjs-actions">
                <button class="btn-yjs" id="saveBtn">
                    <i class="fas fa-save"></i> Save
                </button>
                <button class="btn-yjs" id="renderBtn">
                    <i class="fas fa-eye"></i> Preview
                </button>
                <button class="btn-yjs primary" id="backBtn">
                    <i class="fas fa-arrow-left"></i> Back to Documents
                </button>
            </div>
        </div>
        
        <div class="yjs-editor" id="editor">
            <!-- CodeMirror editor will be initialized here -->
        </div>
    </div>

    <!-- Scripts -->
    <script src="/socket.io/socket.io.js"></script>
    <script type="module">
        import { YjsCollaborativeEditor } from './js/yjsEditor.js';
        
        // Get document ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const documentId = urlParams.get('doc');
        
        if (!documentId) {
            alert('No document ID specified');
            window.location.href = '/';
        }
        
        // Get user info from session
        const sessionId = getCookie('sessionId');
        if (!sessionId) {
            alert('Please log in first');
            window.location.href = '/login.html';
        }
        
        // Initialize the collaborative editor
        let editor = null;
        let currentUser = null;
        
        async function initializeEditor() {
            try {
                // Get user info
                const response = await fetch('/api/user/profile', {
                    credentials: 'include'
                });
                
                if (!response.ok) {
                    throw new Error('Failed to get user profile');
                }
                
                currentUser = await response.json();
                
                // Initialize Yjs collaborative editor
                editor = new YjsCollaborativeEditor(
                    document.getElementById('editor'),
                    {
                        serverUrl: window.location.origin,
                        documentId: documentId,
                        userId: currentUser.id,
                        userName: currentUser.displayName,
                        userColor: generateUserColor(currentUser.id),
                        onContentChange: handleContentChange,
                        onUserJoin: handleUserJoin,
                        onUserLeave: handleUserLeave,
                        onConnectionChange: handleConnectionChange
                    }
                );
                
                // Get initial document content
                const docResponse = await fetch(`/api/documents/${documentId}`, {
                    credentials: 'include'
                });
                
                let initialContent = '';
                if (docResponse.ok) {
                    const docData = await docResponse.json();
                    initialContent = docData.document.content || '';
                }
                
                // Initialize the editor
                const success = await editor.initialize(initialContent);
                if (!success) {
                    throw new Error('Failed to initialize collaborative editor');
                }
                
                console.log('Yjs collaborative editor initialized successfully');
                
                // Update UI
                updateStats();
                
                // Set up periodic stats updates
                setInterval(updateStats, 2000);
                
            } catch (error) {
                console.error('Error initializing editor:', error);
                alert('Failed to initialize collaborative editor: ' + error.message);
            }
        }
        
        // Event handlers
        function handleContentChange(content) {
            updateStats();
        }
        
        function handleUserJoin(user) {
            console.log('User joined:', user);
            updateUsersList();
        }
        
        function handleUserLeave(user) {
            console.log('User left:', user);
            updateUsersList();
        }
        
        function handleConnectionChange(isConnected) {
            const statusEl = document.getElementById('connectionStatus');
            if (isConnected) {
                statusEl.className = 'yjs-status connected';
                statusEl.innerHTML = '<i class="fas fa-circle"></i> <span>Connected</span>';
            } else {
                statusEl.className = 'yjs-status disconnected';
                statusEl.innerHTML = '<i class="fas fa-circle"></i> <span>Disconnected</span>';
            }
        }
        
        function updateStats() {
            if (!editor) return;
            
            const stats = editor.getStats();
            const statsEl = document.getElementById('statsDisplay');
            statsEl.textContent = `Document: ${stats.documentLength} chars | Users: ${stats.connectedUsers}`;
        }
        
        function updateUsersList() {
            if (!editor) return;
            
            const users = editor.getConnectedUsers();
            const usersEl = document.getElementById('usersList');
            
            usersEl.innerHTML = users.map(user => `
                <div class="yjs-user">
                    <div class="yjs-user-color" style="background-color: ${user.color}"></div>
                    <span>${user.name}</span>
                </div>
            `).join('');
        }
        
        function generateUserColor(userId) {
            // Generate a consistent color for each user
            const colors = [
                '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
                '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
            ];
            const hash = userId.split('').reduce((a, b) => {
                a = ((a << 5) - a) + b.charCodeAt(0);
                return a & a;
            }, 0);
            return colors[Math.abs(hash) % colors.length];
        }
        
        // Helper function to get cookie value
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }
        
        // Button event handlers
        document.getElementById('saveBtn').addEventListener('click', async () => {
            if (!editor) return;
            
            try {
                const content = editor.getContent();
                const response = await fetch(`/api/documents/${documentId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({ content })
                });
                
                if (response.ok) {
                    alert('Document saved successfully');
                } else {
                    throw new Error('Failed to save document');
                }
            } catch (error) {
                console.error('Error saving document:', error);
                alert('Failed to save document: ' + error.message);
            }
        });
        
        document.getElementById('renderBtn').addEventListener('click', async () => {
            if (!editor) return;
            
            try {
                const content = editor.getContent();
                const response = await fetch(`/api/documents/${documentId}/render`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({ content })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    // Open preview in new window
                    const previewWindow = window.open('', '_blank');
                    previewWindow.document.write(data.html);
                    previewWindow.document.close();
                } else {
                    throw new Error('Failed to render document');
                }
            } catch (error) {
                console.error('Error rendering document:', error);
                alert('Failed to render document: ' + error.message);
            }
        });
        
        document.getElementById('backBtn').addEventListener('click', () => {
            window.location.href = '/';
        });
        
        // Initialize when page loads
        initializeEditor();
        
        // Clean up when page unloads
        window.addEventListener('beforeunload', () => {
            if (editor) {
                editor.destroy();
            }
        });
    </script>
</body>
</html>
