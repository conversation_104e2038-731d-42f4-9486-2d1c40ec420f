const express = require('express');
const http = require('http');
const socketIO = require('socket.io');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const cookieParser = require('cookie-parser');

// Import SQLite models
const { DocumentManager } = require('./models/documentSqlite');
const { UserManager } = require('./models/userSqlite');
const { VSCodeSyncManager } = require('./models/vscodeSync');
const { YjsServer } = require('./models/yjsServer');
const { ProjectManager } = require('./models/projectManager');

const app = express();
const server = http.createServer(app);
const io = socketIO(server);

// Middleware
app.use(express.static('public'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

// Initialize managers
const documentManager = new DocumentManager();
const userManager = new UserManager();
const projectManager = new ProjectManager();
const vscodeManager = new VSCodeSyncManager(documentManager, io);
const yjsServer = new YjsServer(server, documentManager);

// Create a default user if none exists
(async () => {
  const users = await userManager.getAllUsers();
  if (users.length === 0) {
    await userManager.createUser('demo', 'demo', 'Demo User');
  }
})();

// Track active connections
const activeConnections = new Map();

// Helper function to convert user IDs to full user objects
async function getUsersFromIds(userIds) {
  console.log('Converting user IDs to user objects:', userIds);
  const users = [];

  for (const id of userIds) {
    const user = await userManager.getUserById(id);
    console.log(
      `User ${id}:`,
      user ? `${user.displayName} (${user.color})` : 'not found'
    );
    if (user) {
      users.push({
        id: user.id,
        displayName: user.displayName,
        color: user.color,
      });
    }
  }

  console.log('Final user objects:', users);
  return users;
}

// Authentication middleware
const authenticate = async (req, res, next) => {
  const sessionId = req.cookies.sessionId;
  if (sessionId) {
    const user = await userManager.validateSession(sessionId);
    if (user) {
      req.user = user;
      return next();
    }
  }

  // Allow access to authentication endpoints
  if (req.path === '/api/login' || req.path === '/api/register') {
    return next();
  }

  // Block other API routes if not authenticated
  if (req.path.startsWith('/api/')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // For non-API routes, redirect to login page
  if (
    req.path !== '/login.html' &&
    req.path !== '/register.html' &&
    !req.path.startsWith('/css/') &&
    !req.path.startsWith('/js/auth')
  ) {
    return res.redirect('/login.html');
  }

  next();
};

// Apply authentication middleware
app.use(authenticate);

// Socket.IO connection
io.on('connection', (socket) => {
  console.log('New client connected');
  let currentUser = null;
  let currentDocumentId = null;

  // Check for authentication in the handshake
  if (socket.handshake.auth && socket.handshake.auth.sessionId) {
    const sessionId = socket.handshake.auth.sessionId;
    console.log('Socket connected with sessionId in handshake:', sessionId);

    (async () => {
      const user = await userManager.validateSession(sessionId);
      if (user) {
        currentUser = user;
        console.log(
          'Socket authenticated on connection for user:',
          user.displayName
        );
        socket.emit('authentication-success', { user });

        // Store connection
        activeConnections.set(socket.id, { user, socket });
        console.log('Active connections:', activeConnections.size);

        // Send list of documents
        const documents = await documentManager.getAllDocuments();
        console.log('Sending document list:', documents.length, 'documents');
        socket.emit('document-list', documents);
      }
    })();
  }

  // Authenticate socket connection
  socket.on('authenticate', ({ sessionId }) => {
    console.log(
      'Socket authentication attempt with sessionId:',
      sessionId ? 'present' : 'missing'
    );

    (async () => {
      const user = await userManager.validateSession(sessionId);
      if (user) {
        currentUser = user;
        console.log('Socket authenticated for user:', user.displayName);
        socket.emit('authentication-success', { user });

        // Store connection
        activeConnections.set(socket.id, { user, socket });
        console.log('Active connections:', activeConnections.size);

        // Send list of documents
        const documents = await documentManager.getAllDocuments();
        console.log('Sending document list:', documents.length, 'documents');
        socket.emit('document-list', documents);
      } else {
        console.log('Socket authentication failed');
        socket.emit('authentication-failed');
      }
    })();
  });

  // Join a document
  socket.on('join-document', ({ documentId }) => {
    console.log('Join document request:', documentId);
    if (!currentUser) {
      console.log('Cannot join document: User not authenticated');
      return;
    }

    (async () => {
      // Leave current document if any
      if (currentDocumentId) {
        console.log('Leaving current document:', currentDocumentId);
        socket.leave(`document:${currentDocumentId}`);
        const activeUserIds = await documentManager.removeUserFromDocument(
          currentDocumentId,
          currentUser.id
        );
        console.log('Active user IDs after leaving:', activeUserIds);
        const activeUsers = await getUsersFromIds(activeUserIds);
        io.to(`document:${currentDocumentId}`).emit(
          'active-users-updated',
          activeUsers
        );
      }

      // Join new document
      currentDocumentId = documentId;
      const document = await documentManager.getDocument(documentId);

      if (document) {
        console.log(
          'Joining document:',
          document.name,
          '(ID:',
          documentId,
          ')'
        );
        socket.join(`document:${documentId}`);

        // Add user to document and notify others
        const activeUserIds = await documentManager.addUserToDocument(
          documentId,
          currentUser.id
        );
        console.log('Active user IDs after joining:', activeUserIds);

        // Send document content to the client
        console.log(
          'Sending document content to client, length:',
          document.content.length
        );
        socket.emit('init-document', {
          content: document.content,
          documentId,
          documentName: document.name,
        });

        // Send pending changes for this document
        const pendingChanges = await documentManager.getPendingChanges(
          documentId
        );
        if (pendingChanges.length > 0) {
          console.log(
            `Sending ${pendingChanges.length} pending changes to client`
          );
          socket.emit('pending-changes', { changes: pendingChanges });
        }

        // Notify all clients in the document about the new user
        console.log('Broadcasting active users update');
        const activeUsers = await getUsersFromIds(activeUserIds);
        io.to(`document:${documentId}`).emit(
          'active-users-updated',
          activeUsers
        );

        // Send user info for cursor tracking
        console.log('Broadcasting user joined event');
        socket.broadcast.to(`document:${documentId}`).emit('user-joined', {
          userId: currentUser.id,
          username: currentUser.displayName,
          color: currentUser.color,
        });

        // Request cursor positions from all clients in the room
        console.log('Requesting cursor positions from all clients');
        socket.broadcast
          .to(`document:${documentId}`)
          .emit('request-cursor-position');
      } else {
        console.log('Document not found:', documentId);
        socket.emit('error', { message: 'Document not found' });
      }
    })();
  });

  // Handle cursor position updates
  socket.on('cursor-update', ({ documentId, position }) => {
    if (!currentUser) {
      console.log('Cursor update received but no current user');
      return;
    }

    // Use the provided documentId or fall back to the current one
    const docId = documentId || currentDocumentId;
    if (!docId) {
      console.log('Cursor update received but no document ID');
      return;
    }

    // Validate position data
    if (
      !position ||
      typeof position.line !== 'number' ||
      typeof position.character !== 'number'
    ) {
      console.log('Invalid cursor position data:', position);
      return;
    }

    console.log(
      `Cursor update from ${currentUser.displayName} at position ${position.line}:${position.character} in document ${docId}`
    );

    // Create cursor data
    const cursorData = {
      userId: currentUser.id,
      username: currentUser.displayName,
      color: currentUser.color,
      position,
    };

    // Get list of clients in the document room for debugging
    const roomClients = io.sockets.adapter.rooms.get(`document:${docId}`);
    const clientCount = roomClients ? roomClients.size : 0;
    console.log(
      `Broadcasting cursor to ${clientCount} clients in room document:${docId}`
    );

    // Broadcast to all clients in the document room except sender
    socket.broadcast.to(`document:${docId}`).emit('remote-cursor', cursorData);

    // Also broadcast to the sender for debugging
    socket.emit('cursor-sent', {
      success: true,
      cursorData,
      documentId: docId,
      roomClientCount: clientCount,
    });
  });

  // Handle debug cursor events
  socket.on('debug-cursor', (data) => {
    if (!currentUser) return;

    console.log('Debug cursor event:', {
      user: currentUser.displayName,
      socketId: socket.id,
      data,
    });

    // Echo back to the sender
    socket.emit('debug-cursor-response', {
      received: true,
      timestamp: new Date().toISOString(),
    });
  });

  // Handle content updates
  socket.on(
    'update-content',
    ({ documentId, content, changes, trackChangesMode }) => {
      if (!currentUser) return;

      console.log(
        `Content update from ${currentUser.displayName}, track changes mode: ${
          trackChangesMode ? 'ON' : 'OFF'
        }`
      );

      (async () => {
        const document = await documentManager.getDocument(documentId);
        if (document) {
          if (trackChangesMode === true) {
            // In track changes mode, store changes as pending instead of updating content directly
            console.log(`Processing ${changes.length} changes from client`);

            // Process each change
            for (const change of changes) {
              // Skip empty changes
              if (change.fromA === change.toA && !change.inserted) {
                console.log('Skipping empty change');
                continue;
              }

              const changeId = await documentManager.addPendingChange(
                documentId,
                currentUser.id,
                change.fromA,
                change.toA,
                change.inserted
              );

              if (changeId) {
                // Create the change object
                const pendingChange = {
                  id: changeId,
                  documentId,
                  userId: currentUser.id,
                  userName: currentUser.displayName,
                  userColor: currentUser.color,
                  fromPos: change.fromA,
                  toPos: change.toA,
                  insertedText: change.inserted,
                  createdAt: new Date().toLocaleString(),
                };

                // Get the latest version of the change (in case it was merged)
                const updatedChange = await documentManager.getPendingChange(
                  changeId
                );
                if (updatedChange) {
                  // Broadcast the pending change to all clients including sender
                  io.to(`document:${documentId}`).emit(
                    'pending-change',
                    updatedChange
                  );
                } else {
                  // Fallback to the original change if we couldn't get the updated one
                  io.to(`document:${documentId}`).emit(
                    'pending-change',
                    pendingChange
                  );
                }
              }
            }

            // Log pending changes for debugging
            console.log(
              `Pending changes added to document ${documentId} by ${currentUser.displayName}`
            );
          } else {
            // Normal mode - update document content directly
            await documentManager.updateDocument(
              documentId,
              content,
              currentUser.id
            );

            // Update VS Code file if it's synced
            await vscodeManager.updateVSCodeFile(documentId, content);

            // Check if this is an incremental update
            const isIncrementalUpdate = changes && changes.length > 0;

            // Broadcast changes to other clients
            socket.broadcast
              .to(`document:${documentId}`)
              .emit('content-updated', {
                content,
                changes,
                userId: currentUser.id,
                isIncrementalUpdate,
              });

            // Log content update for debugging
            console.log(
              `Document ${documentId} updated by ${currentUser.displayName}`
            );
          }
        } else {
          console.error(`Document ${documentId} not found for update`);
        }
      })();
    }
  );

  // Handle version history requests
  socket.on('get-document-versions', ({ documentId }) => {
    if (!currentUser) return;

    (async () => {
      const versions = await documentManager.getDocumentVersions(documentId);
      socket.emit('document-versions', { versions });
    })();
  });

  // Handle document list request (for VS Code extension)
  socket.on('get-documents', () => {
    if (!currentUser) {
      console.log('Document list requested but user not authenticated');
      // Send empty list for unauthenticated users
      socket.emit('document-list', []);
      return;
    }

    (async () => {
      const documents = await documentManager.getAllDocuments();
      console.log(`Sending ${documents.length} documents to socket client`);
      socket.emit('document-list', documents);
    })();
  });

  // Handle public document list request (no auth required)
  socket.on('get-public-documents', () => {
    console.log('Public document list requested via socket');
    (async () => {
      try {
        const documents = await documentManager.getAllDocuments();
        console.log(
          `Sending ${documents.length} documents to socket client (public request)`
        );
        socket.emit('document-list', documents);
      } catch (error) {
        console.error('Error getting public document list via socket:', error);
        socket.emit('document-list', []);
      }
    })();
  });

  // Handle version restoration
  socket.on('restore-version', ({ versionId }) => {
    if (!currentUser) return;

    (async () => {
      const success = await documentManager.restoreDocumentVersion(
        versionId,
        currentUser.id
      );

      if (success) {
        // Get the version to find out which document it belongs to
        const version = await documentManager.getDocumentVersion(versionId);
        if (version) {
          // Get the updated document content
          const document = await documentManager.getDocument(
            version.documentId
          );

          // Update VS Code file if it's synced
          await vscodeManager.updateVSCodeFile(
            version.documentId,
            document.content
          );

          // Notify all clients in the document about the content update
          io.to(`document:${version.documentId}`).emit('content-updated', {
            content: document.content,
            userId: currentUser.id,
          });

          socket.emit('version-restored', { success: true });
        }
      } else {
        socket.emit('version-restored', {
          success: false,
          error: 'Version could not be restored',
        });
      }
    })();
  });

  // Handle saving named versions
  socket.on('save-named-version', ({ documentId, versionName }) => {
    if (!currentUser) return;

    (async () => {
      const success = await documentManager.saveNamedVersion(
        documentId,
        versionName,
        currentUser.id
      );

      if (success) {
        // Get updated versions list
        const versions = await documentManager.getDocumentVersions(documentId);
        socket.emit('document-versions', { versions });
      } else {
        socket.emit('error', { message: 'Could not save version' });
      }
    })();
  });

  // Handle getting pending changes
  socket.on('get-pending-changes', ({ documentId }) => {
    if (!currentUser) return;

    console.log(
      `Getting pending changes for document ${documentId} requested by ${currentUser.displayName}`
    );

    (async () => {
      const changes = await documentManager.getPendingChanges(documentId);
      console.log(
        `Found ${changes.length} pending changes for document ${documentId}`
      );
      socket.emit('pending-changes', { changes });
    })();
  });

  // Handle accepting changes
  socket.on('accept-change', ({ changeId }) => {
    if (!currentUser) return;

    (async () => {
      const result = await documentManager.acceptChange(
        changeId,
        currentUser.id
      );

      if (result.success) {
        // Update VS Code file if it's synced
        await vscodeManager.updateVSCodeFile(
          result.documentId,
          result.newContent
        );

        // Notify all clients in the document about the content update
        io.to(`document:${result.documentId}`).emit('content-updated', {
          content: result.newContent,
          userId: currentUser.id,
          changeAccepted: true,
          changeId,
        });

        // Send updated pending changes list
        const changes = await documentManager.getPendingChanges(
          result.documentId
        );
        io.to(`document:${result.documentId}`).emit('pending-changes', {
          changes,
        });
      } else {
        socket.emit('error', {
          message: result.error || 'Could not accept change',
        });
      }
    })();
  });

  // Handle rejecting changes
  socket.on('reject-change', ({ changeId }) => {
    if (!currentUser) return;

    (async () => {
      const result = await documentManager.rejectChange(changeId);

      if (result.success && result.documentId) {
        // Notify all clients in the document about the rejected change
        io.to(`document:${result.documentId}`).emit('change-rejected', {
          changeId,
          userId: currentUser.id,
        });

        // Send updated pending changes list
        const changes = await documentManager.getPendingChanges(
          result.documentId
        );
        io.to(`document:${result.documentId}`).emit('pending-changes', {
          changes,
        });
      } else {
        socket.emit('error', {
          message: result.error || 'Could not reject change',
        });
      }
    })();
  });

  // Handle getting document users
  socket.on('get-document-users', ({ documentId }) => {
    if (!currentUser) {
      console.log('Cannot get document users: User not authenticated');
      return;
    }

    const docId = documentId || currentDocumentId;
    if (!docId) {
      console.log('Cannot get document users: No document ID provided');
      return;
    }

    console.log(
      `Getting document users for document ${docId} requested by ${currentUser.displayName}`
    );

    (async () => {
      try {
        const activeUserIds = await documentManager.getActiveUsers(docId);
        console.log('Active user IDs for document:', activeUserIds);
        const activeUsers = await getUsersFromIds(activeUserIds);
        console.log('Sending document users:', activeUsers);
        socket.emit('document-users', { users: activeUsers });
      } catch (error) {
        console.error('Error getting document users:', error);
        socket.emit('document-users', { users: [] });
      }
    })();
  });

  // Handle VS Code file mapping registration
  socket.on('register-vscode-file', ({ documentId, filePath }) => {
    try {
      console.log(
        `Registering VS Code file mapping: ${documentId} -> ${filePath}`
      );
      vscodeManager.registerFileMapping(documentId, filePath);
      console.log(`VS Code file mapping registered successfully`);
    } catch (error) {
      console.error('Error registering VS Code file mapping:', error);
    }
  });

  // Handle VS Code sync request
  socket.on('vscode-sync', ({ documentId }) => {
    console.log('VS Code sync request for document:', documentId);
    if (!currentUser) {
      console.log('Cannot sync to VS Code: User not authenticated');
      socket.emit('vscode-sync-result', {
        success: false,
        error: 'Not authenticated',
      });
      return;
    }

    (async () => {
      try {
        // Get the document content
        const document = await documentManager.getDocument(documentId);
        if (!document) {
          console.log('Document not found for VS Code sync:', documentId);
          socket.emit('vscode-sync-result', {
            success: false,
            error: 'Document not found',
          });
          return;
        }

        // Sync the document to VS Code using the manager
        const syncResult = await vscodeManager.syncToVSCode(
          documentId,
          document
        );

        if (syncResult.success) {
          // Try to open the file in VS Code
          try {
            // Try to use the 'code' command to open the file
            const vscode = spawn('code', [syncResult.filePath]);

            let errorOutput = '';
            vscode.stderr.on('data', (data) => {
              errorOutput += data.toString();
            });

            vscode.on('error', (err) => {
              console.error('Error opening VS Code:', err);
              socket.emit('vscode-sync-result', {
                success: false,
                error:
                  'Failed to open VS Code. Make sure VS Code is installed and the "code" command is in your PATH.',
                filePath: syncResult.filePath,
                isSynced: true, // The file is synced even if VS Code didn't open
              });
            });

            vscode.on('close', (code) => {
              if (code === 0) {
                console.log(
                  'Successfully opened file in VS Code:',
                  syncResult.filePath
                );
                socket.emit('vscode-sync-result', {
                  success: true,
                  filePath: syncResult.filePath,
                  isSynced: true,
                });

                // Notify all clients that this document is synced with VS Code
                io.to(`document:${documentId}`).emit('document-vscode-synced', {
                  documentId,
                  isSynced: true,
                });
              } else {
                console.error('VS Code process exited with code:', code);
                socket.emit('vscode-sync-result', {
                  success: false,
                  error: `VS Code process exited with code ${code}. Error: ${errorOutput}`,
                  filePath: syncResult.filePath,
                  isSynced: true, // The file is synced even if VS Code didn't open
                });
              }
            });
          } catch (error) {
            console.error('Error launching VS Code:', error);
            socket.emit('vscode-sync-result', {
              success: false,
              error: error.message,
              filePath: syncResult.filePath,
              isSynced: true, // The file is synced even if VS Code didn't open
            });
          }
        } else {
          socket.emit('vscode-sync-result', {
            success: false,
            error: syncResult.error || 'Unknown error syncing to VS Code',
          });
        }
      } catch (error) {
        console.error('Error in VS Code sync:', error);
        socket.emit('vscode-sync-result', {
          success: false,
          error: error.message,
        });
      }
    })();
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log('Client disconnected');

    // Remove from active connections
    activeConnections.delete(socket.id);

    // Remove from current document if any
    if (currentUser && currentDocumentId) {
      (async () => {
        const activeUserIds = await documentManager.removeUserFromDocument(
          currentDocumentId,
          currentUser.id
        );
        const activeUsers = await getUsersFromIds(activeUserIds);
        io.to(`document:${currentDocumentId}`).emit(
          'active-users-updated',
          activeUsers
        );
        io.to(`document:${currentDocumentId}`).emit('user-left', {
          userId: currentUser.id,
        });
      })();
    }
  });
});

// API Routes

// Debug endpoint to check if documents exist
app.get('/api/debug/documents', (req, res) => {
  (async () => {
    try {
      console.log('Debug document list requested');
      const documents = await documentManager.getAllDocuments();
      console.log(`Debug: Found ${documents.length} documents`);
      res.send(
        `<h1>Documents in database: ${
          documents.length
        }</h1><pre>${JSON.stringify(documents, null, 2)}</pre>`
      );
    } catch (error) {
      console.error('Error getting debug document list:', error);
      res.status(500).send(`<h1>Error</h1><pre>${error.stack}</pre>`);
    }
  })();
});

// Create a document (for VS Code extension) - no auth required
app.post('/api/documents/create', (req, res) => {
  (async () => {
    try {
      const { name, content } = req.body;
      console.log(`Creating document from VS Code: ${name}`);

      if (!name) {
        return res.status(400).json({ error: 'Document name is required' });
      }

      const document = await documentManager.createDocument(
        name,
        content || ''
      );
      console.log(`Created document: ${document.id}`);
      res.json({ success: true, document });
    } catch (error) {
      console.error('Error creating document from VS Code:', error);
      res.status(500).json({ error: error.message });
    }
  })();
});

// Get all documents (for VS Code extension) - no auth required
app.get('/api/documents/public-list', (req, res) => {
  (async () => {
    try {
      console.log('Public document list requested');
      const documents = await documentManager.getAllDocuments();
      console.log(`Returning ${documents.length} documents in public list`);
      res.json({ documents });
    } catch (error) {
      console.error('Error getting public document list:', error);
      res.status(500).json({ error: error.message });
    }
  })();
});

// Get all documents (for VS Code extension)
app.get('/api/documents/list', (req, res) => {
  (async () => {
    const documents = await documentManager.getAllDocuments();
    res.json({ documents });
  })();
});

// User authentication
app.post('/api/login', (req, res) => {
  const { username, password } = req.body;

  console.log(`Login attempt for user: ${username}`);

  (async () => {
    const result = await userManager.authenticateUser(username, password);
    if (result) {
      console.log(
        `Login successful for user: ${username}, setting cookie sessionId`
      );
      res.cookie('sessionId', result.sessionId, {
        httpOnly: false, // Allow JavaScript to access the cookie
        path: '/',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
      });
      res.json({
        success: true,
        user: result.user,
        sessionId: result.sessionId,
      });
    } else {
      console.log(`Login failed for user: ${username}`);
      res.status(401).json({ error: 'Invalid credentials' });
    }
  })();
});

app.post('/api/register', (req, res) => {
  const { username, password, displayName } = req.body;

  console.log(`Registration attempt for user: ${username}`);

  (async () => {
    const user = await userManager.createUser(username, password, displayName);
    if (user) {
      console.log(`Registration successful for user: ${username}`);
      const result = await userManager.authenticateUser(username, password);
      console.log(`Setting cookie sessionId for new user: ${username}`);
      res.cookie('sessionId', result.sessionId, {
        httpOnly: false, // Allow JavaScript to access the cookie
        path: '/',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
      });
      res.json({
        success: true,
        user: result.user,
        sessionId: result.sessionId,
      });
    } else {
      console.log(
        `Registration failed for user: ${username} (username already exists)`
      );
      res.status(400).json({ error: 'Username already exists' });
    }
  })();
});

app.post('/api/logout', (req, res) => {
  const sessionId = req.cookies.sessionId;
  if (sessionId) {
    (async () => {
      await userManager.logout(sessionId);
      res.clearCookie('sessionId');
      res.json({ success: true });
    })();
  } else {
    res.json({ success: true });
  }
});

app.get('/api/user', (req, res) => {
  if (req.user) {
    res.json({ user: req.user });
  } else {
    res.status(401).json({ error: 'Not authenticated' });
  }
});

app.post('/api/users/details', (req, res) => {
  const { userIds } = req.body;

  if (!Array.isArray(userIds)) {
    return res.status(400).json({ error: 'userIds must be an array' });
  }

  (async () => {
    const users = [];

    for (const id of userIds) {
      const user = await userManager.getUserById(id);
      if (user) {
        users.push({
          id: user.id,
          displayName: user.displayName,
          color: user.color,
        });
      }
    }

    res.json({ users });
  })();
});

// Document management
app.get('/api/documents', (req, res) => {
  (async () => {
    const documents = await documentManager.getAllDocuments();
    res.json({ documents });
  })();
});

app.post('/api/documents', (req, res) => {
  (async () => {
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { name, content, projectId, folderId } = req.body;
    const document = await documentManager.createDocument(
      name,
      content || '',
      projectId,
      folderId,
      user.id
    );

    if (document) {
      res.json({ document });
    } else {
      res.status(500).json({ error: 'Failed to create document' });
    }
  })();
});

app.get('/api/documents/:id', (req, res) => {
  (async () => {
    const document = await documentManager.getDocument(req.params.id);
    if (document) {
      res.json({ document });
    } else {
      res.status(404).json({ error: 'Document not found' });
    }
  })();
});

app.delete('/api/documents/:id', (req, res) => {
  (async () => {
    const success = await documentManager.deleteDocument(req.params.id);
    if (success) {
      res.json({ success });
    } else {
      res.status(404).json({ error: 'Document not found' });
    }
  })();
});

// Version history endpoints
app.get('/api/documents/:id/versions', (req, res) => {
  (async () => {
    const versions = await documentManager.getDocumentVersions(req.params.id);
    res.json({ versions });
  })();
});

app.get('/api/versions/:id', (req, res) => {
  (async () => {
    const version = await documentManager.getDocumentVersion(req.params.id);
    if (version) {
      res.json({ version });
    } else {
      res.status(404).json({ error: 'Version not found' });
    }
  })();
});

app.post('/api/versions/:id/restore', (req, res) => {
  (async () => {
    // Get current user from session
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const success = await documentManager.restoreDocumentVersion(
      req.params.id,
      user.id
    );

    if (success) {
      // Get the version to find out which document it belongs to
      const version = await documentManager.getDocumentVersion(req.params.id);
      if (version) {
        // Notify all clients in the document about the content update
        io.to(`document:${version.documentId}`).emit('content-updated', {
          content: version.content,
          userId: user.id,
        });
      }

      res.json({ success });
    } else {
      res
        .status(404)
        .json({ error: 'Version not found or could not be restored' });
    }
  })();
});

app.post('/api/documents/:id/save-version', (req, res) => {
  (async () => {
    const { versionName } = req.body;

    // Get current user from session
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const success = await documentManager.saveNamedVersion(
      req.params.id,
      versionName,
      user.id
    );

    if (success) {
      res.json({ success });
    } else {
      res
        .status(404)
        .json({ error: 'Document not found or version could not be saved' });
    }
  })();
});

// Track changes endpoints
app.get('/api/documents/:id/pending-changes', (req, res) => {
  (async () => {
    // Get current user from session
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const changes = await documentManager.getPendingChanges(req.params.id);
    res.json({ changes });
  })();
});

app.post('/api/changes/:id/accept', (req, res) => {
  (async () => {
    // Get current user from session
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const result = await documentManager.acceptChange(req.params.id, user.id);

    if (result.success) {
      // Update VS Code file if it's synced
      await vscodeManager.updateVSCodeFile(
        result.documentId,
        result.newContent
      );

      // Notify all clients in the document about the content update
      io.to(`document:${result.documentId}`).emit('content-updated', {
        content: result.newContent,
        userId: user.id,
        changeAccepted: true,
        changeId: req.params.id,
      });

      res.json({ success: true });
    } else {
      res
        .status(404)
        .json({ error: result.error || 'Change could not be accepted' });
    }
  })();
});

app.post('/api/changes/:id/reject', (req, res) => {
  (async () => {
    // Get current user from session
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const result = await documentManager.rejectChange(req.params.id);

    if (result.success) {
      // Notify all clients in the document about the rejected change
      if (result.documentId) {
        io.to(`document:${result.documentId}`).emit('change-rejected', {
          changeId: req.params.id,
          userId: user.id,
        });
      }

      res.json({ success: true });
    } else {
      res
        .status(404)
        .json({ error: result.error || 'Change could not be rejected' });
    }
  })();
});

// Endpoint to sync document to VS Code
app.post('/api/documents/:id/sync-vscode', (req, res) => {
  (async () => {
    // Get current user from session
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const documentId = req.params.id;
    const document = await documentManager.getDocument(documentId);

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    try {
      // Sync the document to VS Code using the manager
      const syncResult = await vscodeManager.syncToVSCode(documentId, document);

      if (!syncResult.success) {
        return res.status(500).json({
          error: syncResult.error || 'Unknown error syncing to VS Code',
        });
      }

      // Try to open the file in VS Code
      const vscode = spawn('code', [syncResult.filePath]);

      let errorOutput = '';
      vscode.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      vscode.on('error', (err) => {
        console.error('Error opening VS Code:', err);
        return res.status(500).json({
          error:
            'Failed to open VS Code. Make sure VS Code is installed and the "code" command is in your PATH.',
          filePath: syncResult.filePath,
          isSynced: true, // The file is synced even if VS Code didn't open
        });
      });

      // Notify all clients that this document is synced with VS Code
      io.to(`document:${documentId}`).emit('document-vscode-synced', {
        documentId,
        isSynced: true,
      });

      vscode.on('close', (code) => {
        if (code === 0) {
          console.log(
            'Successfully opened file in VS Code:',
            syncResult.filePath
          );
          return res.json({
            success: true,
            filePath: syncResult.filePath,
            isSynced: true,
          });
        } else {
          console.error('VS Code process exited with code:', code);
          return res.status(500).json({
            error: `VS Code process exited with code ${code}. Error: ${errorOutput}`,
            filePath: syncResult.filePath,
            isSynced: true, // The file is synced even if VS Code didn't open
          });
        }
      });
    } catch (error) {
      console.error('Error in VS Code sync:', error);
      return res.status(500).json({ error: error.message });
    }
  })();
});

// Endpoint to render Quarto document
app.post('/api/render', (req, res) => {
  const { documentId } = req.body;

  (async () => {
    const document = await documentManager.getDocument(documentId);

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    const content = document.content;
    const tempFilePath = path.join(__dirname, 'temp', 'document.qmd');

    // Ensure temp directory exists
    if (!fs.existsSync(path.join(__dirname, 'temp'))) {
      fs.mkdirSync(path.join(__dirname, 'temp'), { recursive: true });
    }

    // Write content to temporary file
    fs.writeFileSync(tempFilePath, content);

    // Execute Quarto render command
    exec(`quarto render ${tempFilePath} --to html`, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error: ${error.message}`);
        return res.status(500).json({ error: error.message });
      }

      if (stderr) {
        console.error(`stderr: ${stderr}`);
      }

      // Read the rendered HTML
      const htmlPath = path.join(__dirname, 'temp', 'document.html');
      if (fs.existsSync(htmlPath)) {
        const html = fs.readFileSync(htmlPath, 'utf8');

        // Broadcast rendered HTML to all clients viewing this document
        io.to(`document:${documentId}`).emit('preview-updated', { html });

        res.json({ html });
      } else {
        res.status(500).json({ error: 'Failed to generate HTML' });
      }
    });
  })();
});

// Yjs server statistics
app.get('/api/yjs/stats', (req, res) => {
  const stats = yjsServer.getStats();
  res.json(stats);
});

// Project management API
app.get('/api/projects', (req, res) => {
  (async () => {
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const projects = await projectManager.getAllProjects(user.id);
    res.json({ projects });
  })();
});

app.post('/api/projects', (req, res) => {
  (async () => {
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { name, description } = req.body;
    const project = await projectManager.createProject(
      name,
      description,
      user.id
    );

    if (project) {
      res.json({ project });
    } else {
      res.status(500).json({ error: 'Failed to create project' });
    }
  })();
});

app.get('/api/projects/:id', (req, res) => {
  (async () => {
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const project = await projectManager.getProject(req.params.id);
    if (project) {
      const structure = await projectManager.getProjectStructure(req.params.id);
      res.json({ project, structure });
    } else {
      res.status(404).json({ error: 'Project not found' });
    }
  })();
});

app.put('/api/projects/:id/activate', (req, res) => {
  (async () => {
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const success = await projectManager.setActiveProject(
      req.params.id,
      user.id
    );
    if (success) {
      res.json({ success: true });
    } else {
      res.status(500).json({ error: 'Failed to activate project' });
    }
  })();
});

// Folder management API
app.post('/api/folders', (req, res) => {
  (async () => {
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { name, projectId, parentId } = req.body;
    const folder = await projectManager.createFolder(
      name,
      projectId,
      user.id,
      parentId
    );

    if (folder) {
      res.json({ folder });
    } else {
      res.status(500).json({ error: 'Failed to create folder' });
    }
  })();
});

// Project collaboration API
app.post('/api/projects/:id/invite', (req, res) => {
  (async () => {
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { username } = req.body;
    const result = await projectManager.inviteUserToProject(
      req.params.id,
      user.id,
      username
    );

    if (result.success) {
      res.json({ success: true, invitationId: result.invitationId });
    } else {
      res.status(400).json({ error: result.error });
    }
  })();
});

app.get('/api/invitations', (req, res) => {
  (async () => {
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const invitations = await projectManager.getPendingInvitations(user.id);
    res.json({ invitations });
  })();
});

app.post('/api/invitations/:id/accept', (req, res) => {
  (async () => {
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const result = await projectManager.acceptInvitation(
      req.params.id,
      user.id
    );

    if (result.success) {
      res.json({ success: true });
    } else {
      res.status(400).json({ error: result.error });
    }
  })();
});

app.post('/api/invitations/:id/decline', (req, res) => {
  (async () => {
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const result = await projectManager.declineInvitation(
      req.params.id,
      user.id
    );

    if (result.success) {
      res.json({ success: true });
    } else {
      res.status(400).json({ error: result.error });
    }
  })();
});

app.get('/api/projects/:id/members', (req, res) => {
  (async () => {
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user has access to this project
    const hasAccess = await projectManager.hasProjectAccess(
      req.params.id,
      user.id
    );
    if (!hasAccess) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const members = await projectManager.getProjectMembers(req.params.id);
    res.json({ members });
  })();
});

app.delete('/api/projects/:id/members/:userId', (req, res) => {
  (async () => {
    const sessionId = req.cookies.sessionId;
    const user = await userManager.validateSession(sessionId);

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const result = await projectManager.removeUserFromProject(
      req.params.id,
      req.params.userId,
      user.id
    );

    if (result.success) {
      res.json({ success: true });
    } else {
      res.status(400).json({ error: result.error });
    }
  })();
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
