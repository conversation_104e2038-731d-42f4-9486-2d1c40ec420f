const express = require('express');
const http = require('http');
const socketIO = require('socket.io');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const cookieParser = require('cookie-parser');

// Import SQLite models
const { DocumentManager } = require('./models/documentSqlite');
const { UserManager } = require('./models/userSqlite');

const app = express();
const server = http.createServer(app);
const io = socketIO(server);

// Middleware
app.use(express.static('public'));
app.use(express.json());
app.use(cookieParser());

// Initialize managers
const documentManager = new DocumentManager();
const userManager = new UserManager();

// Create a default user if none exists
(async () => {
  const users = await userManager.getAllUsers();
  if (users.length === 0) {
    await userManager.createUser('demo', 'demo', 'Demo User');
  }
})();

// Track active connections
const activeConnections = new Map();

// Authentication middleware
const authenticate = async (req, res, next) => {
  const sessionId = req.cookies.sessionId;
  if (sessionId) {
    const user = await userManager.validateSession(sessionId);
    if (user) {
      req.user = user;
      return next();
    }
  }

  // Allow access to authentication endpoints
  if (req.path === '/api/login' || req.path === '/api/register') {
    return next();
  }

  // Block other API routes if not authenticated
  if (req.path.startsWith('/api/')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // For non-API routes, redirect to login page
  if (req.path !== '/login.html' && req.path !== '/register.html' && !req.path.startsWith('/css/') && !req.path.startsWith('/js/auth')) {
    return res.redirect('/login.html');
  }

  next();
};

// Apply authentication middleware
app.use(authenticate);

// Socket.IO connection
io.on('connection', (socket) => {
  console.log('New client connected');
  let currentUser = null;
  let currentDocumentId = null;

  // Check for authentication in the handshake
  if (socket.handshake.auth && socket.handshake.auth.sessionId) {
    const sessionId = socket.handshake.auth.sessionId;
    console.log('Socket connected with sessionId in handshake:', sessionId);
    
    (async () => {
      const user = await userManager.validateSession(sessionId);
      if (user) {
        currentUser = user;
        console.log('Socket authenticated on connection for user:', user.displayName);
        socket.emit('authentication-success', { user });

        // Store connection
        activeConnections.set(socket.id, { user, socket });
        console.log('Active connections:', activeConnections.size);

        // Send list of documents
        const documents = await documentManager.getAllDocuments();
        console.log('Sending document list:', documents.length, 'documents');
        socket.emit('document-list', documents);
      }
    })();
  }

  // Authenticate socket connection
  socket.on('authenticate', ({ sessionId }) => {
    console.log('Socket authentication attempt with sessionId:', sessionId ? 'present' : 'missing');
    
    (async () => {
      const user = await userManager.validateSession(sessionId);
      if (user) {
        currentUser = user;
        console.log('Socket authenticated for user:', user.displayName);
        socket.emit('authentication-success', { user });

        // Store connection
        activeConnections.set(socket.id, { user, socket });
        console.log('Active connections:', activeConnections.size);

        // Send list of documents
        const documents = await documentManager.getAllDocuments();
        console.log('Sending document list:', documents.length, 'documents');
        socket.emit('document-list', documents);
      } else {
        console.log('Socket authentication failed');
        socket.emit('authentication-failed');
      }
    })();
  });

  // Join a document
  socket.on('join-document', ({ documentId }) => {
    console.log('Join document request:', documentId);
    if (!currentUser) {
      console.log('Cannot join document: User not authenticated');
      return;
    }

    (async () => {
      // Leave current document if any
      if (currentDocumentId) {
        console.log('Leaving current document:', currentDocumentId);
        socket.leave(`document:${currentDocumentId}`);
        const activeUsers = await documentManager.removeUserFromDocument(currentDocumentId, currentUser.id);
        console.log('Active users after leaving:', activeUsers);
        io.to(`document:${currentDocumentId}`).emit('active-users-updated', activeUsers);
      }

      // Join new document
      currentDocumentId = documentId;
      const document = await documentManager.getDocument(documentId);

      if (document) {
        console.log('Joining document:', document.name, '(ID:', documentId, ')');
        socket.join(`document:${documentId}`);

        // Add user to document and notify others
        const activeUsers = await documentManager.addUserToDocument(documentId, currentUser.id);
        console.log('Active users after joining:', activeUsers);

        // Send document content to the client
        console.log('Sending document content to client, length:', document.content.length);
        socket.emit('init-document', {
          content: document.content,
          documentId,
          documentName: document.name
        });

        // Notify all clients in the document about the new user
        console.log('Broadcasting active users update');
        io.to(`document:${documentId}`).emit('active-users-updated', activeUsers);

        // Send user info for cursor tracking
        console.log('Broadcasting user joined event');
        socket.broadcast.to(`document:${documentId}`).emit('user-joined', {
          userId: currentUser.id,
          username: currentUser.displayName,
          color: currentUser.color
        });
      } else {
        console.log('Document not found:', documentId);
        socket.emit('error', { message: 'Document not found' });
      }
    })();
  });

  // Handle cursor position updates
  socket.on('cursor-update', ({ position }) => {
    if (!currentUser || !currentDocumentId) return;

    socket.broadcast.to(`document:${currentDocumentId}`).emit('remote-cursor', {
      userId: currentUser.id,
      username: currentUser.displayName,
      color: currentUser.color,
      position
    });
  });

  // Handle content updates
  socket.on('update-content', ({ documentId, content, changes }) => {
    if (!currentUser) return;

    (async () => {
      const document = await documentManager.getDocument(documentId);
      if (document) {
        // Update document content
        await documentManager.updateDocument(documentId, content);

        // Broadcast changes to other clients
        socket.broadcast.to(`document:${documentId}`).emit('content-updated', {
          content,
          changes,
          userId: currentUser.id
        });

        // Log content update for debugging
        console.log(`Document ${documentId} updated by ${currentUser.displayName}`);
      } else {
        console.error(`Document ${documentId} not found for update`);
      }
    })();
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log('Client disconnected');

    // Remove from active connections
    activeConnections.delete(socket.id);

    // Remove from current document if any
    if (currentUser && currentDocumentId) {
      (async () => {
        const activeUsers = await documentManager.removeUserFromDocument(currentDocumentId, currentUser.id);
        io.to(`document:${currentDocumentId}`).emit('active-users-updated', activeUsers);
        io.to(`document:${currentDocumentId}`).emit('user-left', { userId: currentUser.id });
      })();
    }
  });
});

// API Routes

// User authentication
app.post('/api/login', (req, res) => {
  const { username, password } = req.body;

  console.log(`Login attempt for user: ${username}`);
  
  (async () => {
    const result = await userManager.authenticateUser(username, password);
    if (result) {
      console.log(`Login successful for user: ${username}, setting cookie sessionId`);
      res.cookie('sessionId', result.sessionId, {
        httpOnly: false, // Allow JavaScript to access the cookie
        path: '/',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      });
      res.json({ success: true, user: result.user, sessionId: result.sessionId });
    } else {
      console.log(`Login failed for user: ${username}`);
      res.status(401).json({ error: 'Invalid credentials' });
    }
  })();
});

app.post('/api/register', (req, res) => {
  const { username, password, displayName } = req.body;

  console.log(`Registration attempt for user: ${username}`);
  
  (async () => {
    const user = await userManager.createUser(username, password, displayName);
    if (user) {
      console.log(`Registration successful for user: ${username}`);
      const result = await userManager.authenticateUser(username, password);
      console.log(`Setting cookie sessionId for new user: ${username}`);
      res.cookie('sessionId', result.sessionId, {
        httpOnly: false, // Allow JavaScript to access the cookie
        path: '/',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      });
      res.json({ success: true, user: result.user, sessionId: result.sessionId });
    } else {
      console.log(`Registration failed for user: ${username} (username already exists)`);
      res.status(400).json({ error: 'Username already exists' });
    }
  })();
});

app.post('/api/logout', (req, res) => {
  const sessionId = req.cookies.sessionId;
  if (sessionId) {
    (async () => {
      await userManager.logout(sessionId);
      res.clearCookie('sessionId');
      res.json({ success: true });
    })();
  } else {
    res.json({ success: true });
  }
});

app.get('/api/user', (req, res) => {
  if (req.user) {
    res.json({ user: req.user });
  } else {
    res.status(401).json({ error: 'Not authenticated' });
  }
});

app.post('/api/users/details', (req, res) => {
  const { userIds } = req.body;

  if (!Array.isArray(userIds)) {
    return res.status(400).json({ error: 'userIds must be an array' });
  }

  (async () => {
    const users = [];
    
    for (const id of userIds) {
      const user = await userManager.getUserById(id);
      if (user) {
        users.push({
          id: user.id,
          displayName: user.displayName,
          color: user.color
        });
      }
    }
    
    res.json({ users });
  })();
});

// Document management
app.get('/api/documents', (req, res) => {
  (async () => {
    const documents = await documentManager.getAllDocuments();
    res.json({ documents });
  })();
});

app.post('/api/documents', (req, res) => {
  const { name, content } = req.body;
  
  (async () => {
    const document = await documentManager.createDocument(name, content || '');
    res.json({ document });
  })();
});

app.get('/api/documents/:id', (req, res) => {
  (async () => {
    const document = await documentManager.getDocument(req.params.id);
    if (document) {
      res.json({ document });
    } else {
      res.status(404).json({ error: 'Document not found' });
    }
  })();
});

app.delete('/api/documents/:id', (req, res) => {
  (async () => {
    const success = await documentManager.deleteDocument(req.params.id);
    if (success) {
      res.json({ success });
    } else {
      res.status(404).json({ error: 'Document not found' });
    }
  })();
});

// Endpoint to render Quarto document
app.post('/api/render', (req, res) => {
  const { documentId } = req.body;
  
  (async () => {
    const document = await documentManager.getDocument(documentId);

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    const content = document.content;
    const tempFilePath = path.join(__dirname, 'temp', 'document.qmd');

    // Ensure temp directory exists
    if (!fs.existsSync(path.join(__dirname, 'temp'))) {
      fs.mkdirSync(path.join(__dirname, 'temp'), { recursive: true });
    }

    // Write content to temporary file
    fs.writeFileSync(tempFilePath, content);

    // Execute Quarto render command
    exec(`quarto render ${tempFilePath} --to html`, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error: ${error.message}`);
        return res.status(500).json({ error: error.message });
      }

      if (stderr) {
        console.error(`stderr: ${stderr}`);
      }

      // Read the rendered HTML
      const htmlPath = path.join(__dirname, 'temp', 'document.html');
      if (fs.existsSync(htmlPath)) {
        const html = fs.readFileSync(htmlPath, 'utf8');

        // Broadcast rendered HTML to all clients viewing this document
        io.to(`document:${documentId}`).emit('preview-updated', { html });

        res.json({ html });
      } else {
        res.status(500).json({ error: 'Failed to generate HTML' });
      }
    });
  })();
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
