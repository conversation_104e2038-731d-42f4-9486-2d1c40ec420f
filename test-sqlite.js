const { UserManager } = require('./models/userSqlite');
const { DocumentManager } = require('./models/documentSqlite');
const db = require('./models/database');

async function testSQLite() {
  console.log('Testing SQLite implementation...');
  
  // Test user manager
  const userManager = new UserManager();
  
  // Create a test user
  console.log('Creating test user...');
  const testUser = await userManager.createUser('test_user', 'password123', 'Test User');
  console.log('Test user created:', testUser);
  
  // Authenticate the test user
  console.log('Authenticating test user...');
  const authResult = await userManager.authenticateUser('test_user', 'password123');
  console.log('Authentication result:', authResult ? 'Success' : 'Failed');
  
  if (authResult) {
    console.log('Session ID:', authResult.sessionId);
    
    // Validate the session
    console.log('Validating session...');
    const validatedUser = await userManager.validateSession(authResult.sessionId);
    console.log('Validated user:', validatedUser);
    
    // Test document manager
    const documentManager = new DocumentManager();
    
    // Create a test document
    console.log('Creating test document...');
    const testDoc = await documentManager.createDocument('Test Document', '# Test Content');
    console.log('Test document created:', testDoc);
    
    // Get the document
    console.log('Getting document...');
    const retrievedDoc = await documentManager.getDocument(testDoc.id);
    console.log('Retrieved document:', retrievedDoc);
    
    // Update the document
    console.log('Updating document...');
    const updateResult = await documentManager.updateDocument(testDoc.id, '# Updated Content');
    console.log('Update result:', updateResult);
    
    // Add user to document
    console.log('Adding user to document...');
    const activeUsers = await documentManager.addUserToDocument(testDoc.id, testUser.id);
    console.log('Active users:', activeUsers);
    
    // Get all documents
    console.log('Getting all documents...');
    const allDocs = await documentManager.getAllDocuments();
    console.log('All documents:', allDocs);
    
    // Clean up
    console.log('Cleaning up...');
    await documentManager.removeUserFromDocument(testDoc.id, testUser.id);
    await documentManager.deleteDocument(testDoc.id);
    await db.run('DELETE FROM users WHERE username = ?', ['test_user']);
    await db.run('DELETE FROM sessions WHERE username = ?', ['test_user']);
  }
  
  console.log('Test completed');
}

// Run the test
testSQLite()
  .then(() => {
    console.log('All tests passed!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
