# VSCode Extension Improvements

This document outlines the recent improvements made to the Collaborative Quarto VSCode extension.

## New Features

### 1. Automatic Metadata File Detection

The extension now automatically activates when:
- A workspace contains a `.quarto-collab.json` file
- A `.quarto-collab.json` file is opened in the editor

**Activation Events Added:**
- `workspaceContains:**/.quarto-collab.json`
- `onOpenTextDocument:.quarto-collab.json`

### 2. Auto-Connection Feature

**New Setting:** `collaborative-quarto.autoConnect` (default: `true`)

When enabled:
- Automatically connects to the server when a metadata file is detected
- Shows a notification about the auto-connection
- No user prompt required

When disabled:
- Shows a prompt asking if the user wants to connect
- Maintains the previous behavior

### 3. Remote Cursor Display Control

**New Setting:** `collaborative-quarto.showRemoteCursors` (default: `true`)

When enabled:
- Shows remote user cursors with improved, less intrusive styling
- Uses subtle border indicators instead of background colors
- Displays username labels as floating overlays

When disabled:
- Completely hides all remote cursors
- Prevents any layout changes from cursor rendering
- Automatically clears existing cursors when setting is changed

### 4. Improved Cursor Rendering

**Changes Made:**
- Replaced background highlighting with subtle left border
- Username labels now appear as floating overlays above the cursor
- Zero-width cursor ranges to prevent text layout shifts
- More transparent and less visually intrusive design

### 5. Automatic Sync of Already Open Documents

**New Feature:**
- When auto-connecting to a server, already open documents are automatically synced
- No need to lose and regain focus on open files
- Documents are automatically joined to their collaborative sessions
- Works for both auto-connection and manual connection scenarios

**How it works:**
- After successful connection, scans all open editors for .qmd files in the sync folder
- Automatically joins each document to its collaborative session on the server
- Sets up real-time synchronization without requiring user interaction

## Configuration Options

Add these settings to your VSCode settings:

```json
{
  "collaborative-quarto.autoConnect": true,
  "collaborative-quarto.showRemoteCursors": true
}
```

## Usage

### Automatic Setup
1. Place a `.quarto-collab.json` file in your workspace root
2. The extension will automatically activate and detect the project
3. If `autoConnect` is enabled, it will connect automatically
4. If disabled, you'll see a prompt to connect

### Manual Control
- Use the command palette: "Collaborative Quarto: Connect to Server"
- Toggle cursor display via settings
- Disable auto-connect if you prefer manual control

## Metadata File Format

The `.quarto-collab.json` file should contain:

```json
{
  "project": {
    "id": "your-project-id",
    "name": "Your Project Name"
  },
  "server": {
    "url": "http://your-server:3000"
  },
  "sync": {
    "folderPath": "/path/to/sync/folder"
  },
  "auth": {
    "sessionId": "your-session-id"
  }
}
```

## Benefits

1. **Seamless Workflow**: No manual setup required when opening projects
2. **Reduced Visual Clutter**: Option to disable cursors that affect layout
3. **Better User Experience**: Less intrusive cursor rendering
4. **Flexible Configuration**: Users can customize behavior to their preferences
5. **Immediate Sync**: Already open documents sync automatically without focus changes
6. **No Interruption**: Users can continue working without losing context
