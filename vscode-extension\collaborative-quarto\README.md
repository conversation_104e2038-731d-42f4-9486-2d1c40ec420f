# Collaborative Quarto Extension for VS Code

This extension enables real-time collaborative editing of Quarto documents in VS Code, connecting to a Collaborative Quarto server.

## Features

- Real-time collaborative editing of Quarto documents
- See other users' cursors and selections
- Synchronize changes between VS Code and the web editor
- Select from available documents on the server

## Requirements

- VS Code 1.60.0 or higher
- A running Collaborative Quarto server

## Extension Settings

This extension contributes the following settings:

* `collaborative-quarto.serverUrl`: URL of the Collaborative Quarto server (default: http://localhost:3000)

## How to Use

1. Install the extension
2. Configure the server URL in settings
3. Use the command "Connect to Collaborative Quarto Server" from the command palette
4. Select a document to edit
5. Start collaborating!

## Commands

- `collaborative-quarto.connect`: Connect to the Collaborative Quarto server
- `collaborative-quarto.disconnect`: Disconnect from the server
- `collaborative-quarto.selectDocument`: Select a document to edit

## Known Issues

- Currently, the extension replaces the entire document content when receiving updates from other users. Future versions will implement incremental updates.
- Authentication is not yet implemented.

## Release Notes

### 0.1.0

Initial release of Collaborative Quarto extension.
