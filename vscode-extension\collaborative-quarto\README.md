# Collaborative Quarto Extension for VS Code

This extension enables real-time collaborative editing of Quarto documents in VS Code, connecting to a Collaborative Quarto server.

## Features

- Real-time collaborative editing of Quarto documents
- See other users' cursors and selections (with option to disable)
- Synchronize changes between VS Code and the web editor
- Select from available documents on the server
- **NEW**: Automatic project detection via `.quarto-collab.json` files
- **NEW**: Auto-connection to collaborative projects
- **NEW**: Configurable cursor display options
- **NEW**: Less intrusive cursor rendering that doesn't affect text layout

## Requirements

- VS Code 1.60.0 or higher
- A running Collaborative Quarto server

## Extension Settings

This extension contributes the following settings:

* `collaborative-quarto.serverUrl`: URL of the Collaborative Quarto server (default: http://localhost:3000)
* `collaborative-quarto.showRemoteCursors`: Show remote user cursors in the editor (default: true)
* `collaborative-quarto.autoConnect`: Automatically connect when a metadata file is detected (default: true)
* `collaborative-quarto.currentProjectId`: ID of the currently selected project
* `collaborative-quarto.syncFolderPath`: Path to the local folder for syncing project files

## How to Use

### Automatic Setup (Recommended)
1. Install the extension
2. Place a `.quarto-collab.json` metadata file in your workspace root
3. Open the workspace - the extension will automatically detect and connect to the project
4. Start collaborating!

### Manual Setup
1. Install the extension
2. Configure the server URL in settings
3. Use the command "Connect to Collaborative Quarto Server" from the command palette
4. Select a document to edit
5. Start collaborating!

### Metadata File Format
Create a `.quarto-collab.json` file in your workspace root:
```json
{
  "project": {
    "id": "your-project-id",
    "name": "Your Project Name"
  },
  "server": {
    "url": "http://your-server:3000"
  },
  "sync": {
    "folderPath": "/path/to/sync/folder"
  },
  "auth": {
    "sessionId": "your-session-id"
  }
}
```

## Commands

- `collaborative-quarto.connect`: Connect to the Collaborative Quarto server
- `collaborative-quarto.disconnect`: Disconnect from the server
- `collaborative-quarto.selectDocument`: Select a document to edit

## Known Issues

- Currently, the extension replaces the entire document content when receiving updates from other users. Future versions will implement incremental updates.
- Authentication is not yet implemented.

## Release Notes

### 0.2.0

**New Features:**
- Automatic project detection via `.quarto-collab.json` files
- Auto-connection to collaborative projects (configurable)
- Option to disable remote cursor display
- Improved cursor rendering that doesn't affect text layout
- File watcher for metadata files
- Configuration change listeners

**Improvements:**
- Less intrusive cursor styling with floating username labels
- Zero-width cursor ranges to prevent layout shifts
- Automatic activation when metadata files are present

### 0.1.0

Initial release of Collaborative Quarto extension.
