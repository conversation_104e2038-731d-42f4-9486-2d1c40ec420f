const vscode = require('vscode');

// Workaround for navigator deprecation warning in engine.io-client
// This prevents the warning about navigator being deprecated
if (typeof global !== 'undefined' && !global.navigator) {
  global.navigator = {
    userAgent: 'vscode-extension',
  };
}

const io = require('socket.io-client');
const fs = require('fs');
const path = require('path');
const os = require('os');
const fetch = require('node-fetch');

/**
 * @type {import('socket.io-client').Socket}
 */
let socket = null;

/**
 * @type {vscode.StatusBarItem}
 */
let statusBarItem = null;

/**
 * @type {string}
 */
let currentDocumentId = null;

/**
 * @type {string}
 */
let currentProjectId = null;

/**
 * @type {string}
 */
let syncFolderPath = null;

/**
 * @type {string}
 */
let sessionId = null;

/**
 * @type {Map<string, {decoration: vscode.TextEditorDecorationType, position: vscode.Position}>}
 */
const remoteCursors = new Map();

/**
 * Clear all remote cursors
 */
function clearRemoteCursors() {
  for (const userId of remoteCursors.keys()) {
    removeRemoteCursor(userId);
  }
}

/**
 * Remove a remote cursor
 * @param {string} userId
 */
function removeRemoteCursor(userId) {
  const cursorInfo = remoteCursors.get(userId);
  if (cursorInfo) {
    const editor = vscode.window.activeTextEditor;
    if (editor) {
      editor.setDecorations(cursorInfo.decoration, []);
    }
    cursorInfo.decoration.dispose();
    remoteCursors.delete(userId);
  }
}

/**
 * Update a remote cursor in the editor
 * @param {Object} cursor
 */
function updateRemoteCursor(cursor) {
  const editor = vscode.window.activeTextEditor;
  if (!editor) {
    console.log('No active editor for cursor update');
    return;
  }

  console.log('Updating remote cursor:', cursor);

  // Remove existing cursor decoration
  removeRemoteCursor(cursor.userId);

  try {
    // Handle different cursor position formats
    let position;
    if (typeof cursor.position === 'number') {
      // Convert simple offset to line/character position
      const document = editor.document;
      position = document.positionAt(cursor.position);
      console.log(
        `Converted cursor position ${cursor.position} to line ${position.line}, character ${position.character}`
      );
    } else if (
      cursor.position &&
      typeof cursor.position.line === 'number' &&
      typeof cursor.position.character === 'number'
    ) {
      // Already in correct format
      position = new vscode.Position(
        cursor.position.line,
        cursor.position.character
      );
    } else {
      console.error('Invalid cursor position data:', cursor.position);
      return;
    }

    // Ensure position is within document bounds
    const document = editor.document;
    const maxLine = document.lineCount - 1;
    const actualLine = Math.min(position.line, maxLine);
    const lineText = document.lineAt(actualLine).text;
    const actualCharacter = Math.min(position.character, lineText.length);

    const finalPosition = new vscode.Position(actualLine, actualCharacter);
    console.log(
      `Created position: line ${actualLine}, character ${actualCharacter}`
    );

    // Create decoration type for this cursor
    const decorationType = vscode.window.createTextEditorDecorationType({
      // Add a cursor line
      backgroundColor: `${cursor.color}33`, // Add transparency
      borderWidth: '2px',
      borderStyle: 'solid',
      borderColor: cursor.color,
      // Add the user name as a label
      after: {
        contentText: ` ${cursor.username || 'User'}`,
        color: cursor.color,
        margin: '0 0 0 5px',
        fontStyle: 'italic',
        fontWeight: 'bold',
      },
      // Make sure it's visible
      isWholeLine: false,
      rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
    });

    // Create a range that extends a bit to make the cursor more visible
    // This creates a small selection rather than just a cursor position
    const endCharacter = Math.min(actualCharacter + 1, lineText.length);
    const endPosition = new vscode.Position(actualLine, endCharacter);
    const range = new vscode.Range(finalPosition, endPosition);

    // Apply decoration
    editor.setDecorations(decorationType, [range]);

    // Log the decoration
    console.log('Applied decoration at range:', range);

    // Store cursor information
    remoteCursors.set(cursor.userId, {
      decoration: decorationType,
      position: finalPosition,
      username: cursor.username,
      color: cursor.color,
    });

    console.log('Remote cursor updated successfully');
  } catch (error) {
    console.error('Error updating remote cursor:', error);
  }
}

/**
 * @type {boolean}
 */
let isProcessingRemoteChange = false;

/**
 * @type {boolean}
 */
let trackChangesMode = false;

/**
 * @type {vscode.StatusBarItem}
 */
let trackChangesStatusBarItem = null;

/**
 * @type {vscode.StatusBarItem}
 */
let usersStatusBarItem = null;

/**
 * @type {Array}
 */
let activeUsers = [];

/**
 * @type {Array<Object>}
 */
let pendingChanges = [];

/**
 * @type {NodeJS.Timeout}
 */
let cursorUpdateInterval = null;

/**
 * @type {vscode.ExtensionContext}
 */
let extensionContext;

/**
 * @param {vscode.ExtensionContext} context
 */
async function activate(context) {
  console.log('Collaborative Quarto extension is now active');

  // Store context for later use
  extensionContext = context;

  // Create status bar items
  statusBarItem = vscode.window.createStatusBarItem(
    vscode.StatusBarAlignment.Left,
    100
  );
  statusBarItem.text = '$(plug) Collaborative Quarto: Disconnected';
  statusBarItem.command = 'collaborative-quarto.connect';
  statusBarItem.show();
  context.subscriptions.push(statusBarItem);

  // Create track changes status bar item
  trackChangesStatusBarItem = vscode.window.createStatusBarItem(
    vscode.StatusBarAlignment.Left,
    99
  );
  trackChangesStatusBarItem.text = '$(diff) Track Changes: OFF';
  trackChangesStatusBarItem.command = 'collaborative-quarto.toggleTrackChanges';
  trackChangesStatusBarItem.show();
  context.subscriptions.push(trackChangesStatusBarItem);

  // Create users status bar item
  usersStatusBarItem = vscode.window.createStatusBarItem(
    vscode.StatusBarAlignment.Left,
    98
  );
  usersStatusBarItem.text = '$(person) Users: 0';
  usersStatusBarItem.tooltip = 'Active collaborative users';
  usersStatusBarItem.show();
  context.subscriptions.push(usersStatusBarItem);

  // Register commands
  context.subscriptions.push(
    vscode.commands.registerCommand(
      'collaborative-quarto.connect',
      connectToServer
    ),
    vscode.commands.registerCommand(
      'collaborative-quarto.disconnect',
      disconnectFromServer
    ),
    vscode.commands.registerCommand(
      'collaborative-quarto.selectDocument',
      selectDocument
    ),
    vscode.commands.registerCommand(
      'collaborative-quarto.createDocument',
      createDocument
    ),
    vscode.commands.registerCommand(
      'collaborative-quarto.toggleTrackChanges',
      toggleTrackChanges
    ),
    vscode.commands.registerCommand(
      'collaborative-quarto.showPendingChanges',
      showPendingChanges
    ),
    vscode.commands.registerCommand(
      'collaborative-quarto.showVersionHistory',
      showVersionHistory
    ),
    vscode.commands.registerCommand(
      'collaborative-quarto.setupProject',
      setupProject
    ),
    vscode.commands.registerCommand(
      'collaborative-quarto.selectSyncFolder',
      selectSyncFolder
    ),
    vscode.commands.registerCommand(
      'collaborative-quarto.openProjectFolder',
      openProjectFolder
    ),
    vscode.commands.registerCommand(
      'collaborative-quarto.syncProject',
      syncCurrentProject
    )
  );

  // Register event handlers
  context.subscriptions.push(
    vscode.workspace.onDidChangeTextDocument(handleLocalDocumentChange),
    vscode.window.onDidChangeTextEditorSelection(handleLocalSelectionChange),
    vscode.window.onDidChangeActiveTextEditor(handleActiveEditorChange)
  );

  // Load project configuration
  await loadProjectConfig();

  // Check for project metadata in workspace folders
  await checkForProjectMetadata();
}

/**
 * Connect to the collaborative server
 */
async function connectToServer() {
  try {
    // Get server URL from configuration
    const config = vscode.workspace.getConfiguration('collaborative-quarto');
    const serverUrl = config.get('serverUrl');

    if (!serverUrl) {
      vscode.window.showErrorMessage(
        'Please configure the Collaborative Quarto server URL in settings'
      );
      return;
    }

    // Disconnect if already connected
    if (socket) {
      socket.disconnect();
    }

    // Show connecting status
    statusBarItem.text = '$(sync~spin) Collaborative Quarto: Connecting...';

    let username, password;

    // Try to use stored session ID first
    if (sessionId) {
      console.log('Attempting to connect with stored session ID');
      useStoredSession = true;
    } else {
      // Get credentials from user
      username = await vscode.window.showInputBox({
        prompt: 'Enter your username for Collaborative Quarto',
        placeHolder: 'Username',
      });

      if (!username) {
        statusBarItem.text = '$(plug) Collaborative Quarto: Disconnected';
        return; // User cancelled
      }

      password = await vscode.window.showInputBox({
        prompt: 'Enter your password',
        password: true,
        placeHolder: 'Password',
      });

      if (!password) {
        statusBarItem.text = '$(plug) Collaborative Quarto: Disconnected';
        return; // User cancelled
      }
    }

    // Connect to server
    socket = io(serverUrl, {
      auth: sessionId ? { sessionId } : { username, password },
    });

    // Handle connection events
    socket.on('connect', async () => {
      console.log('Socket connected, authenticating...');

      // Authenticate with the server
      try {
        if (sessionId) {
          // Try to authenticate with stored session ID
          console.log('Attempting authentication with stored session ID');
          socket.emit('authenticate', { sessionId });
        } else {
          // Authenticate with username/password
          const response = await fetch(`${serverUrl}/api/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password }),
          });

          const data = await response.json();

          if (data.success) {
            // Store the session ID for future HTTP requests
            sessionId = data.sessionId;
            socket.auth = { sessionId };

            console.log('Authentication successful, session ID:', sessionId);

            // Authenticate the socket connection
            socket.emit('authenticate', { sessionId });

            statusBarItem.text = '$(check) Collaborative Quarto: Connected';
            vscode.window.showInformationMessage(
              'Connected to Collaborative Quarto server'
            );

            // Check if we have a project configured
            if (currentProjectId && syncFolderPath) {
              // Auto-sync the configured project
              try {
                await autoSyncProject();
              } catch (error) {
                console.log(
                  'Auto-sync failed, user can manually setup project:',
                  error.message
                );
                // Don't show error to user - they can manually setup project
              }
            } else {
              // Prompt to setup project or select document
              const choice = await vscode.window.showQuickPick(
                [
                  {
                    label: 'Setup Project',
                    description:
                      'Configure project and sync folder for collaborative editing',
                  },
                  {
                    label: 'Select Document',
                    description: 'Select a single document to edit',
                  },
                ],
                {
                  placeHolder: 'How would you like to collaborate?',
                }
              );

              if (choice) {
                if (choice.label === 'Setup Project') {
                  await setupProject();
                } else {
                  selectDocument();
                }
              }
            }
          } else {
            socket.disconnect();
            statusBarItem.text =
              '$(error) Collaborative Quarto: Authentication Failed';
            vscode.window.showErrorMessage(
              'Authentication failed: ' + (data.error || 'Invalid credentials')
            );
          }
        }
      } catch (error) {
        socket.disconnect();
        statusBarItem.text =
          '$(error) Collaborative Quarto: Authentication Failed';
        vscode.window.showErrorMessage(
          `Authentication failed: ${error.message}`
        );
      }
    });

    socket.on('disconnect', () => {
      statusBarItem.text = '$(plug) Collaborative Quarto: Disconnected';
      vscode.window.showWarningMessage(
        'Disconnected from Collaborative Quarto server'
      );
      clearRemoteCursors();
      currentDocumentId = null;
      sessionId = null; // Clear session ID on disconnect
    });

    // Handle authentication failure (e.g., expired session)
    socket.on('authentication-failed', async () => {
      console.log(
        'Authentication failed, clearing stored session and retrying with credentials'
      );
      sessionId = null; // Clear invalid session
      socket.disconnect();

      // Retry connection with fresh credentials
      vscode.window.showWarningMessage('Session expired. Please login again.');
      await connectToServer();
    });

    socket.on('error', (error) => {
      statusBarItem.text = '$(error) Collaborative Quarto: Error';
      vscode.window.showErrorMessage(`Connection error: ${error.message}`);
    });

    // Handle document events
    socket.on('document-list', (documents) => {
      // Store documents for selection
      if (extensionContext) {
        extensionContext.workspaceState.update('documents', documents);
        console.log(`Stored ${documents.length} documents in workspace state`);
      }
    });

    socket.on('init-document', ({ content, documentId, documentName }) => {
      console.log(
        `Received init-document for: ${documentName} (ID: ${documentId})`
      );
      console.log(
        `Content length: ${content?.length}, Current document ID: ${currentDocumentId}`
      );

      currentDocumentId = documentId;
      statusBarItem.text = `$(check) Collaborative Quarto: Editing ${documentName}`;

      // Check if we already have this document open in the editor
      const activeEditor = vscode.window.activeTextEditor;
      if (activeEditor && syncFolderPath) {
        const activeFile = activeEditor.document.fileName;
        const relativePath = path.relative(syncFolderPath, activeFile);
        const activeFileName = path.basename(relativePath, '.qmd');

        console.log(`Active file: ${activeFile}`);
        console.log(
          `Active file name: ${activeFileName}, Document name: ${documentName}`
        );

        // If the active file matches this document, just update the content if needed
        if (
          activeFileName === documentName ||
          activeFile.includes(documentName)
        ) {
          console.log(
            `Document ${documentName} is already open, checking content sync`
          );
          const currentContent = activeEditor.document.getText();
          console.log(
            `Current content length: ${currentContent.length}, New content length: ${content?.length}`
          );
          if (currentContent !== content) {
            console.log('Content differs, updating document');
            // Update the content without opening a new file
            updateActiveDocumentContent(content);
          } else {
            console.log('Content is already in sync');
          }
          // Start periodic cursor updates
          startCursorUpdates();
          return;
        }
      }

      // Open or update the document
      console.log(
        `Opening/updating document: ${documentName} (ID: ${documentId})`
      );
      openOrUpdateDocument(documentId, documentName, content);

      // Start periodic cursor updates
      startCursorUpdates();
    });

    socket.on(
      'content-updated',
      async ({ content, changes, userId, fromVSCode, isIncrementalUpdate }) => {
        console.log('Received content update:', {
          userId,
          fromVSCode,
          contentLength: content?.length,
          isIncrementalUpdate,
          currentDocumentId,
          hasActiveEditor: !!vscode.window.activeTextEditor,
          activeEditorFileName:
            vscode.window.activeTextEditor?.document?.fileName,
        });

        if (userId === socket.id) {
          console.log('Ignoring our own changes');
          return; // Ignore our own changes
        }

        isProcessingRemoteChange = true;

        try {
          // Apply changes to the current document
          const editor = vscode.window.activeTextEditor;
          if (editor) {
            const document = editor.document;

            // Check if this update is for the currently active document
            // We should apply updates if we have a current document ID or if this is a .qmd file
            const isQmdFile =
              document.fileName.endsWith('.qmd') ||
              document.languageId === 'markdown';

            console.log('Document matching check:', {
              currentDocumentId,
              isQmdFile,
              fileName: document.fileName,
              languageId: document.languageId,
            });

            if (!currentDocumentId && !isQmdFile) {
              console.log('Update not for current document, skipping');
              return;
            }

            // If we don't have a current document ID but this is a .qmd file,
            // try to auto-join the document
            if (!currentDocumentId && isQmdFile) {
              console.log(
                'Attempting to auto-join document for content update'
              );
              const fileName = path.basename(document.fileName, '.qmd');
              try {
                const documentId = await findDocumentIdByName(fileName);
                if (documentId) {
                  console.log(
                    `Auto-joining document for update: ${fileName} (ID: ${documentId})`
                  );
                  socket.emit('join-document', { documentId });
                  currentDocumentId = documentId;
                }
              } catch (error) {
                console.error('Error auto-joining document:', error);
              }
            }

            if (isIncrementalUpdate && changes && changes.length > 0) {
              // Apply changes incrementally
              console.log('Applying incremental changes:', changes.length);

              const edit = new vscode.WorkspaceEdit();

              // Process each change
              for (const change of changes) {
                try {
                  let startPos, endPos, text;

                  // Handle different change formats
                  if (change.range && change.range.start && change.range.end) {
                    // VSCode format
                    const start = change.range.start;
                    const end = change.range.end;
                    if (
                      typeof start.line !== 'number' ||
                      typeof start.character !== 'number' ||
                      typeof end.line !== 'number' ||
                      typeof end.character !== 'number'
                    ) {
                      console.error(
                        'Invalid VSCode range properties:',
                        change.range
                      );
                      continue;
                    }
                    startPos = new vscode.Position(start.line, start.character);
                    endPos = new vscode.Position(end.line, end.character);
                    text = change.text || '';
                  } else if (
                    typeof change.fromA === 'number' &&
                    typeof change.toA === 'number'
                  ) {
                    // Offset-based format (like from CodeMirror or similar editors)
                    const document = editor.document;
                    startPos = document.positionAt(change.fromA);
                    endPos = document.positionAt(change.toA);
                    text = change.inserted || '';
                    console.log(
                      `Converted offset change: ${change.fromA}-${change.toA} -> line ${startPos.line}:${startPos.character} - line ${endPos.line}:${endPos.character}`
                    );
                  } else {
                    console.error('Invalid change structure:', change);
                    continue;
                  }

                  // Apply the change
                  const range = new vscode.Range(startPos, endPos);
                  edit.replace(document.uri, range, text);
                  console.log(
                    `Applied change: ${startPos.line}:${startPos.character}-${endPos.line}:${endPos.character} -> "${text}"`
                  );
                } catch (changeError) {
                  console.error(
                    'Error applying incremental change:',
                    changeError
                  );
                  console.error(
                    'Change object:',
                    JSON.stringify(change, null, 2)
                  );
                  // Fall back to full content replacement on error
                  console.log(
                    'Falling back to full content replacement due to incremental change error'
                  );
                  const fullRange = new vscode.Range(
                    document.positionAt(0),
                    document.positionAt(document.getText().length)
                  );
                  edit.replace(document.uri, fullRange, content);
                  break;
                }
              }

              await vscode.workspace.applyEdit(edit);
            } else {
              // Fall back to replacing the entire content
              console.log('Applying full content update');
              const edit = new vscode.WorkspaceEdit();
              const fullRange = new vscode.Range(
                document.positionAt(0),
                document.positionAt(document.getText().length)
              );
              edit.replace(document.uri, fullRange, content);

              await vscode.workspace.applyEdit(edit);
            }

            // Show notification if update came from web editor
            if (!fromVSCode) {
              vscode.window.setStatusBarMessage(
                'Document updated from web editor',
                3000
              );
            }
          } else {
            console.log('No active editor found');
          }
        } catch (error) {
          console.error('Error applying remote changes:', error);
          console.error('Error details:', {
            message: error.message,
            stack: error.stack,
            contentLength: content?.length,
            changesCount: changes?.length,
            fromVSCode,
            isIncrementalUpdate,
          });
          vscode.window.showErrorMessage(
            `Error applying remote changes: ${error.message}`
          );
        } finally {
          isProcessingRemoteChange = false;
        }
      }
    );

    socket.on('remote-cursor', (cursor) => {
      console.log('Received remote cursor update:', cursor);
      if (cursor && cursor.position) {
        updateRemoteCursor(cursor);
      } else {
        console.error('Invalid cursor data received:', cursor);
      }
    });

    socket.on('cursor-sent', (data) => {
      console.log('Cursor sent confirmation:', data);
    });

    socket.on('debug-cursor', (data) => {
      console.log('Debug cursor data:', data);
    });

    socket.on('request-cursor-position', () => {
      console.log('Cursor position requested by server');
      // Send current cursor position if we have an active editor
      const editor = vscode.window.activeTextEditor;
      if (editor && currentDocumentId) {
        const selection = editor.selection;
        const position = selection.active;

        console.log(
          'Sending requested cursor position:',
          position.line,
          position.character
        );

        socket.emit('cursor-update', {
          documentId: currentDocumentId,
          position: {
            line: position.line,
            character: position.character,
          },
        });
      }
    });

    socket.on('user-joined', (userData) => {
      console.log('User joined document:', userData);
      vscode.window.showInformationMessage(
        `${userData.username} joined the document`
      );
      // Request updated user list
      socket.emit('get-document-users', { documentId: currentDocumentId });
    });

    socket.on('user-left', ({ userId }) => {
      console.log('User left document:', userId);
      // Remove the cursor when a user leaves
      removeRemoteCursor(userId);
      // Request updated user list
      socket.emit('get-document-users', { documentId: currentDocumentId });
    });

    // Handle document users list
    socket.on('document-users', (data) => {
      console.log('Received document users:', data);
      if (data.users) {
        updateActiveUsers(data.users);
      }
    });

    // Handle active users updates (from server)
    socket.on('active-users-updated', (users) => {
      console.log('Received active users update:', users);
      updateActiveUsers(users);
    });

    // Handle pending changes
    socket.on('pending-changes', ({ changes }) => {
      console.log('Received pending changes update:', changes.length);
      pendingChanges = changes;

      // Show notification if there are pending changes
      if (changes.length > 0) {
        vscode.window.setStatusBarMessage(
          `${changes.length} pending changes`,
          3000
        );
      }
    });

    // Handle cursor sent confirmation
    socket.on('cursor-sent', (data) => {
      console.log('Cursor sent confirmation:', data);
    });

    // Handle debug cursor response
    socket.on('debug-cursor-response', (data) => {
      console.log('Debug cursor response:', data);
    });

    // Handle pending change
    socket.on('pending-change', (change) => {
      console.log('Received new pending change:', change);

      // Add to pending changes list if not already there
      const existingIndex = pendingChanges.findIndex((c) => c.id === change.id);
      if (existingIndex >= 0) {
        pendingChanges[existingIndex] = change;
      } else {
        pendingChanges.push(change);
      }

      // Show notification
      vscode.window.setStatusBarMessage(
        `New change from ${change.userName}`,
        3000
      );
    });

    // Handle change rejected
    socket.on('change-rejected', ({ changeId }) => {
      console.log('Change rejected:', changeId);

      // Remove from pending changes list
      pendingChanges = pendingChanges.filter((c) => c.id !== changeId);

      // Show notification
      vscode.window.setStatusBarMessage('Change rejected', 3000);
    });
  } catch (error) {
    statusBarItem.text = '$(error) Collaborative Quarto: Error';
    vscode.window.showErrorMessage(`Failed to connect: ${error.message}`);
  }
}

/**
 * Disconnect from the collaborative server
 */
function disconnectFromServer() {
  if (socket) {
    socket.disconnect();
    socket = null;
    statusBarItem.text = '$(plug) Collaborative Quarto: Disconnected';
    vscode.window.showInformationMessage(
      'Disconnected from Collaborative Quarto server'
    );
    stopCursorUpdates();
    clearRemoteCursors();
    currentDocumentId = null;
    sessionId = null; // Clear session ID
  }
}

/**
 * Select a document to edit
 */
async function selectDocument() {
  if (!socket) {
    vscode.window.showErrorMessage('Please connect to the server first');
    return;
  }

  try {
    console.log('Requesting document list from server...');

    // Try to get documents via public socket event first
    console.log('Requesting public document list from server...');
    socket.emit('get-public-documents');

    // Wait for the document list
    const documents = await new Promise((resolve) => {
      const handler = (docs) => {
        console.log('Received document list:', docs);
        resolve(docs);
      };

      socket.once('document-list', handler);

      // Timeout after 3 seconds and try authenticated request
      setTimeout(() => {
        socket.off('document-list', handler);
        console.log(
          'Timeout waiting for public document list, trying authenticated request...'
        );

        // Try authenticated request
        socket.emit('get-documents');

        // Wait for response with another timeout
        const authHandler = (docs) => {
          console.log('Received authenticated document list:', docs);
          resolve(docs);
        };

        socket.once('document-list', authHandler);

        // Final timeout
        setTimeout(() => {
          socket.off('document-list', authHandler);
          console.log('Timeout waiting for authenticated document list');
          resolve([]);
        }, 3000);
      }, 3000);
    });

    if (!documents || documents.length === 0) {
      console.log('No documents received from server');

      // Try to fetch documents via REST API as fallback
      try {
        const config = vscode.workspace.getConfiguration(
          'collaborative-quarto'
        );
        const serverUrl = config.get('serverUrl');

        console.log('Trying to fetch documents via REST API...');

        // First, check if the server is accessible and has documents
        try {
          console.log('Checking debug endpoint...');
          const debugResponse = await fetch(`${serverUrl}/api/debug/documents`);
          const debugText = await debugResponse.text();
          console.log(
            'Debug endpoint response:',
            debugText.substring(0, 100) + '...'
          );
        } catch (debugError) {
          console.error('Error accessing debug endpoint:', debugError);
        }

        // Try the public endpoint
        console.log('Trying to fetch documents via public API endpoint...');
        const response = await fetch(`${serverUrl}/api/documents/public-list`);
        const responseText = await response.text();

        try {
          // Try to parse the response as JSON
          const data = JSON.parse(responseText);
          console.log('Public API response parsed:', data);

          if (data.documents && data.documents.length > 0) {
            console.log(
              'Received documents via public REST API:',
              data.documents
            );
            return showDocumentPicker(data.documents);
          }
        } catch (parseError) {
          console.error('Error parsing public API response:', parseError);
          console.log('Raw response:', responseText);
        }

        // If public endpoint fails, try the authenticated endpoint
        console.log(
          'Trying to fetch documents via authenticated API endpoint...'
        );
        const authResponse = await fetch(`${serverUrl}/api/documents/list`);
        const authResponseText = await authResponse.text();

        try {
          // Try to parse the response as JSON
          const authData = JSON.parse(authResponseText);
          console.log('Authenticated API response parsed:', authData);

          if (authData.documents && authData.documents.length > 0) {
            console.log(
              'Received documents via authenticated REST API:',
              authData.documents
            );
            return showDocumentPicker(authData.documents);
          }
        } catch (parseError) {
          console.error(
            'Error parsing authenticated API response:',
            parseError
          );
          console.log('Raw response:', authResponseText);
        }
      } catch (apiError) {
        console.error('Error fetching documents via API:', apiError);
      }

      vscode.window.showErrorMessage(
        'No documents available. Please create a document in the web interface first.'
      );
      return;
    }

    return showDocumentPicker(documents);
  } catch (error) {
    console.error('Error selecting document:', error);
    vscode.window.showErrorMessage(
      `Error selecting document: ${error.message}`
    );
  }
}

/**
 * Show document picker and join selected document
 * @param {Array} documents List of documents
 */
async function showDocumentPicker(documents) {
  // Add a special item to create a new document
  const documentItems = [
    {
      label: '$(new-file) Create New Document',
      description: 'Create a new collaborative document',
      id: 'create-new',
    },
    ...documents.map((doc) => ({
      label: doc.name,
      description: `ID: ${doc.id}`,
      id: doc.id,
    })),
  ];

  const selected = await vscode.window.showQuickPick(documentItems, {
    placeHolder: 'Select a document to edit',
  });

  if (selected) {
    if (selected.id === 'create-new') {
      // Create a new document
      await createDocument();
    } else {
      console.log('Selected document:', selected.id);
      // Join the selected document
      socket.emit('join-document', { documentId: selected.id });
      currentDocumentId = selected.id;
    }
  }
}

/**
 * Create a new collaborative document
 */
async function createDocument() {
  if (!socket) {
    const connectFirst = await vscode.window.showInformationMessage(
      'You need to connect to the server first. Connect now?',
      'Connect',
      'Cancel'
    );

    if (connectFirst === 'Connect') {
      await connectToServer();
    } else {
      return;
    }
  }

  // Get document name from user
  const documentName = await vscode.window.showInputBox({
    prompt: 'Enter a name for the new document',
    placeHolder: 'Document name',
  });

  if (!documentName) return; // User cancelled

  try {
    // Get server URL from configuration
    const config = vscode.workspace.getConfiguration('collaborative-quarto');
    const serverUrl = config.get('serverUrl');

    console.log(`Creating new document: ${documentName}`);

    // Create initial content
    const initialContent = `# ${documentName}\n\nCreated from VS Code on ${new Date().toLocaleString()}\n`;

    // Create document via API
    const response = await fetch(`${serverUrl}/api/documents/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: documentName,
        content: initialContent,
      }),
    });

    const data = await response.json();

    if (data.success && data.document) {
      console.log('Document created successfully:', data.document);

      // Join the new document
      socket.emit('join-document', { documentId: data.document.id });
      currentDocumentId = data.document.id;

      vscode.window.showInformationMessage(
        `Document "${documentName}" created successfully!`
      );
    } else {
      throw new Error(data.error || 'Unknown error creating document');
    }
  } catch (error) {
    console.error('Error creating document:', error);
    vscode.window.showErrorMessage(`Error creating document: ${error.message}`);
  }
}

/**
 * Open or update a document in the editor
 * @param {string} documentId
 * @param {string} documentName - Not used, kept for API compatibility
 * @param {string} content
 */
async function openOrUpdateDocument(documentId, documentName, content) {
  try {
    let filePath = null;

    // First, try to find the document in the sync folder
    if (syncFolderPath && currentProjectId) {
      const syncedFilePath = await findSyncedFilePath(documentId, documentName);
      if (syncedFilePath) {
        filePath = syncedFilePath;
        console.log(`Found synced file: ${filePath}`);
      }
    }

    // If not found in sync folder, create a temporary file (fallback)
    if (!filePath) {
      const tempDir =
        vscode.workspace.workspaceFolders?.[0]?.uri.fsPath ||
        require('os').tmpdir();
      filePath = path.join(tempDir, `${documentName || documentId}.qmd`);
      console.log(`Using temporary file: ${filePath}`);
    }

    console.log(
      `Opening/updating document ${documentId} (${documentName}) at ${filePath}`
    );

    // Check if file exists and if content is different
    let needsUpdate = true;
    if (fs.existsSync(filePath)) {
      const existingContent = fs.readFileSync(filePath, 'utf8');
      needsUpdate = existingContent !== content;
    }

    // Write content to the file only if it's different
    if (needsUpdate) {
      await fs.promises.writeFile(filePath, content, 'utf8');
      console.log(`Updated file content (${content.length} characters)`);
    }

    // Open the file in the editor
    const document = await vscode.workspace.openTextDocument(filePath);
    await vscode.window.showTextDocument(document);

    // Store the mapping between file and document ID
    if (extensionContext) {
      extensionContext.workspaceState.update('documentMapping', {
        ...extensionContext.workspaceState.get('documentMapping', {}),
        [document.uri.toString()]: documentId,
      });
      console.log(`Stored mapping for document ${documentId}`);
    }
  } catch (error) {
    console.error('Error opening document:', error);
    vscode.window.showErrorMessage(`Error opening document: ${error.message}`);
  }
}

/**
 * Update the content of the currently active document
 */
async function updateActiveDocumentContent(content) {
  const activeEditor = vscode.window.activeTextEditor;
  if (!activeEditor) {
    console.log('No active editor to update');
    return;
  }

  try {
    const document = activeEditor.document;
    const currentContent = document.getText();

    if (currentContent === content) {
      console.log('Content is already up to date');
      return;
    }

    // Create a workspace edit to replace the entire document content
    const edit = new vscode.WorkspaceEdit();
    const fullRange = new vscode.Range(
      document.positionAt(0),
      document.positionAt(currentContent.length)
    );

    edit.replace(document.uri, fullRange, content);

    // Apply the edit
    const success = await vscode.workspace.applyEdit(edit);
    if (success) {
      console.log('Successfully updated document content');
    } else {
      console.error('Failed to apply content update');
    }
  } catch (error) {
    console.error('Error updating document content:', error);
  }
}

/**
 * Find the synced file path for a document
 */
async function findSyncedFilePath(documentId, documentName) {
  if (!syncFolderPath || !fs.existsSync(syncFolderPath)) {
    return null;
  }

  // First, try to find by document name
  if (documentName) {
    const nameBasedPath = path.join(syncFolderPath, `${documentName}.qmd`);
    if (fs.existsSync(nameBasedPath)) {
      console.log(`Found file by name: ${nameBasedPath}`);
      return nameBasedPath;
    }

    // Also try without .qmd extension if the name already includes it
    if (documentName.endsWith('.qmd')) {
      const cleanNamePath = path.join(syncFolderPath, documentName);
      if (fs.existsSync(cleanNamePath)) {
        console.log(`Found file by clean name: ${cleanNamePath}`);
        return cleanNamePath;
      }
    }
  }

  // If not found by name, search through all .qmd files in sync folder
  try {
    const files = fs.readdirSync(syncFolderPath, { withFileTypes: true });

    for (const file of files) {
      if (file.isFile() && file.name.endsWith('.qmd')) {
        const fullPath = path.join(syncFolderPath, file.name);
        const baseName = path.basename(file.name, '.qmd');

        // Check if the base name matches the document name
        if (baseName === documentName || baseName === documentId) {
          console.log(`Found file by search: ${fullPath}`);
          return fullPath;
        }
      }
    }

    // Also search in subdirectories (one level deep)
    for (const file of files) {
      if (file.isDirectory()) {
        const subDir = path.join(syncFolderPath, file.name);
        const subFiles = fs.readdirSync(subDir, { withFileTypes: true });

        for (const subFile of subFiles) {
          if (subFile.isFile() && subFile.name.endsWith('.qmd')) {
            const fullPath = path.join(subDir, subFile.name);
            const baseName = path.basename(subFile.name, '.qmd');

            if (baseName === documentName || baseName === documentId) {
              console.log(`Found file in subdirectory: ${fullPath}`);
              return fullPath;
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('Error searching for synced file:', error);
  }

  console.log(
    `No synced file found for document: ${documentName} (ID: ${documentId})`
  );
  return null;
}

/**
 * Handle local document changes
 * @param {vscode.TextDocumentChangeEvent} event
 */
function handleLocalDocumentChange(event) {
  try {
    console.log('handleLocalDocumentChange called');
    console.log('Socket connected:', !!socket);
    console.log('Current document ID:', currentDocumentId);
    console.log('Processing remote change:', isProcessingRemoteChange);

    if (!socket || !currentDocumentId || isProcessingRemoteChange) {
      console.log('Skipping document change - missing requirements');
      return;
    }

    const document = event.document;
    console.log('Document file name:', document.fileName);

    // Only process .qmd files
    if (!document.fileName.endsWith('.qmd')) {
      console.log('Skipping non-qmd file');
      return;
    }

    // Check if this document is from our sync folder (if we have one)
    let isCurrentDocument = false;

    if (syncFolderPath) {
      // For synced files, check if the file is in our sync folder
      const relativePath = path.relative(syncFolderPath, document.fileName);
      isCurrentDocument =
        !relativePath.startsWith('..') && !path.isAbsolute(relativePath);
      console.log(
        `File is in sync folder: ${isCurrentDocument} (relative path: ${relativePath})`
      );
    } else {
      // For non-synced files, check if filename matches document ID
      const fileName = path.basename(document.fileName);
      const expectedFileName = `${currentDocumentId}.qmd`;
      isCurrentDocument = fileName === expectedFileName;
      console.log(
        `File matches document ID: ${isCurrentDocument} (${fileName} vs ${expectedFileName})`
      );
    }

    if (!isCurrentDocument) {
      console.log(
        `Ignoring changes to non-current document: ${document.fileName}`
      );
      return;
    }

    console.log('Local document changed, sending update to server');

    // Extract changes
    const changes = event.contentChanges.map((change) => ({
      range: {
        start: {
          line: change.range.start.line,
          character: change.range.start.character,
        },
        end: {
          line: change.range.end.line,
          character: change.range.end.character,
        },
      },
      text: change.text,
    }));

    console.log('Number of changes:', changes.length);

    if (changes.length === 0) return;

    // Send changes to server
    const updateData = {
      documentId: currentDocumentId,
      content: document.getText(),
      changes,
      trackChangesMode,
      isIncrementalUpdate: true,
    };

    console.log('Sending update-content to server:', {
      documentId: currentDocumentId,
      contentLength: updateData.content.length,
    });
    socket.emit('update-content', updateData);
  } catch (error) {
    console.error('Error handling local document change:', error);
  }
}

/**
 * Handle local selection changes
 * @param {vscode.TextEditorSelectionChangeEvent} event
 */
function handleLocalSelectionChange(event) {
  try {
    if (!socket || !currentDocumentId) {
      console.log('Selection change ignored - no socket or document ID');
      return;
    }

    const editor = event.textEditor;

    // Only process .qmd files
    if (!editor.document.fileName.endsWith('.qmd')) {
      console.log('Selection change ignored - not a .qmd file');
      return;
    }

    // Check if this document is the current collaborative document
    let isCurrentDocument = false;

    if (syncFolderPath) {
      // For synced files, check if the file is in our sync folder
      const relativePath = path.relative(
        syncFolderPath,
        editor.document.fileName
      );
      isCurrentDocument =
        !relativePath.startsWith('..') && !path.isAbsolute(relativePath);
      console.log(
        `Selection change - file is in sync folder: ${isCurrentDocument}`
      );
    } else {
      // For non-synced files, check if filename matches document ID
      const fileName = path.basename(editor.document.fileName);
      const expectedFileName = `${currentDocumentId}.qmd`;
      isCurrentDocument = fileName === expectedFileName;
      console.log(
        `Selection change - file matches document ID: ${isCurrentDocument} (${fileName} vs ${expectedFileName})`
      );
    }

    if (!isCurrentDocument) {
      console.log(
        `Selection change ignored - not current document: ${editor.document.fileName}`
      );
      return;
    }

    // Get the primary selection
    const selection = editor.selection;
    const position = selection.active;

    console.log(
      `VS Code cursor moved to line ${position.line}, character ${position.character}`
    );

    // Send cursor position to server
    const cursorData = {
      documentId: currentDocumentId,
      position: {
        line: position.line,
        character: position.character,
      },
    };

    console.log('Sending cursor update to server:', cursorData);
    socket.emit('cursor-update', cursorData);

    // Also emit a debug event to help diagnose issues
    socket.emit('debug-cursor', {
      documentId: currentDocumentId,
      position: {
        line: position.line,
        character: position.character,
      },
      fileName: fileName,
      socketId: socket.id,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error handling local selection change:', error);
  }
}

/**
 * Handle active editor changes - auto-join documents from synced project
 */
async function handleActiveEditorChange(editor) {
  try {
    if (!editor || !socket || !socket.connected) {
      return;
    }

    const document = editor.document;
    if (!document.fileName.endsWith('.qmd')) {
      return;
    }

    console.log('Active editor changed to .qmd file:', document.fileName);

    // If we have a sync folder, check if this file is in it
    if (syncFolderPath && !document.fileName.startsWith(syncFolderPath)) {
      console.log('File not in sync folder, skipping auto-join');
      return;
    }

    // Get the file name to search for
    let fileName;
    if (syncFolderPath) {
      const relativePath = path.relative(syncFolderPath, document.fileName);
      fileName = path.basename(relativePath, '.qmd');
      console.log(`Active editor changed to synced file: ${fileName}`);
    } else {
      fileName = path.basename(document.fileName, '.qmd');
      console.log(`Active editor changed to .qmd file: ${fileName}`);
    }

    try {
      // Find the document ID by name
      const documentId = await findDocumentIdByName(fileName);
      if (documentId && documentId !== currentDocumentId) {
        console.log(`Auto-joining document: ${fileName} (ID: ${documentId})`);

        // Join the document
        socket.emit('join-document', { documentId });
        currentDocumentId = documentId;

        // Register the file mapping with the server (for VS Code sync)
        if (syncFolderPath) {
          const relativePath = path.relative(syncFolderPath, document.fileName);
          const syncFilePath = path.join(syncFolderPath, relativePath);
          console.log(
            `Registering VS Code file mapping: ${documentId} -> ${syncFilePath}`
          );
          socket.emit('register-vscode-file', {
            documentId,
            filePath: syncFilePath,
          });
        }

        // Request user list for this document
        socket.emit('get-document-users', { documentId });

        statusBarItem.text = `$(check) Collaborative Quarto: Editing "${fileName}"`;
      }
    } catch (error) {
      console.error('Error auto-joining document:', error);
    }
  } catch (error) {
    console.error('Error handling active editor change:', error);
  }
}

/**
 * Find document ID by name in current project
 */
async function findDocumentIdByName(fileName) {
  if (!currentProjectId || !sessionId) {
    return null;
  }

  try {
    const config = vscode.workspace.getConfiguration('collaborative-quarto');
    const serverUrl = config.get('serverUrl', 'http://localhost:3000');

    const response = await fetch(
      `${serverUrl}/api/projects/${currentProjectId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Cookie: `sessionId=${sessionId}`,
        },
        credentials: 'include',
      }
    );

    if (response.ok) {
      const projectData = await response.json();
      const document = projectData.structure.documents.find(
        (doc) => doc.name === fileName
      );
      return document ? document.id : null;
    }
  } catch (error) {
    console.error('Error finding document ID:', error);
  }

  return null;
}

/**
 * Update active users display
 */
function updateActiveUsers(users) {
  activeUsers = users || [];

  const userCount = activeUsers.length;
  const userNames = activeUsers
    .map((user) => user.username || user.displayName || 'Unknown')
    .join(', ');

  if (userCount === 0) {
    usersStatusBarItem.text = '$(person) Users: 0';
    usersStatusBarItem.tooltip = 'No active users';
  } else if (userCount === 1) {
    usersStatusBarItem.text = `$(person) Users: 1`;
    usersStatusBarItem.tooltip = `Active user: ${userNames}`;
  } else {
    usersStatusBarItem.text = `$(people) Users: ${userCount}`;
    usersStatusBarItem.tooltip = `Active users: ${userNames}`;
  }

  console.log(`Updated active users: ${userCount} users (${userNames})`);
}

/**
 * Toggle track changes mode
 */
async function toggleTrackChanges() {
  if (!socket || !currentDocumentId) {
    vscode.window.showErrorMessage('Please connect to a document first');
    return;
  }

  // Toggle track changes mode
  trackChangesMode = !trackChangesMode;

  // Update status bar
  trackChangesStatusBarItem.text = `$(diff) Track Changes: ${
    trackChangesMode ? 'ON' : 'OFF'
  }`;

  // Show notification
  vscode.window.showInformationMessage(
    `Track changes mode ${trackChangesMode ? 'enabled' : 'disabled'}`
  );

  // If enabling track changes, get pending changes
  if (trackChangesMode) {
    socket.emit('get-pending-changes', { documentId: currentDocumentId });
  }
}

/**
 * Show pending changes
 */
async function showPendingChanges() {
  if (!socket || !currentDocumentId) {
    vscode.window.showErrorMessage('Please connect to a document first');
    return;
  }

  // Request pending changes
  socket.emit('get-pending-changes', { documentId: currentDocumentId });

  // Wait for the pending changes
  const changes = await new Promise((resolve) => {
    const handler = ({ changes }) => {
      console.log('Received pending changes:', changes);
      resolve(changes);
    };

    socket.once('pending-changes', handler);

    // Timeout after 5 seconds
    setTimeout(() => {
      socket.off('pending-changes', handler);
      console.log('Timeout waiting for pending changes');
      resolve([]);
    }, 5000);
  });

  if (!changes || changes.length === 0) {
    vscode.window.showInformationMessage('No pending changes');
    return;
  }

  // Show pending changes in a quick pick
  const changeItems = changes.map((change) => ({
    label: `${change.userName}: ${change.insertedText.substring(0, 30)}${
      change.insertedText.length > 30 ? '...' : ''
    }`,
    description: `at position ${change.fromPos}`,
    detail: change.createdAt,
    change,
  }));

  const selected = await vscode.window.showQuickPick(changeItems, {
    placeHolder: 'Select a change to accept or reject',
    canPickMany: false,
  });

  if (selected) {
    const actions = ['Accept', 'Reject', 'Cancel'];
    const action = await vscode.window.showQuickPick(actions, {
      placeHolder: `What do you want to do with this change?`,
    });

    if (action === 'Accept') {
      socket.emit('accept-change', { changeId: selected.change.id });
    } else if (action === 'Reject') {
      socket.emit('reject-change', { changeId: selected.change.id });
    }
  }
}

/**
 * Show version history
 */
async function showVersionHistory() {
  if (!socket || !currentDocumentId) {
    vscode.window.showErrorMessage('Please connect to a document first');
    return;
  }

  // Request version history
  socket.emit('get-document-versions', { documentId: currentDocumentId });

  // Wait for the version history
  const versions = await new Promise((resolve) => {
    const handler = ({ versions }) => {
      console.log('Received document versions:', versions);
      resolve(versions);
    };

    socket.once('document-versions', handler);

    // Timeout after 5 seconds
    setTimeout(() => {
      socket.off('document-versions', handler);
      console.log('Timeout waiting for document versions');
      resolve([]);
    }, 5000);
  });

  if (!versions || versions.length === 0) {
    vscode.window.showInformationMessage('No version history available');
    return;
  }

  // Show versions in a quick pick
  const versionItems = versions.map((version) => ({
    label: version.name || `Version ${version.id}`,
    description: `by ${version.userName}`,
    detail: version.createdAt,
    version,
  }));

  const selected = await vscode.window.showQuickPick(versionItems, {
    placeHolder: 'Select a version to restore',
    canPickMany: false,
  });

  if (selected) {
    const confirm = await vscode.window.showQuickPick(['Yes', 'No'], {
      placeHolder: `Restore to version "${selected.label}"?`,
    });

    if (confirm === 'Yes') {
      socket.emit('restore-version', { versionId: selected.version.id });

      // Wait for confirmation
      const result = await new Promise((resolve) => {
        const handler = (result) => {
          console.log('Version restore result:', result);
          resolve(result);
        };

        socket.once('version-restored', handler);

        // Timeout after 5 seconds
        setTimeout(() => {
          socket.off('version-restored', handler);
          console.log('Timeout waiting for version restore result');
          resolve({ success: false });
        }, 5000);
      });

      if (result.success) {
        vscode.window.showInformationMessage('Version restored successfully');
      } else {
        vscode.window.showErrorMessage(
          `Failed to restore version: ${result.error || 'Unknown error'}`
        );
      }
    }
  }
}

/**
 * Start periodic cursor updates
 */
function startCursorUpdates() {
  // Clear any existing interval
  if (cursorUpdateInterval) {
    clearInterval(cursorUpdateInterval);
  }

  // Set up a new interval to send cursor updates every 2 seconds
  cursorUpdateInterval = setInterval(() => {
    if (!socket || !currentDocumentId) return;

    const editor = vscode.window.activeTextEditor;
    if (!editor) return;

    // Only process .qmd files
    if (!editor.document.fileName.endsWith('.qmd')) return;

    // Check if this is the current document
    let isCurrentDocument = false;

    if (syncFolderPath) {
      // For synced files, check if the file is in our sync folder
      const relativePath = path.relative(
        syncFolderPath,
        editor.document.fileName
      );
      isCurrentDocument =
        !relativePath.startsWith('..') && !path.isAbsolute(relativePath);
    } else {
      // For non-synced files, check if filename matches document ID
      const fileName = path.basename(editor.document.fileName);
      const expectedFileName = `${currentDocumentId}.qmd`;
      isCurrentDocument = fileName === expectedFileName;
    }

    if (!isCurrentDocument) return;

    // Get the primary selection
    const selection = editor.selection;
    const position = selection.active;

    console.log(
      'Sending periodic cursor update:',
      position.line,
      position.character
    );

    // Send cursor position to server
    socket.emit('cursor-update', {
      documentId: currentDocumentId,
      position: {
        line: position.line,
        character: position.character,
      },
    });
  }, 2000); // Update every 2 seconds

  console.log('Started periodic cursor updates');
}

/**
 * Stop periodic cursor updates
 */
function stopCursorUpdates() {
  if (cursorUpdateInterval) {
    clearInterval(cursorUpdateInterval);
    cursorUpdateInterval = null;
    console.log('Stopped periodic cursor updates');
  }
}

/**
 * Setup project - select project and sync folder
 */
async function setupProject() {
  try {
    // First, connect to server if not connected
    if (!socket || !socket.connected) {
      vscode.window.showInformationMessage('Connecting to server...');
      await connectToServer();

      // Wait a bit for authentication to complete
      await new Promise((resolve) => setTimeout(resolve, 2000));

      if (!socket || !socket.connected) {
        throw new Error('Failed to connect to server');
      }
    }

    vscode.window.showInformationMessage('Loading projects...');

    // Get list of projects from server
    const projects = await getProjectList();

    if (!projects || projects.length === 0) {
      vscode.window.showInformationMessage(
        'No projects found. Please create a project in the web interface first.'
      );
      return;
    }

    // Show project selection
    const projectItems = projects.map((project) => ({
      label: project.name,
      description: project.description || 'No description',
      detail: `Created: ${new Date(project.created_at).toLocaleDateString()}`,
      project: project,
    }));

    const selectedProject = await vscode.window.showQuickPick(projectItems, {
      placeHolder: 'Select a project to sync with',
    });

    if (!selectedProject) {
      return;
    }

    currentProjectId = selectedProject.project.id;

    // Now select sync folder
    await selectSyncFolder();

    // Save project configuration
    await saveProjectConfig(selectedProject.project);

    // Create metadata file in sync folder
    await createProjectMetadata(selectedProject.project);

    // Start project sync
    await startProjectSync(selectedProject.project);

    vscode.window.showInformationMessage(
      `Project "${selectedProject.project.name}" setup complete! Files will be synced to your selected folder.`
    );
  } catch (error) {
    console.error('Error setting up project:', error);
    vscode.window.showErrorMessage(
      `Error setting up project: ${error.message}`
    );
  }
}

/**
 * Select sync folder
 */
async function selectSyncFolder() {
  // First try the standard folder picker
  const folderUri = await vscode.window.showOpenDialog({
    canSelectFiles: false,
    canSelectFolders: true,
    canSelectMany: false,
    openLabel: 'Select Sync Folder',
  });

  if (folderUri && folderUri[0]) {
    syncFolderPath = folderUri[0].fsPath;
  } else {
    // If user cancelled, offer to create a new folder
    const createNew = await vscode.window.showQuickPick(
      [
        {
          label: 'Create New Folder',
          description: 'Create a new folder for project sync',
        },
        { label: 'Cancel', description: 'Cancel folder selection' },
      ],
      {
        placeHolder: 'No folder selected. Would you like to create a new one?',
      }
    );

    if (createNew && createNew.label === 'Create New Folder') {
      // Get folder name
      const folderName = await vscode.window.showInputBox({
        prompt: 'Enter name for new sync folder',
        placeHolder: 'my-project-sync',
        validateInput: (value) => {
          if (!value || value.trim().length === 0) {
            return 'Folder name cannot be empty';
          }
          // Check for invalid characters
          if (!/^[a-zA-Z0-9_\-\s]+$/.test(value)) {
            return 'Folder name contains invalid characters';
          }
          return null;
        },
      });

      if (!folderName) {
        return; // User cancelled
      }

      // Get parent directory
      const parentUri = await vscode.window.showOpenDialog({
        canSelectFiles: false,
        canSelectFolders: true,
        canSelectMany: false,
        openLabel: 'Select Parent Directory',
      });

      if (!parentUri || !parentUri[0]) {
        return; // User cancelled
      }

      // Create the new folder
      syncFolderPath = path.join(parentUri[0].fsPath, folderName.trim());

      try {
        if (!fs.existsSync(syncFolderPath)) {
          fs.mkdirSync(syncFolderPath, { recursive: true });
          console.log(`Created new sync folder: ${syncFolderPath}`);
        }
      } catch (error) {
        vscode.window.showErrorMessage(
          `Failed to create folder: ${error.message}`
        );
        return;
      }
    } else {
      return; // User cancelled
    }
  }

  if (syncFolderPath) {
    // Save to workspace settings
    const config = vscode.workspace.getConfiguration('collaborative-quarto');
    await config.update(
      'syncFolderPath',
      syncFolderPath,
      vscode.ConfigurationTarget.Workspace
    );

    console.log(`Sync folder set to: ${syncFolderPath}`);
    vscode.window.showInformationMessage(
      `Sync folder set to: ${syncFolderPath}`
    );
  }
}

/**
 * Open project folder in VS Code
 */
async function openProjectFolder() {
  const config = vscode.workspace.getConfiguration('collaborative-quarto');
  const savedSyncPath = config.get('syncFolderPath');

  if (savedSyncPath && fs.existsSync(savedSyncPath)) {
    const uri = vscode.Uri.file(savedSyncPath);
    await vscode.commands.executeCommand('vscode.openFolder', uri);
  } else {
    vscode.window.showWarningMessage(
      'No sync folder configured. Please run "Setup Project" first.'
    );
  }
}

/**
 * Get project list from server
 */
async function getProjectList() {
  try {
    // First ensure we're authenticated
    if (!socket || !socket.connected || !sessionId) {
      throw new Error('Not connected to server. Please connect first.');
    }

    // Get server URL from configuration
    const config = vscode.workspace.getConfiguration('collaborative-quarto');
    const serverUrl = config.get('serverUrl', 'http://localhost:3000');

    console.log('Fetching projects with session ID:', sessionId);

    const response = await fetch(`${serverUrl}/api/projects`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Cookie: `sessionId=${sessionId}`,
      },
      credentials: 'include',
    });

    console.log('Projects API response status:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('Projects fetched successfully:', data.projects?.length || 0);
      return data.projects;
    } else if (response.status === 401) {
      throw new Error(
        'Authentication required. Please connect to the server first.'
      );
    } else {
      const errorText = await response.text();
      console.error('Projects API error:', response.status, errorText);
      throw new Error(`Failed to fetch projects: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Error fetching projects:', error);
    throw error;
  }
}

/**
 * Save project configuration
 */
async function saveProjectConfig(project) {
  const config = vscode.workspace.getConfiguration('collaborative-quarto');
  await config.update(
    'currentProjectId',
    project.id,
    vscode.ConfigurationTarget.Workspace
  );
  await config.update(
    'currentProjectName',
    project.name,
    vscode.ConfigurationTarget.Workspace
  );

  // Also save to global state for persistence
  if (extensionContext) {
    await extensionContext.globalState.update('lastProjectId', project.id);
    await extensionContext.globalState.update('lastProjectName', project.name);
    await extensionContext.globalState.update('lastSyncFolder', syncFolderPath);
  }
}

/**
 * Create project metadata file in sync folder
 */
async function createProjectMetadata(project) {
  if (!syncFolderPath) {
    console.log('No sync folder path, skipping metadata creation');
    return;
  }

  const config = vscode.workspace.getConfiguration('collaborative-quarto');
  const serverUrl = config.get('serverUrl', 'http://localhost:3000');

  const metadata = {
    version: '1.0',
    project: {
      id: project.id,
      name: project.name,
      description: project.description || '',
    },
    server: {
      url: serverUrl,
    },
    sync: {
      folderPath: syncFolderPath,
      lastSync: new Date().toISOString(),
    },
    auth: {
      sessionId: sessionId,
      // Note: We store session ID but it may expire
      // Extension should handle re-authentication gracefully
    },
  };

  const metadataPath = path.join(syncFolderPath, '.quarto-collab.json');

  try {
    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
    console.log(`Created project metadata file: ${metadataPath}`);

    // Also create a .gitignore to exclude the metadata file
    const gitignorePath = path.join(syncFolderPath, '.gitignore');
    const gitignoreContent = '.quarto-collab.json\n';

    if (!fs.existsSync(gitignorePath)) {
      fs.writeFileSync(gitignorePath, gitignoreContent);
      console.log('Created .gitignore file');
    } else {
      // Check if our entry is already there
      const existingContent = fs.readFileSync(gitignorePath, 'utf8');
      if (!existingContent.includes('.quarto-collab.json')) {
        fs.appendFileSync(gitignorePath, gitignoreContent);
        console.log('Added metadata file to .gitignore');
      }
    }
  } catch (error) {
    console.error('Error creating project metadata:', error);
    // Don't throw - this is not critical
  }
}

/**
 * Update project metadata file with current sync time
 */
async function updateProjectMetadata() {
  if (!syncFolderPath) {
    return;
  }

  const metadataPath = path.join(syncFolderPath, '.quarto-collab.json');

  if (!fs.existsSync(metadataPath)) {
    return;
  }

  try {
    const metadataContent = fs.readFileSync(metadataPath, 'utf8');
    const metadata = JSON.parse(metadataContent);

    // Update sync time and session
    metadata.sync.lastSync = new Date().toISOString();
    metadata.auth.sessionId = sessionId;

    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
    console.log('Updated project metadata');
  } catch (error) {
    console.error('Error updating project metadata:', error);
  }
}

/**
 * Start project sync - download all project files to sync folder
 */
async function startProjectSync(project) {
  if (!syncFolderPath) {
    throw new Error('No sync folder configured');
  }

  try {
    console.log(`Starting project sync for: ${project.name || project.id}`);

    // Get project structure from server
    const config = vscode.workspace.getConfiguration('collaborative-quarto');
    const serverUrl = config.get('serverUrl', 'http://localhost:3000');

    const response = await fetch(`${serverUrl}/api/projects/${project.id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Cookie: `sessionId=${sessionId}`,
      },
      credentials: 'include',
    });

    if (!response.ok) {
      if (response.status === 404) {
        // Project not found - clear the configuration
        console.log('Project not found, clearing configuration');
        const config = vscode.workspace.getConfiguration(
          'collaborative-quarto'
        );
        await config.update(
          'currentProjectId',
          undefined,
          vscode.ConfigurationTarget.Workspace
        );
        await config.update(
          'currentProjectName',
          undefined,
          vscode.ConfigurationTarget.Workspace
        );
        currentProjectId = null;
        throw new Error('Project no longer exists or access denied');
      }
      throw new Error(
        `Failed to fetch project structure: ${response.statusText}`
      );
    }

    const projectData = await response.json();
    console.log(
      'Project structure received:',
      JSON.stringify(projectData, null, 2)
    );

    // Check if we have project data
    if (!projectData.project) {
      throw new Error('No project data received from server');
    }

    // Create project folder structure
    console.log('Creating project folders...');
    await createProjectFolders(projectData.structure);

    // Download all documents in the project
    console.log('Downloading project documents...');
    await downloadProjectDocuments(projectData.structure);

    console.log('Project sync completed successfully');

    // Update metadata file with last sync time
    await updateProjectMetadata();
  } catch (error) {
    console.error('Error syncing project:', error);
    throw error;
  }
}

/**
 * Create folder structure for project
 */
async function createProjectFolders(structure) {
  console.log('Creating folders from structure');
  console.log('Project folders:', structure.folders);

  if (!structure.folders || structure.folders.length === 0) {
    console.log('No folders to create');
    return;
  }

  for (const folder of structure.folders) {
    const folderPath = path.join(syncFolderPath, folder.path || folder.name);

    try {
      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true });
        console.log(`Created folder: ${folderPath}`);
      } else {
        console.log(`Folder already exists: ${folderPath}`);
      }
    } catch (error) {
      console.error(`Error creating folder ${folderPath}:`, error);
    }
  }
}

/**
 * Download all documents in project
 */
async function downloadProjectDocuments(structure) {
  console.log('Downloading documents from structure');
  console.log('Project documents:', structure.documents);

  if (!structure.documents || structure.documents.length === 0) {
    console.log('No documents to download');
    return;
  }

  console.log(`Found ${structure.documents.length} documents to download`);

  for (const document of structure.documents) {
    try {
      console.log(
        `Downloading document: ${document.name} (ID: ${document.id})`
      );
      await downloadDocument(document);
    } catch (error) {
      console.error(`Error downloading document ${document.name}:`, error);
    }
  }

  console.log('Finished downloading all documents');
}

/**
 * Download a single document
 */
async function downloadDocument(document) {
  console.log(`Starting download for document: ${document.name}`);

  const config = vscode.workspace.getConfiguration('collaborative-quarto');
  const serverUrl = config.get('serverUrl', 'http://localhost:3000');

  console.log(
    `Fetching document from: ${serverUrl}/api/documents/${document.id}`
  );

  const response = await fetch(`${serverUrl}/api/documents/${document.id}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Cookie: `sessionId=${sessionId}`,
    },
    credentials: 'include',
  });

  console.log(`Document API response status: ${response.status}`);

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`Document API error: ${response.status} - ${errorText}`);
    throw new Error(`Failed to download document: ${response.statusText}`);
  }

  const docData = await response.json();
  console.log('Document data received:', docData);

  // Check if we have document content
  if (!docData.document) {
    throw new Error('No document data in response');
  }

  // Determine file path
  let filePath;
  if (document.folder_path) {
    filePath = path.join(
      syncFolderPath,
      document.folder_path,
      `${document.name}.qmd`
    );
  } else {
    filePath = path.join(syncFolderPath, `${document.name}.qmd`);
  }

  console.log(`Writing file to: ${filePath}`);

  // Ensure directory exists
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) {
    console.log(`Creating directory: ${dir}`);
    fs.mkdirSync(dir, { recursive: true });
  }

  // Write file
  const content = docData.document.content || '';
  console.log(`Writing ${content.length} characters to file`);
  fs.writeFileSync(filePath, content);
  console.log(`Successfully downloaded: ${filePath}`);
}

/**
 * Auto-sync configured project
 */
async function autoSyncProject() {
  if (!currentProjectId || !syncFolderPath) {
    console.log('No project configuration found for auto-sync');
    return;
  }

  try {
    console.log(
      `Auto-syncing project ${currentProjectId} to ${syncFolderPath}`
    );

    // Get project details
    const config = vscode.workspace.getConfiguration('collaborative-quarto');
    const serverUrl = config.get('serverUrl', 'http://localhost:3000');

    const response = await fetch(
      `${serverUrl}/api/projects/${currentProjectId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Cookie: `sessionId=${sessionId}`,
        },
        credentials: 'include',
      }
    );

    if (response.ok) {
      const projectData = await response.json();
      await startProjectSync(projectData);

      const projectName = projectData.project.name;
      statusBarItem.text = `$(check) Collaborative Quarto: Project "${projectName}" synced`;
      vscode.window.showInformationMessage(
        `Project "${projectName}" synced successfully!`
      );

      // Setup file watchers for the sync folder
      setupFileWatchers();
    } else if (response.status === 404) {
      // Project not found - clear the configuration
      console.log('Configured project not found, clearing configuration');
      const config = vscode.workspace.getConfiguration('collaborative-quarto');
      await config.update(
        'currentProjectId',
        undefined,
        vscode.ConfigurationTarget.Workspace
      );
      await config.update(
        'currentProjectName',
        undefined,
        vscode.ConfigurationTarget.Workspace
      );
      currentProjectId = null;
      throw new Error('Configured project no longer exists');
    } else if (response.status === 403) {
      // No access to project
      console.log('No access to configured project');
      throw new Error('No access to configured project');
    } else {
      throw new Error(`Failed to fetch project: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Error auto-syncing project:', error);
    // Only show error for unexpected errors, not access/not found issues
    if (
      !error.message.includes('no longer exists') &&
      !error.message.includes('No access')
    ) {
      vscode.window.showErrorMessage(`Error syncing project: ${error.message}`);
    }
    throw error; // Re-throw so caller can handle
  }
}

/**
 * Setup file watchers for sync folder
 */
function setupFileWatchers() {
  if (!syncFolderPath) return;

  // Watch for changes in the sync folder
  const watcher = vscode.workspace.createFileSystemWatcher(
    new vscode.RelativePattern(syncFolderPath, '**/*.qmd')
  );

  watcher.onDidChange(async (uri) => {
    console.log(`File changed: ${uri.fsPath}`);
    await syncFileToServer(uri.fsPath);
  });

  watcher.onDidCreate(async (uri) => {
    console.log(`File created: ${uri.fsPath}`);
    await syncFileToServer(uri.fsPath);
  });

  watcher.onDidDelete(async (uri) => {
    console.log(`File deleted: ${uri.fsPath}`);
    // Handle file deletion on server
  });

  console.log(`File watcher setup for: ${syncFolderPath}`);
}

/**
 * Sync a local file to the server
 */
async function syncFileToServer(filePath) {
  if (!currentProjectId) return;

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(filePath, '.qmd');

    console.log(`Syncing file to server: ${fileName}`);

    // Find or create document on server
    // This would need to be implemented based on your API
    // For now, just log the sync attempt
    console.log(
      `Would sync: ${fileName} with content length ${content.length}`
    );
  } catch (error) {
    console.error(`Error syncing file ${filePath}:`, error);
  }
}

/**
 * Sync current project manually
 */
async function syncCurrentProject() {
  if (!currentProjectId) {
    vscode.window.showWarningMessage(
      'No project configured. Please run "Setup Project" first.'
    );
    return;
  }

  if (!socket || !socket.connected) {
    vscode.window.showWarningMessage(
      'Not connected to server. Please connect first.'
    );
    return;
  }

  try {
    vscode.window.showInformationMessage('Syncing project...');
    await autoSyncProject();
  } catch (error) {
    vscode.window.showErrorMessage(`Error syncing project: ${error.message}`);
  }
}

/**
 * Load project configuration
 */
async function loadProjectConfig() {
  const config = vscode.workspace.getConfiguration('collaborative-quarto');

  // Try workspace settings first
  currentProjectId = config.get('currentProjectId');
  syncFolderPath = config.get('syncFolderPath');

  // Fall back to global state if workspace settings not available
  if (!currentProjectId && extensionContext) {
    currentProjectId = extensionContext.globalState.get('lastProjectId');
    syncFolderPath = extensionContext.globalState.get('lastSyncFolder');
  }

  if (currentProjectId) {
    console.log(
      `Loaded project configuration: Project ID ${currentProjectId}, Sync folder: ${syncFolderPath}`
    );
  }
}

/**
 * Check for project metadata in workspace folders
 */
async function checkForProjectMetadata() {
  const workspaceFolders = vscode.workspace.workspaceFolders;
  console.log('Checking for project metadata...');
  console.log(
    'Workspace folders:',
    workspaceFolders?.map((f) => f.uri.fsPath)
  );

  if (!workspaceFolders || workspaceFolders.length === 0) {
    console.log('No workspace folders found');
    return;
  }

  // Check each workspace folder for metadata
  for (const folder of workspaceFolders) {
    const metadataPath = path.join(folder.uri.fsPath, '.quarto-collab.json');
    console.log(`Checking for metadata at: ${metadataPath}`);

    if (fs.existsSync(metadataPath)) {
      console.log(`Found project metadata in: ${folder.uri.fsPath}`);

      try {
        const metadataContent = fs.readFileSync(metadataPath, 'utf8');
        const metadata = JSON.parse(metadataContent);

        console.log('Loaded project metadata:', metadata);

        // Update configuration with metadata
        currentProjectId = metadata.project.id;
        syncFolderPath = metadata.sync.folderPath;
        sessionId = metadata.auth.sessionId; // May be expired

        // Update VS Code configuration
        const config = vscode.workspace.getConfiguration(
          'collaborative-quarto'
        );
        await config.update(
          'currentProjectId',
          currentProjectId,
          vscode.ConfigurationTarget.Workspace
        );
        await config.update(
          'syncFolderPath',
          syncFolderPath,
          vscode.ConfigurationTarget.Workspace
        );
        await config.update(
          'serverUrl',
          metadata.server.url,
          vscode.ConfigurationTarget.Workspace
        );

        // Show notification with option to auto-connect
        const choice = await vscode.window.showInformationMessage(
          `Found Quarto collaborative project: "${metadata.project.name}". Would you like to connect?`,
          'Connect Now',
          'Not Now'
        );

        if (choice === 'Connect Now') {
          // Try to connect with stored session
          await connectToServer();
        }

        break; // Only process the first metadata file found
      } catch (error) {
        console.error('Error reading project metadata:', error);
        vscode.window.showWarningMessage(
          'Found project metadata file but could not read it. You may need to setup the project again.'
        );
      }
    }
  }
}

function deactivate() {
  disconnectFromServer();
  if (statusBarItem) {
    statusBarItem.dispose();
  }
  if (trackChangesStatusBarItem) {
    trackChangesStatusBarItem.dispose();
  }
  if (usersStatusBarItem) {
    usersStatusBarItem.dispose();
  }
  stopCursorUpdates();
  clearRemoteCursors();
}

module.exports = {
  activate,
  deactivate,
};
