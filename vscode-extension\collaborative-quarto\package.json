{"name": "collaborative-quarto", "displayName": "Collaborative Quarto", "description": "Real-time collaborative editing for Quarto documents", "version": "0.1.0", "license": "MIT", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "activationEvents": ["onCommand:collaborative-quarto.connect", "onCommand:collaborative-quarto.disconnect", "onLanguage:markdown"], "main": "./extension.js", "contributes": {"commands": [{"command": "collaborative-quarto.connect", "title": "Connect to Collaborative Quarto Server"}, {"command": "collaborative-quarto.disconnect", "title": "Disconnect from Collaborative Quarto Server"}, {"command": "collaborative-quarto.selectDocument", "title": "Select Document to Edit"}, {"command": "collaborative-quarto.createDocument", "title": "Create New Collaborative Document"}, {"command": "collaborative-quarto.toggleTrackChanges", "title": "Toggle Track Changes"}, {"command": "collaborative-quarto.showPendingChanges", "title": "Show Pending Changes"}, {"command": "collaborative-quarto.showVersionHistory", "title": "Show Version History"}, {"command": "collaborative-quarto.setupProject", "title": "Setup Project and Sync Folder"}, {"command": "collaborative-quarto.selectSyncFolder", "title": "Select Sync Folder"}, {"command": "collaborative-quarto.openProjectFolder", "title": "Open Project Folder"}, {"command": "collaborative-quarto.syncProject", "title": "Sync Current Project"}], "configuration": {"title": "Collaborative Quarto", "properties": {"collaborative-quarto.serverUrl": {"type": "string", "default": "http://localhost:3000", "description": "URL of the Collaborative Quarto server"}, "collaborative-quarto.currentProjectId": {"type": "string", "description": "ID of the currently selected project"}, "collaborative-quarto.currentProjectName": {"type": "string", "description": "Name of the currently selected project"}, "collaborative-quarto.syncFolderPath": {"type": "string", "description": "Path to the local folder for syncing project files"}}}}, "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "node ./test/runTest.js"}, "dependencies": {"node-fetch": "^2.6.7", "socket.io-client": "^4.7.5"}, "devDependencies": {"@types/vscode": "^1.60.0", "eslint": "^8.0.0", "glob": "^7.2.0"}}