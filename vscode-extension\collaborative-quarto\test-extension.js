#!/usr/bin/env node

/**
 * Simple test script to validate extension functionality
 */

const fs = require('fs');
const path = require('path');

console.log('Testing Collaborative Quarto Extension...\n');

// Test 1: Check if package.json has correct activation events
console.log('1. Testing activation events...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const activationEvents = packageJson.activationEvents;

const expectedEvents = [
  'onCommand:collaborative-quarto.connect',
  'onCommand:collaborative-quarto.disconnect', 
  'onLanguage:markdown',
  'workspaceContains:**/.quarto-collab.json',
  'onOpenTextDocument:.quarto-collab.json'
];

let allEventsPresent = true;
expectedEvents.forEach(event => {
  if (!activationEvents.includes(event)) {
    console.log(`❌ Missing activation event: ${event}`);
    allEventsPresent = false;
  }
});

if (allEventsPresent) {
  console.log('✅ All activation events present');
}

// Test 2: Check if new configuration options are present
console.log('\n2. Testing configuration options...');
const config = packageJson.contributes.configuration.properties;

const expectedConfigs = [
  'collaborative-quarto.showRemoteCursors',
  'collaborative-quarto.autoConnect'
];

let allConfigsPresent = true;
expectedConfigs.forEach(configKey => {
  if (!config[configKey]) {
    console.log(`❌ Missing configuration: ${configKey}`);
    allConfigsPresent = false;
  } else {
    console.log(`✅ Configuration present: ${configKey} (default: ${config[configKey].default})`);
  }
});

// Test 3: Check if extension.js has the new functions
console.log('\n3. Testing extension functions...');
const extensionCode = fs.readFileSync('extension.js', 'utf8');

const expectedFunctions = [
  'setupMetadataFileWatcher',
  'handleMetadataFileOpened',
  'setupConfigurationWatcher'
];

expectedFunctions.forEach(funcName => {
  if (extensionCode.includes(`function ${funcName}`)) {
    console.log(`✅ Function present: ${funcName}`);
  } else {
    console.log(`❌ Missing function: ${funcName}`);
  }
});

// Test 4: Check if cursor setting is respected
console.log('\n4. Testing cursor setting integration...');
if (extensionCode.includes('showRemoteCursors') && extensionCode.includes('config.get(\'showRemoteCursors\'')) {
  console.log('✅ Cursor setting integration present');
} else {
  console.log('❌ Cursor setting integration missing');
}

// Test 5: Check if auto-connect setting is respected
console.log('\n5. Testing auto-connect setting integration...');
if (extensionCode.includes('autoConnect') && extensionCode.includes('config.get(\'autoConnect\'')) {
  console.log('✅ Auto-connect setting integration present');
} else {
  console.log('❌ Auto-connect setting integration missing');
}

console.log('\n✅ Extension validation complete!');
console.log('\nTo install and test the extension:');
console.log('1. Run: npm install');
console.log('2. Run: npx vsce package');
console.log('3. Run: code --install-extension collaborative-quarto-*.vsix');
